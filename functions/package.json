{"name": "functions", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "serve": "npm run build && firebase emulators:start --only functions", "shell": "npm run build && firebase functions:shell", "start": "npm run shell", "deploy": "firebase deploy --only functions", "logs": "firebase functions:log"}, "engines": {"node": "18"}, "main": "lib/index.js", "dependencies": {"@sparticuz/chromium": "^121.0.0", "@types/cors": "^2.8.19", "cors": "^2.8.5", "firebase-admin": "^12.6.0", "firebase-functions": "^5.1.1", "pdf-lib": "^1.17.1", "puppeteer-core": "^21.11.0", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@types/node": "^18.0.0", "@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "firebase-functions-test": "^3.1.0", "typescript": "^5.0.0"}, "private": true}
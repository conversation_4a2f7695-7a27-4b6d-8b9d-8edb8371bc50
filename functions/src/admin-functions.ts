import { onCall, onRequest } from 'firebase-functions/v2/https';
import { logger } from 'firebase-functions';
import * as admin from 'firebase-admin';
import { requireAppCheck } from './middleware/app-check';
import cors from 'cors';

// CORS configuration for HTTP endpoints
const corsHandler = cors({
  origin: [
    'https://auburnengineering.com',
    'https://auburn-engineering.web.app',
    'https://auburn-engineering.firebaseapp.com',
    /^https:\/\/.*\.web\.app$/,
    /^https:\/\/.*\.firebaseapp\.com$/,
    ...(process.env.NODE_ENV === 'development' ? ['http://localhost:3000', 'http://127.0.0.1:3000'] : [])
  ],
  methods: ['GET', 'POST', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: false
});

/**
 * Helper function to verify authentication token and check admin privileges
 */
async function verifyAdminAuth(authHeader: string | undefined) {
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    throw new Error('Authentication required');
  }

  const token = authHeader.replace('Bearer ', '');
  
  try {
    // Verify the token using Firebase Admin SDK
    const decodedToken = await admin.auth().verifyIdToken(token);
    const user = {
      uid: decodedToken.uid,
      email: decodedToken.email
    };

    // Get user profile to check admin status
    const userDocRef = admin.firestore().collection('users').doc(user.uid);
    const userDoc = await userDocRef.get();
    
    if (!userDoc.exists) {
      throw new Error('User profile not found');
    }

    const userData = userDoc.data();
    if (!userData || userData.role !== 'admin') {
      throw new Error('Admin privileges required');
    }

    return { user, userData };
  } catch (error) {
    throw new Error(`Authentication failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Cloud Function to queue bulk exports
 * Replaces POST /api/admin/queue-export Next.js route
 */
export const queueExport = onCall({
  region: 'asia-east1',
  memory: '1GiB',
  timeoutSeconds: 300
}, async (request) => {
  // Validate App Check token
  await requireAppCheck('queueExport')(request);
  
  const { auth, data } = request;
  
  if (!auth) {
    throw new Error('Authentication required');
  }

  try {
    const { checklistIds, type = 'pdf' } = data;

    // Basic validation
    if (!checklistIds || !Array.isArray(checklistIds) || checklistIds.length === 0) {
      throw new Error('Checklist IDs are required');
    }

    if (!['pdf', 'excel'].includes(type)) {
      throw new Error('Invalid export type. Must be "pdf" or "excel"');
    }

    if (checklistIds.length > 500) {
      throw new Error('Maximum 500 checklists allowed per export');
    }

    // Check admin privileges
    const userDocRef = admin.firestore().collection('users').doc(auth.uid);
    const userDoc = await userDocRef.get();
    
    if (!userDoc.exists) {
      throw new Error('User profile not found');
    }

    const userData = userDoc.data();
    if (!userData || userData.role !== 'admin') {
      throw new Error('Admin privileges required');
    }

    // Get checklists for metadata
    const checklistsRef = admin.firestore().collection('checklists');
    const checklistPromises = checklistIds.map((id: string) => checklistsRef.doc(id).get());
    const checklistDocs = await Promise.all(checklistPromises);
    
    const validChecklists = checklistDocs
      .filter(doc => doc.exists)
      .map(doc => ({
        id: doc.id,
        ...doc.data(),
        generalInfo: doc.data()?.generalInfo || {}
      }));
    
    if (validChecklists.length === 0) {
      throw new Error('No valid checklists found');
    }

    // Prepare metadata
    const clients = Array.from(new Set(validChecklists.map(c => c.generalInfo.clientName).filter(Boolean)));
    const dates = validChecklists
      .map(c => c.generalInfo.date)
      .filter(Boolean)
      .map(d => new Date(d))
      .sort((a, b) => a.getTime() - b.getTime());
    
    const metadata = {
      checklistCount: validChecklists.length,
      clientName: clients.length === 1 ? clients[0] : `${clients.length} clients`,
      dateRange: dates.length > 1 
        ? `${dates[0].toLocaleDateString()} - ${dates[dates.length - 1].toLocaleDateString()}`
        : (dates[0] ? dates[0].toLocaleDateString() : new Date().toLocaleDateString()),
      requestedBy: userData.displayName || userData.email || auth.token?.email
    };

    // Create queue item
    const queueItem = {
      userId: auth.uid,
      type,
      status: 'pending',
      checklistIds,
      checklistCount: validChecklists.length,
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      progress: {
        currentIndex: 0,
        totalCount: validChecklists.length,
        percentage: 0,
        stage: 'queued'
      },
      metadata: {
        ...metadata,
        exportMethod: 'cloud-function'
      }
    };

    // Add to export-queue collection
    const queueRef = await admin.firestore().collection('export-queue').add(queueItem);
    const queueId = queueRef.id;
    
    logger.info('Export queued successfully', {
      queueId,
      checklistCount: validChecklists.length,
      type,
      userId: auth.uid
    });

    return {
      success: true,
      data: {
        queueId,
        checklistCount: validChecklists.length,
        type,
        message: 'Export queued successfully'
      }
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    logger.error('Failed to queue export', {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
      userId: auth.uid
    });

    throw new Error(`Failed to queue export: ${errorMessage}`);
  }
});

/**
 * Cloud Function to get export queue statistics
 * Replaces GET /api/admin/queue-export Next.js route
 */
export const getExportQueueStats = onCall({
  region: 'asia-east1',
  memory: '512MiB',
  timeoutSeconds: 60
}, async (request) => {
  // Validate App Check token
  await requireAppCheck('getExportQueueStats')(request);
  
  const { auth } = request;
  
  if (!auth) {
    throw new Error('Authentication required');
  }

  try {
    // Check admin privileges
    const userDocRef = admin.firestore().collection('users').doc(auth.uid);
    const userDoc = await userDocRef.get();
    
    if (!userDoc.exists) {
      throw new Error('User profile not found');
    }

    const userData = userDoc.data();
    if (!userData || userData.role !== 'admin') {
      throw new Error('Admin privileges required');
    }

    // Get queue statistics
    const queueRef = admin.firestore().collection('export-queue').where('userId', '==', auth.uid);
    const snapshot = await queueRef.get();
    
    const stats = {
      pending: 0,
      processing: 0,
      completed: 0,
      failed: 0,
      total: snapshot.size
    };

    snapshot.docs.forEach(doc => {
      const data = doc.data();
      if (data.status && stats.hasOwnProperty(data.status)) {
        stats[data.status as keyof typeof stats]++;
      }
    });
    
    return {
      success: true,
      data: stats
    };

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    logger.error('Failed to get queue stats', {
      error: errorMessage,
      userId: auth.uid
    });

    throw new Error(`Failed to get queue statistics: ${errorMessage}`);
  }
});

/**
 * HTTP endpoint version of queue export for REST API compatibility
 * This provides backward compatibility during migration
 */
export const queueExportHttp = onRequest({
  region: 'asia-east1',
  memory: '1GiB',
  timeoutSeconds: 300,
  maxInstances: 50
}, async (req, res) => {
  return new Promise<void>((resolve) => {
    corsHandler(req, res, async () => {
      try {
        if (req.method === 'POST') {
          // Handle POST request for queueing export
          const authHeader = req.get('authorization');
          const { user, userData } = await verifyAdminAuth(authHeader);
          
          const { checklistIds, type = 'pdf' } = req.body;

          // Validation
          if (!checklistIds || !Array.isArray(checklistIds) || checklistIds.length === 0) {
            res.status(400).json({ error: 'Checklist IDs are required' });
            resolve();
            return;
          }

          if (!['pdf', 'excel'].includes(type)) {
            res.status(400).json({ error: 'Invalid export type. Must be "pdf" or "excel"' });
            resolve();
            return;
          }

          if (checklistIds.length > 500) {
            res.status(400).json({ error: 'Maximum 500 checklists allowed per export' });
            resolve();
            return;
          }

          // Get checklists for metadata
          const checklistsRef = admin.firestore().collection('checklists');
          const checklistPromises = checklistIds.map((id: string) => checklistsRef.doc(id).get());
          const checklistDocs = await Promise.all(checklistPromises);
          
          const validChecklists = checklistDocs
            .filter(doc => doc.exists)
            .map(doc => ({
              id: doc.id,
              ...doc.data(),
              generalInfo: doc.data()?.generalInfo || {}
            }));
          
          if (validChecklists.length === 0) {
            res.status(400).json({ error: 'No valid checklists found' });
            resolve();
            return;
          }

          // Prepare metadata
          const clients = Array.from(new Set(validChecklists.map(c => c.generalInfo.clientName).filter(Boolean)));
          const dates = validChecklists
            .map(c => c.generalInfo.date)
            .filter(Boolean)
            .map(d => new Date(d))
            .sort((a, b) => a.getTime() - b.getTime());
          
          const metadata = {
            checklistCount: validChecklists.length,
            clientName: clients.length === 1 ? clients[0] : `${clients.length} clients`,
            dateRange: dates.length > 1 
              ? `${dates[0].toLocaleDateString()} - ${dates[dates.length - 1].toLocaleDateString()}`
              : (dates[0] ? dates[0].toLocaleDateString() : new Date().toLocaleDateString()),
            requestedBy: userData.displayName || userData.email || user.email
          };

          // Create queue item
          const queueItem = {
            userId: user.uid,
            type,
            status: 'pending',
            checklistIds,
            checklistCount: validChecklists.length,
            createdAt: admin.firestore.FieldValue.serverTimestamp(),
            progress: {
              currentIndex: 0,
              totalCount: validChecklists.length,
              percentage: 0,
              stage: 'queued'
            },
            metadata: {
              ...metadata,
              exportMethod: 'cloud-function'
            }
          };

          // Add to export-queue collection
          const queueRef = await admin.firestore().collection('export-queue').add(queueItem);
          const queueId = queueRef.id;
          
          logger.info('Export queued successfully via HTTP', {
            queueId,
            checklistCount: validChecklists.length,
            type,
            userId: user.uid
          });

          res.status(200).json({
            success: true,
            data: {
              queueId,
              checklistCount: validChecklists.length,
              type,
              message: 'Export queued successfully'
            }
          });

        } else if (req.method === 'GET') {
          // Handle GET request for queue stats
          const authHeader = req.get('authorization');
          const { user } = await verifyAdminAuth(authHeader);

          // Get queue statistics
          const queueRef = admin.firestore().collection('export-queue').where('userId', '==', user.uid);
          const snapshot = await queueRef.get();
          
          const stats = {
            pending: 0,
            processing: 0,
            completed: 0,
            failed: 0,
            total: snapshot.size
          };

          snapshot.docs.forEach(doc => {
            const data = doc.data();
            if (data.status && stats.hasOwnProperty(data.status)) {
              stats[data.status as keyof typeof stats]++;
            }
          });
          
          res.status(200).json({
            success: true,
            data: stats
          });

        } else {
          res.status(405).json({ error: 'Method not allowed' });
        }

        resolve();

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
        
        logger.error('HTTP queue export error', {
          error: errorMessage,
          method: req.method,
          stack: error instanceof Error ? error.stack : undefined
        });

        if (errorMessage.includes('Authentication') || errorMessage.includes('Admin privileges')) {
          res.status(errorMessage.includes('Authentication required') ? 401 : 403);
        } else {
          res.status(500);
        }
        
        res.json({ 
          error: errorMessage,
          details: process.env.NODE_ENV === 'development' ? errorMessage : undefined
        });
        resolve();
      }
    });
  });
}); 
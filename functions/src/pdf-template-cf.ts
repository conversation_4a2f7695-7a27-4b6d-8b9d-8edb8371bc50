interface ChecklistData {
  id: string;
  generalInfo: {
    clientName: string;
    building: string;
    equipmentName: string;
    location: string;
    tagNo: string;
    date: string;
    ppmAttempt: string;
    inspectedBy: string;
    approvedBy: string;
  };
  mechanicalChecks: Record<string, any>;
  electricalChecks: Record<string, any>;
  sequenceControlsChecks: Record<string, any>;
  remarks?: string;
  beforeImage?: string;
  afterImage?: string;
  signature?: string;
  isCompleted: boolean;
}

interface ChecklistFieldConfig {
  key: string;
  label: string;
  type: 'status' | 'number';
  unit?: string;
  section: 'mechanical' | 'electrical' | 'sequence';
}

interface PDFTemplateProps {
  checklist: ChecklistData;
  logoDataUrl?: string;
  beforeImageDataUrl?: string;
  afterImageDataUrl?: string;
  signatureDataUrl?: string;
}

// Field configuration - matches client-side config
const MECHANICAL_CHECKS: ChecklistFieldConfig[] = [
  { key: 'airflowVelocity', label: 'Airflow in M/S - Face Velocity', type: 'number', unit: 'M/S', section: 'mechanical' },
  { key: 'beltWearPulleyAlignment', label: 'Belt wear & Pulley alignment', type: 'status', section: 'mechanical' },
  { key: 'bladeImpellerDamage', label: 'Blade / Impeller damage crack', type: 'status', section: 'mechanical' },
  { key: 'boltSetScrewTightness', label: 'Bolt & Set Screw Tightness', type: 'status', section: 'mechanical' },
  { key: 'bladeTipClearance', label: 'Check Blade tip clearance', type: 'status', section: 'mechanical' },
  { key: 'excessiveVibration', label: 'Check Excessive Vibration', type: 'status', section: 'mechanical' },
  { key: 'fanGuardProtection', label: 'Check Fan Guard protection', type: 'status', section: 'mechanical' },
  { key: 'fanPowerOff', label: 'Check Fan Power Off', type: 'status', section: 'mechanical' },
  { key: 'motorOverheating', label: 'Check Motor Overheating', type: 'status', section: 'mechanical' },
  { key: 'rotationDirection', label: 'Check Rotation Clockwise/Anti', type: 'status', section: 'mechanical' },
  { key: 'cleanBladesHousing', label: 'Clean Blades & Housing', type: 'status', section: 'mechanical' },
  { key: 'dustDebrisRemoval', label: 'Dust & Debris removal', type: 'status', section: 'mechanical' },
  { key: 'erraticOperation', label: 'Erratic Operation / Malfunctioning', type: 'status', section: 'mechanical' },
  { key: 'inletVanesOperation', label: 'Inlet Vanes Operation freedom', type: 'status', section: 'mechanical' },
  { key: 'bearingLubrication', label: 'Lubrication on Bearings', type: 'status', section: 'mechanical' },
  { key: 'noObstructionsBackflow', label: 'No obstructions / Backflow', type: 'status', section: 'mechanical' },
  { key: 'physicalDamageStability', label: 'Physical Damage stability of fan', type: 'status', section: 'mechanical' },
  { key: 'speedRpm', label: 'Speed in Rotation per Minute', type: 'number', unit: 'RPM', section: 'mechanical' },
  { key: 'springMountVibrationIsolator', label: 'Spring Mount / Vibration Isolator', type: 'status', section: 'mechanical' },
  { key: 'unusualSoundDecibel', label: 'Unusual Sound in Decibel', type: 'number', unit: 'dB', section: 'mechanical' }
];

const ELECTRICAL_CHECKS: ChecklistFieldConfig[] = [
  { key: 'bmsControlsInterlocks', label: 'BMS Controls & Interlocks', type: 'status', section: 'electrical' },
  { key: 'burntMarksDiscolorMelted', label: 'Burnt Marks, Discolor, Melted', type: 'status', section: 'electrical' },
  { key: 'circuitBreakerFunctional', label: 'Check Circuit Breaker Functional', type: 'status', section: 'electrical' },
  { key: 'contractorsBreakers', label: 'Check Contractors & Breakers', type: 'status', section: 'electrical' },
  { key: 'fireAlarmConnected', label: 'Check Fire Alarm Connected', type: 'status', section: 'electrical' },
  { key: 'fuseTerminals', label: 'Check Fuse & Terminals', type: 'status', section: 'electrical' },
  { key: 'mccPowerOffBreaker', label: 'Check MCC Power Off - Breaker', type: 'status', section: 'electrical' },
  { key: 'signsLiquidLeaks', label: 'Check Signs of liquid Leaks', type: 'status', section: 'electrical' },
  { key: 'tripSettingsFunction', label: 'Check Trip settings & Function', type: 'status', section: 'electrical' },
  { key: 'controlRelaysOperations', label: 'Control Relays operations', type: 'status', section: 'electrical' },
  { key: 'currentAmps', label: 'Current in Amps', type: 'number', unit: 'A', section: 'electrical' },
  { key: 'doorsCoversCloseProperly', label: 'Doors & Covers close properly', type: 'status', section: 'electrical' },
  { key: 'frayingExposedWires', label: 'Fraying / Exposed Wires', type: 'status', section: 'electrical' },
  { key: 'highLowSpeedVerification', label: 'High / Low Speed Verification', type: 'status', section: 'electrical' },
  { key: 'indicationsOnOffTrip', label: 'Indications [ON] [OFF] & [TRIP]', type: 'status', section: 'electrical' },
  { key: 'looseWiresToBeTightened', label: 'Loose wires to be tightened', type: 'status', section: 'electrical' },
  { key: 'motorPowerKw', label: 'Motor Power in KW', type: 'number', unit: 'KW', section: 'electrical' },
  { key: 'potentialVoltage', label: 'Potential Voltage', type: 'number', unit: 'V', section: 'electrical' },
  { key: 'selectorHandStopAuto', label: 'Selector [HAND] [STOP] [AUTO]', type: 'status', section: 'electrical' },
  { key: 'testEmergencyStopButton', label: 'Test Emergency Stop Button', type: 'status', section: 'electrical' }
];

const SEQUENCE_CONTROLS_CHECKS: ChecklistFieldConfig[] = [
  { key: 'dptDifferentialPressureTransmitter', label: 'DPT - Differential Pressure Transmitter', type: 'status', section: 'sequence' },
  { key: 'erraticOperationMalfunctioning', label: 'Erratic Operation / Malfunctioning', type: 'status', section: 'sequence' },
  { key: 'indicationsOnOffTrip', label: 'Indications [ON] [OFF] & [TRIP]', type: 'status', section: 'sequence' },
  { key: 'mccOffOverrideFunction', label: 'MCC Off - Override Function', type: 'status', section: 'sequence' },
  { key: 'msfdDamperFunctional', label: 'MSFD - Damper Functional', type: 'status', section: 'sequence' },
  { key: 'offWithDuctDetectorActivation', label: 'Off with Duct Detector activation', type: 'status', section: 'sequence' },
  { key: 'overrideFscsPanelStatus', label: 'Override (FSCS) Panel Status', type: 'status', section: 'sequence' },
  { key: 'sameTagNameInMccFan', label: 'Same Tag Name in MCC & Fan', type: 'status', section: 'sequence' },
  { key: 'selectorRunStopAuto', label: 'Selector [RUN] [STOP] [AUTO]', type: 'status', section: 'sequence' },
  { key: 'vfdVariableFrequencyDrive', label: 'VFD - Variable Frequency Drive', type: 'status', section: 'sequence' }
];

const ALL_CHECKS = [
  ...MECHANICAL_CHECKS,
  ...ELECTRICAL_CHECKS,
  ...SEQUENCE_CONTROLS_CHECKS
];



// Get display value for any field value, handling empty states consistently
const getDisplayValue = (value: any, includeEmpty: boolean = true): string => {
  if (value === undefined || value === null || String(value).trim() === '') {
    return includeEmpty ? 'N/A' : '';
  }
  
  if (typeof value === 'string' && (value === 'OK' || value === 'Faulty' || value === 'N/A' || value === 'Missing')) {
    return value;
  }
  
  return String(value);
};

// Get sorted fields with proper ordering and empty field handling
const getSectionFieldsWithValues = (
  data: Record<string, any>,
  section: 'mechanical' | 'electrical' | 'sequence',
  includeEmpty: boolean = true
): Array<{ key: string; value: any; label: string }> => {
  // Get all configured fields for this section in proper order
  const sectionFields = ALL_CHECKS.filter(field => field.section === section);
  
  return sectionFields.map(fieldConfig => {
    const value = data[fieldConfig.key];
    const processedValue = getDisplayValue(value, includeEmpty);
    
    return {
      key: fieldConfig.key,
      value: processedValue,
      label: fieldConfig.label
    };
  }).filter(item => includeEmpty || (item.value !== 'N/A' && item.value !== ''));
};

export function generatePDFHTML(props: PDFTemplateProps): string {
  const { checklist, logoDataUrl, beforeImageDataUrl, afterImageDataUrl, signatureDataUrl } = props;

  const calculateStats = () => {
    const allItems = [
      ...Object.entries(checklist.mechanicalChecks),
      ...Object.entries(checklist.electricalChecks),
      ...Object.entries(checklist.sequenceControlsChecks)
    ];

    const stats = { OK: 0, Faulty: 0, 'N/A': 0, Missing: 0, total: 0 };
    
    allItems.forEach(([, value]) => {
      if (typeof value === 'string' && (value === 'OK' || value === 'Faulty' || value === 'N/A' || value === 'Missing')) {
        stats[value as keyof typeof stats]++;
        stats.total++;
      }
    });
    
    return stats;
  };

  const stats = calculateStats();
  const okPercentage = stats.total > 0 ? Math.round((stats.OK / stats.total) * 100) : 0;

  const renderChecklistSection = (title: string, data: Record<string, any>, section: 'mechanical' | 'electrical' | 'sequence') => {
    // Get all fields with values, including empty fields as "N/A"
    const items = getSectionFieldsWithValues(data, section, true);

    const getStatusColor = (value: string | number | boolean) => {
      if (typeof value === 'string') {
        switch (value) {
          case 'OK': return '#28a745';
          case 'Faulty': return '#dc3545';
          case 'N/A': return '#856404';
          case 'Missing': return '#6c757d';
        }
      }
      return '#0066cc';
    };

    const itemsHTML = items.map((item) => {
      const displayValue = item.value;
      const statusColor = getStatusColor(item.value);
      
      return `
        <div style="display: flex; align-items: center; justify-content: space-between; padding: 5px 0; font-size: 10.5px; border-bottom: 1px solid #e9ecef; min-height: 24px;">
          <span style="flex: 1; line-height: 1.3; word-wrap: break-word; color: #212529; font-weight: 500; margin-right: 8px;">
            ${item.label}
          </span>
          <span style="color: ${statusColor}; font-weight: 700; font-size: 10px; flex-shrink: 0;">
            ${displayValue}
          </span>
        </div>
      `;
    }).join('');

    return `
      <div style="background-color: white; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden;">
        <div style="background-color: #CC0000; color: white; padding: 10px 12px; font-size: 13px; font-weight: 700; text-align: center; letter-spacing: 0.2px; border-radius: 6px 6px 0 0;">
          ${title}
        </div>
        <div style="padding: 8px; max-height: 680px; overflow-y: auto; background-color: white;">
          ${itemsHTML}
        </div>
      </div>
    `;
  };

  // Create pie chart SVG
  const createPieChartSVG = () => {
    if (stats.total === 0) {
      return `
        <div style="margin: 0 auto 8px; width: 80px; height: 80px; display: flex; justify-content: center;">
          <div style="width: 80px; height: 80px; border-radius: 50%; background-color: #e9ecef; display: flex; align-items: center; justify-content: center; font-size: 10px; color: #6c757d;">
            No Data
          </div>
        </div>
      `;
    }

    const pieData = [];
    if (stats.OK > 0) pieData.push({ label: 'OK', value: stats.OK, color: '#28a745' });
    if (stats.Faulty > 0) pieData.push({ label: 'Faulty', value: stats.Faulty, color: '#dc3545' });
    if (stats['N/A'] > 0) pieData.push({ label: 'N/A', value: stats['N/A'], color: '#ffc107' });
    if (stats.Missing > 0) pieData.push({ label: 'Missing', value: stats.Missing, color: '#6c757d' });

    if (pieData.length === 1) {
      return `
        <div style="margin: 0 auto 8px; width: 80px; height: 80px; display: flex; justify-content: center;">
          <svg width="80" height="80" viewBox="0 0 80 80">
            <circle cx="40" cy="40" r="35" fill="${pieData[0].color}" stroke="#fff" stroke-width="2"/>
            <circle cx="40" cy="40" r="18" fill="white" stroke="#e9ecef" stroke-width="1"/>
            <text x="40" y="38" text-anchor="middle" font-size="12" font-weight="bold" fill="#000">100%</text>
            <text x="40" y="48" text-anchor="middle" font-size="7" fill="#666">${pieData[0].label}</text>
          </svg>
        </div>
      `;
    }

    // Multi-segment pie chart
    const radius = 35;
    const centerX = 40;
    const centerY = 40;
    let currentAngle = -Math.PI / 2;

    const slices = pieData.map(item => {
      const sliceAngle = (item.value / stats.total) * 2 * Math.PI;
      const startAngle = currentAngle;
      const endAngle = currentAngle + sliceAngle;
      
      const x1 = centerX + radius * Math.cos(startAngle);
      const y1 = centerY + radius * Math.sin(startAngle);
      const x2 = centerX + radius * Math.cos(endAngle);
      const y2 = centerY + radius * Math.sin(endAngle);
      
      const largeArcFlag = sliceAngle > Math.PI ? 1 : 0;
      
      const pathData = [
        `M ${centerX} ${centerY}`,
        `L ${x1} ${y1}`,
        `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
        'Z'
      ].join(' ');
      
      currentAngle = endAngle;
      
      return `<path d="${pathData}" fill="${item.color}" stroke="#fff" stroke-width="2"/>`;
    }).join('');

    return `
      <div style="margin: 0 auto 8px; width: 80px; height: 80px; display: flex; justify-content: center;">
        <svg width="80" height="80" viewBox="0 0 80 80">
          ${slices}
          <circle cx="${centerX}" cy="${centerY}" r="18" fill="white" stroke="#e9ecef" stroke-width="1"/>
          <text x="${centerX}" y="${centerY - 3}" text-anchor="middle" font-size="12" font-weight="bold" fill="#000">${okPercentage}%</text>
          <text x="${centerX}" y="${centerY + 8}" text-anchor="middle" font-size="7" fill="#666">OK</text>
        </svg>
      </div>
    `;
  };

  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <title>PPM Checklist Report</title>
        <style>
          @page {
            size: A4;
            margin: 0 12mm 12mm 12mm;
          }
          * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
            box-sizing: border-box;
          }
          body {
            margin: 0;
            padding: 0;
            font-family: Arial, "Helvetica Neue", Helvetica, sans-serif;
            background: white;
          }
        </style>
      </head>
      <body>
        <div style="font-family: Arial, 'Helvetica Neue', Helvetica, sans-serif; font-size: 10px; line-height: 1.3; color: #212529; background-color: #fff; width: 210mm; height: 297mm; margin: 0 auto; padding: 5mm 10mm 10mm 10mm; box-sizing: border-box; display: flex; flex-direction: column;">
          
          <!-- Header -->
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; padding: 8px 12px; border-bottom: 3px solid #CC0000; background-color: #ffffff; border-radius: 4px 4px 0 0;">
            <div>
              <h1 style="font-size: 18px; font-weight: 700; color: #CC0000; margin: 0 0 3px 0; letter-spacing: -0.2px;">
                PPM Ventilation System Checklist
              </h1>
              <p style="font-size: 11px; color: #666666; margin: 0; font-weight: 600;">
                Preventive Maintenance Inspection Report
              </p>
            </div>
                          <div style="width: 200px; height: 90px; display: flex; align-items: center; justify-content: center; overflow: hidden; border-radius: 6px; background-color: transparent;">
              ${logoDataUrl ? `<img src="${logoDataUrl}" alt="Auburn Engineering" style="max-width: 100%; max-height: 100%; object-fit: contain; border: none; background: transparent;">` : '<div style="font-size: 10px; color: #6c757d;">Logo</div>'}
            </div>
          </div>

          <!-- Information Grid -->
          <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 10px; margin-bottom: 12px; font-size: 11px;">
            <div style="background-color: #ffffff; padding: 10px; border-radius: 6px; border: 1px solid #e9ecef;">
              <div style="margin-bottom: 4px; display: flex;">
                <span style="font-weight: 700; color: #495057; min-width: 55px;">Client:</span>
                <span style="color: #212529; font-weight: 600;">${checklist.generalInfo.clientName}</span>
              </div>
              <div style="margin-bottom: 4px; display: flex;">
                <span style="font-weight: 700; color: #495057; min-width: 55px;">Building:</span>
                <span style="color: #212529; font-weight: 600;">${checklist.generalInfo.building}</span>
              </div>
              <div style="display: flex;">
                <span style="font-weight: 700; color: #495057; min-width: 55px;">Inspector:</span>
                <span style="color: #212529; font-weight: 600;">${checklist.generalInfo.inspectedBy}</span>
              </div>
            </div>
            <div style="background-color: #ffffff; padding: 10px; border-radius: 6px; border: 1px solid #e9ecef;">
              <div style="margin-bottom: 4px; display: flex;">
                <span style="font-weight: 700; color: #495057; min-width: 55px;">Equipment:</span>
                <span style="color: #212529; font-weight: 600;">${checklist.generalInfo.equipmentName}</span>
              </div>
              <div style="margin-bottom: 4px; display: flex;">
                <span style="font-weight: 700; color: #495057; min-width: 55px;">Location:</span>
                <span style="color: #212529; font-weight: 600;">${checklist.generalInfo.location}</span>
              </div>
              <div style="display: flex;">
                <span style="font-weight: 700; color: #495057; min-width: 55px;">Tag No:</span>
                <span style="color: #212529; font-weight: 600;">${checklist.generalInfo.tagNo}</span>
              </div>
            </div>
            <div style="background-color: #ffffff; padding: 10px; border-radius: 6px; border: 1px solid #e9ecef;">
              <div style="margin-bottom: 4px; display: flex;">
                <span style="font-weight: 700; color: #495057; min-width: 55px;">Date:</span>
                <span style="color: #212529; font-weight: 600;">${checklist.generalInfo.date}</span>
              </div>
              <div style="margin-bottom: 4px; display: flex;">
                <span style="font-weight: 700; color: #495057; min-width: 55px;">PPM:</span>
                <span style="color: #212529; font-weight: 600;">${checklist.generalInfo.ppmAttempt}</span>
              </div>
              <div style="display: flex;">
                <span style="font-weight: 700; color: #495057; min-width: 55px;">Approved:</span>
                <span style="color: #212529; font-weight: 600;">${checklist.generalInfo.approvedBy}</span>
              </div>
            </div>
          </div>

          <!-- Main Content Grid -->
          <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; margin-bottom: 12px;">
            ${renderChecklistSection('Mechanical List', checklist.mechanicalChecks, 'mechanical')}
            ${renderChecklistSection('Electrical List', checklist.electricalChecks, 'electrical')}
            
            <div style="display: flex; flex-direction: column; gap: 10px;">
              ${renderChecklistSection('Sequence/Controls List', checklist.sequenceControlsChecks, 'sequence')}
              
              <!-- Status Chart -->
              <div style="background-color: white; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden;">
                <div style="background-color: #CC0000; color: white; padding: 10px 12px; font-size: 12px; font-weight: 700; text-align: center; letter-spacing: 0.2px; border-radius: 6px 6px 0 0;">
                  Status Overview
                </div>
                <div style="padding: 12px; display: flex; flex-direction: column; align-items: center; gap: 8px;">
                  <div style="text-align: center;">
                    ${createPieChartSVG()}
                    <div style="font-size: 7px; color: #6c757d; margin-top: 4px;">Overall Status</div>
                  </div>
                  
                  <div style="display: flex; flex-direction: column; gap: 4px; width: 100%;">
                    <div style="display: flex; align-items: center; font-size: 10px;">
                      <div style="width: 12px; height: 12px; background-color: #28a745; margin-right: 6px; border-radius: 2px;"></div>
                      <span style="flex: 1; font-weight: 600;">Passed:</span>
                      <span style="font-weight: 700; color: #28a745;">${stats.OK}</span>
                    </div>
                    <div style="display: flex; align-items: center; font-size: 10px;">
                      <div style="width: 12px; height: 12px; background-color: #dc3545; margin-right: 6px; border-radius: 2px;"></div>
                      <span style="flex: 1; font-weight: 600;">Failed:</span>
                      <span style="font-weight: 700; color: #dc3545;">${stats.Faulty}</span>
                    </div>
                    <div style="display: flex; align-items: center; font-size: 10px;">
                      <div style="width: 12px; height: 12px; background-color: #ffc107; margin-right: 6px; border-radius: 2px;"></div>
                      <span style="flex: 1; font-weight: 600;">N/A:</span>
                      <span style="font-weight: 700; color: #856404;">${stats['N/A']}</span>
                    </div>
                    <div style="display: flex; align-items: center; font-size: 10px;">
                      <div style="width: 12px; height: 12px; background-color: #6c757d; margin-right: 6px; border-radius: 2px;"></div>
                      <span style="flex: 1; font-weight: 600;">Missing:</span>
                      <span style="font-weight: 700; color: #6c757d;">${stats.Missing}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Images Section -->
          <div style="margin-bottom: 8px;">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px;">
              <div>
                <h4 style="font-size: 12px; font-weight: 700; margin: 0 0 6px 0; color: #CC0000; text-align: center;">Before Image</h4>
                <div style="border: 1px solid #e9ecef; border-radius: 6px; height: 198px; background-color: #ffffff; display: flex; align-items: center; justify-content: center; font-size: 10px; color: #6c757d; overflow: hidden;">
                  ${beforeImageDataUrl ? `<img src="${beforeImageDataUrl}" alt="Before" style="width: 100%; height: 100%; object-fit: cover;">` : 'No image available'}
                </div>
              </div>
              <div>
                <h4 style="font-size: 12px; font-weight: 700; margin: 0 0 6px 0; color: #CC0000; text-align: center;">After Image</h4>
                <div style="border: 1px solid #e9ecef; border-radius: 6px; height: 198px; background-color: #ffffff; display: flex; align-items: center; justify-content: center; font-size: 10px; color: #6c757d; overflow: hidden;">
                  ${afterImageDataUrl ? `<img src="${afterImageDataUrl}" alt="After" style="width: 100%; height: 100%; object-fit: cover;">` : 'No image available'}
                </div>
              </div>
            </div>
          </div>

          <!-- Footer Section -->
          <div style="display: grid; grid-template-columns: 2fr 1fr; gap: 10px; margin-top: auto;">
            <div style="padding: 8px 0;">
              <h4 style="font-size: 12px; font-weight: 700; margin: 0 0 6px 0; color: #CC0000;">Remarks</h4>
              <div style="font-size: 11px; color: #495057; line-height: 1.4; min-height: 35px;">
                ${checklist.remarks || 'No remarks provided'}
              </div>
            </div>
            <div>
              <h4 style="font-size: 12px; font-weight: 700; margin: 0 0 6px 0; color: #CC0000; text-align: center;">Engineer Signature</h4>
              <div style="border-radius: 4px; height: 60px; background-color: transparent; display: flex; align-items: center; justify-content: center; font-size: 9px; color: #6c757d; overflow: hidden;">
                ${signatureDataUrl ? `<img src="${signatureDataUrl}" alt="Signature" style="width: 100%; height: 100%; object-fit: contain; border: none; background: transparent;">` : 'Signature pending'}
              </div>
            </div>
          </div>
        </div>
      </body>
    </html>
  `;
}

/**
 * Generate bulk export summary page HTML
 */
export function generateBulkPDFSummaryHTML(props: {
  summary: import('./utils/bulk-export-analytics').BulkExportSummary;
  logoDataUrl?: string;
  pageNumber?: number;
  totalPages?: number;
  equipmentPage?: Array<{
    tagNo: string;
    equipmentName: string;
    location: string;
    client: string;
    overallStatus: 'OK' | 'Faulty';
    issueCount: number;
    completionPercentage: number;
  }>;
}): string {
  const { summary, logoDataUrl, pageNumber = 1, totalPages = 1, equipmentPage } = props;
  
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'OK': return '#28a745';
      case 'Faulty': return '#dc3545';
      case 'N/A': return '#ffc107';
      case 'Mixed': return '#fd7e14';
      default: return '#6c757d';
    }
  };



  const generateCombinedStatusChart = () => {
    const { OK, Faulty, 'N/A': NA, Missing } = summary.statistics;
    const total = summary.statistics.total;
    
    if (total === 0) {
      return `
        <svg width="120" height="120" viewBox="0 0 120 120">
          <circle cx="60" cy="60" r="50" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
          <text x="60" y="60" text-anchor="middle" font-size="12" fill="#6c757d">No Data</text>
        </svg>
      `;
    }
    
    const colors = {
      OK: '#28a745',
      Faulty: '#dc3545',
      'N/A': '#ffc107',
      Missing: '#6c757d'
    };
    
    let currentAngle = 0;
    const radius = 50;
    const centerX = 60;
    const centerY = 60;
    
    const slices = [
      { label: 'OK', value: OK, color: colors.OK },
      { label: 'Faulty', value: Faulty, color: colors.Faulty },
      { label: 'N/A', value: NA, color: colors['N/A'] },
      { label: 'Missing', value: Missing, color: colors.Missing }
    ].filter(slice => slice.value > 0);
    
    const pathElements = slices.map(slice => {
      const angle = (slice.value / total) * 360;
      const startAngle = currentAngle;
      const endAngle = currentAngle + angle;
      
      const x1 = centerX + radius * Math.cos((startAngle - 90) * Math.PI / 180);
      const y1 = centerY + radius * Math.sin((startAngle - 90) * Math.PI / 180);
      const x2 = centerX + radius * Math.cos((endAngle - 90) * Math.PI / 180);
      const y2 = centerY + radius * Math.sin((endAngle - 90) * Math.PI / 180);
      
      const largeArcFlag = angle > 180 ? 1 : 0;
      
      const pathData = [
        `M ${centerX} ${centerY}`,
        `L ${x1} ${y1}`,
        `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
        'Z'
      ].join(' ');
      
      currentAngle = endAngle;
      
      return `<path d="${pathData}" fill="${slice.color}" stroke="white" stroke-width="2"/>`;
    }).join('');
    
    return `
      <svg width="120" height="120" viewBox="0 0 120 120">
        ${pathElements}
        <circle cx="${centerX}" cy="${centerY}" r="20" fill="white" stroke="#dee2e6" stroke-width="1"/>
        <text x="${centerX}" y="${centerY - 3}" text-anchor="middle" font-size="12" font-weight="bold" fill="#000">${total}</text>
        <text x="${centerX}" y="${centerY + 8}" text-anchor="middle" font-size="8" fill="#666">Total</text>
      </svg>
    `;
  };



  const isFirstPage = pageNumber === 1;
  const equipmentToShow = equipmentPage || summary.equipmentOverview.slice(0, 200);

  return `
    <!DOCTYPE html>
    <html>
      <head>
        <meta charset="utf-8">
        <title>Bulk Export Summary - Auburn Engineering</title>
        <style>
          @page {
            size: A4;
            margin: 8mm;
          }
          * {
            -webkit-print-color-adjust: exact !important;
            color-adjust: exact !important;
            print-color-adjust: exact !important;
            box-sizing: border-box;
          }
          body {
            margin: 0;
            padding: 0;
            font-family: Arial, "Helvetica Neue", Helvetica, sans-serif;
            background: white;
            font-size: 10px;
            line-height: 1.3;
            color: #212529;
          }
          .page-container {
            width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            padding: 8mm;
            box-sizing: border-box;
            display: flex;
            flex-direction: column;
          }
        </style>
      </head>
      <body>
        <div class="page-container">
          
          <!-- Header -->
          <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; padding: 12px 15px; border-bottom: 3px solid #CC0000; background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%); border-radius: 8px;">
            <div>
              <h1 style="font-size: 22px; font-weight: 700; color: #CC0000; margin: 0 0 4px 0; letter-spacing: -0.3px;">
                ${isFirstPage ? 'Bulk Export Summary Report' : `Equipment Overview - Page ${pageNumber}`}
              </h1>
              <p style="font-size: 12px; color: #495057; margin: 0; font-weight: 600;">
                Auburn Engineering - PPM Inspection Summary
              </p>
            </div>
            <div style="width: 180px; height: 80px; display: flex; align-items: center; justify-content: center; overflow: hidden; border-radius: 6px;">
              ${logoDataUrl ? `<img src="${logoDataUrl}" alt="Auburn Engineering" style="max-width: 100%; max-height: 100%; object-fit: contain;">` : '<div style="font-size: 10px; color: #6c757d;">Auburn Engineering</div>'}
            </div>
          </div>

          ${isFirstPage ? `
          <!-- Export Information -->
          <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 12px; margin-bottom: 15px;">
            <div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 12px; border-left: 4px solid #0066cc;">
              <h3 style="font-size: 12px; font-weight: 700; color: #495057; margin: 0 0 8px 0;">Export Details</h3>
              <div style="font-size: 10px; margin-bottom: 4px;"><strong>Date:</strong> ${summary.exportDate}</div>
              <div style="font-size: 10px; margin-bottom: 4px;"><strong>Requested by:</strong> ${summary.requestedBy}</div>
              <div style="font-size: 10px;"><strong>Total Checklists:</strong> ${summary.totalChecklists}</div>
            </div>
            <div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 12px; border-left: 4px solid #28a745;">
              <h3 style="font-size: 12px; font-weight: 700; color: #495057; margin: 0 0 8px 0;">Date Range</h3>
              <div style="font-size: 11px; font-weight: 600; color: #212529;">${summary.dateRange}</div>
            </div>
            <div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 12px; border-left: 4px solid #ffc107;">
              <h3 style="font-size: 12px; font-weight: 700; color: #495057; margin: 0 0 8px 0;">Clients</h3>
              <div style="font-size: 10px;">${summary.clients.length > 3 ? summary.clients.slice(0, 3).join(', ') + '...' : summary.clients.join(', ')}</div>
              <div style="font-size: 9px; color: #6c757d; margin-top: 4px;">${summary.clients.length} client(s)</div>
            </div>
          </div>



          <!-- Overall Status & Category Breakdown -->
          <div style="display: grid; grid-template-columns: 1fr 2fr; gap: 15px; margin-bottom: 15px;">
            <div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; padding: 15px; text-align: center;">
              <h3 style="font-size: 14px; font-weight: 700; color: #495057; margin: 0 0 12px 0;">Overall Status Distribution</h3>
              ${generateCombinedStatusChart()}
              <div style="margin-top: 12px; font-size: 10px;">
                <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                  <span style="color: #495057;">OK:</span>
                  <span style="font-weight: 700; color: #28a745;">${summary.statistics.OK} (${summary.statistics.percentages.OK}%)</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                  <span style="color: #495057;">Faulty:</span>
                  <span style="font-weight: 700; color: #dc3545;">${summary.statistics.Faulty} (${summary.statistics.percentages.Faulty}%)</span>
                </div>
                <div style="display: flex; justify-content: space-between; margin-bottom: 4px;">
                  <span style="color: #495057;">N/A:</span>
                  <span style="font-weight: 700; color: #ffc107;">${summary.statistics['N/A']} (${summary.statistics.percentages['N/A']}%)</span>
                </div>
                <div style="display: flex; justify-content: space-between;">
                  <span style="color: #495057;">Missing:</span>
                  <span style="font-weight: 700; color: #6c757d;">${summary.statistics.Missing} (${summary.statistics.percentages.Missing}%)</span>
                </div>
              </div>
            </div>
            
            <div>
              <h3 style="font-size: 14px; font-weight: 700; color: #495057; margin: 0 0 12px 0;">Faulty Items by Category</h3>
              <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px; margin-bottom: 12px;">
                <div style="background: white; border: 1px solid #e9ecef; border-radius: 6px; padding: 10px; text-align: center; border-left: 4px solid #dc3545;">
                  <div style="font-size: 16px; font-weight: 700; color: #dc3545; margin-bottom: 4px;">${summary.categoryBreakdown.mechanical.Faulty}</div>
                  <div style="font-size: 9px; color: #495057; font-weight: 600; margin-bottom: 3px;">Mechanical</div>
                  <div style="font-size: 8px; color: #dc3545; font-weight: 700;">Faulty Items</div>
                </div>
                <div style="background: white; border: 1px solid #e9ecef; border-radius: 6px; padding: 10px; text-align: center; border-left: 4px solid #dc3545;">
                  <div style="font-size: 16px; font-weight: 700; color: #dc3545; margin-bottom: 4px;">${summary.categoryBreakdown.electrical.Faulty}</div>
                  <div style="font-size: 9px; color: #495057; font-weight: 600; margin-bottom: 3px;">Electrical</div>
                  <div style="font-size: 8px; color: #dc3545; font-weight: 700;">Faulty Items</div>
                </div>
                <div style="background: white; border: 1px solid #e9ecef; border-radius: 6px; padding: 10px; text-align: center; border-left: 4px solid #dc3545;">
                  <div style="font-size: 16px; font-weight: 700; color: #dc3545; margin-bottom: 4px;">${summary.categoryBreakdown.sequence.Faulty}</div>
                  <div style="font-size: 9px; color: #495057; font-weight: 600; margin-bottom: 3px;">Sequence</div>
                  <div style="font-size: 8px; color: #dc3545; font-weight: 700;">Faulty Items</div>
                </div>
              </div>
              <h3 style="font-size: 14px; font-weight: 700; color: #495057; margin: 0 0 12px 0;">Missing Items by Category</h3>
              <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 10px;">
                <div style="background: white; border: 1px solid #e9ecef; border-radius: 6px; padding: 10px; text-align: center; border-left: 4px solid #6c757d;">
                  <div style="font-size: 16px; font-weight: 700; color: #6c757d; margin-bottom: 4px;">${summary.categoryBreakdown.mechanical.Missing}</div>
                  <div style="font-size: 9px; color: #495057; font-weight: 600; margin-bottom: 3px;">Mechanical</div>
                  <div style="font-size: 8px; color: #6c757d; font-weight: 700;">Missing Items</div>
                </div>
                <div style="background: white; border: 1px solid #e9ecef; border-radius: 6px; padding: 10px; text-align: center; border-left: 4px solid #6c757d;">
                  <div style="font-size: 16px; font-weight: 700; color: #6c757d; margin-bottom: 4px;">${summary.categoryBreakdown.electrical.Missing}</div>
                  <div style="font-size: 9px; color: #495057; font-weight: 600; margin-bottom: 3px;">Electrical</div>
                  <div style="font-size: 8px; color: #6c757d; font-weight: 700;">Missing Items</div>
                </div>
                <div style="background: white; border: 1px solid #e9ecef; border-radius: 6px; padding: 10px; text-align: center; border-left: 4px solid #6c757d;">
                  <div style="font-size: 16px; font-weight: 700; color: #6c757d; margin-bottom: 4px;">${summary.categoryBreakdown.sequence.Missing}</div>
                  <div style="font-size: 9px; color: #495057; font-weight: 600; margin-bottom: 3px;">Sequence</div>
                  <div style="font-size: 8px; color: #6c757d; font-weight: 700;">Missing Items</div>
                </div>
              </div>
            </div>
          </div>
          ` : ''}

          <!-- Equipment Overview Table -->
          <div style="background: white; border: 1px solid #e9ecef; border-radius: 8px; overflow: hidden; flex: 1;">
            <div style="background: #CC0000; color: white; padding: 12px 15px; display: flex; justify-content: space-between; align-items: center;">
              <h3 style="font-size: 14px; font-weight: 700; margin: 0;">
                Equipment Status Overview
              </h3>
              ${totalPages > 1 ? `<span style="font-size: 11px; opacity: 0.9;">Page ${pageNumber} of ${totalPages}</span>` : ''}
            </div>
            <div style="overflow-x: auto;">
              <table style="width: 100%; border-collapse: collapse; font-size: 9px;">
                <thead>
                  <tr style="background: #f8f9fa; border-bottom: 2px solid #dee2e6;">
                    <th style="padding: 8px 10px; text-align: left; font-weight: 700; color: #495057; border-right: 1px solid #dee2e6;">Tag No</th>
                    <th style="padding: 8px 10px; text-align: left; font-weight: 700; color: #495057; border-right: 1px solid #dee2e6;">Equipment Name</th>
                    <th style="padding: 8px 10px; text-align: left; font-weight: 700; color: #495057; border-right: 1px solid #dee2e6;">Location</th>
                    <th style="padding: 8px 10px; text-align: left; font-weight: 700; color: #495057; border-right: 1px solid #dee2e6;">Client</th>
                    <th style="padding: 8px 10px; text-align: center; font-weight: 700; color: #495057; border-right: 1px solid #dee2e6;">Status</th>
                    <th style="padding: 8px 10px; text-align: center; font-weight: 700; color: #495057;">Issues</th>
                  </tr>
                </thead>
                <tbody>
                  ${equipmentToShow.map((equipment, index) => {
                    const isFaulty = equipment.overallStatus === 'Faulty' || equipment.issueCount > 0;
                    const rowBackground = isFaulty ? 
                      (index % 2 === 0 ? '#fff5f5' : '#fef2f2') : 
                      (index % 2 === 0 ? '#fdfdfd' : 'white');
                    const borderColor = isFaulty ? '#fc8181' : '#dee2e6';
                    
                    return `
                    <tr style="border-bottom: 1px solid ${borderColor}; background: ${rowBackground}; ${isFaulty ? 'border-left: 4px solid #dc3545;' : ''}">
                      <td style="padding: 6px 10px; font-weight: 600; color: #212529; border-right: 1px solid ${borderColor};">
                        ${equipment.tagNo}
                      </td>
                      <td style="padding: 6px 10px; color: #495057; border-right: 1px solid ${borderColor}; max-width: 120px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${equipment.equipmentName}</td>
                      <td style="padding: 6px 10px; color: #495057; border-right: 1px solid ${borderColor}; max-width: 100px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${equipment.location}</td>
                      <td style="padding: 6px 10px; color: #495057; border-right: 1px solid ${borderColor}; max-width: 100px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;">${equipment.client}</td>
                      <td style="padding: 6px 10px; text-align: center; border-right: 1px solid ${borderColor};">
                        <span style="background: ${getStatusColor(equipment.overallStatus)}; color: white; padding: 2px 6px; border-radius: 3px; font-size: 8px; font-weight: 700;">${equipment.overallStatus}</span>
                      </td>
                                             <td style="padding: 6px 10px; text-align: center; font-weight: 700; color: ${equipment.issueCount > 0 ? '#dc3545' : '#28a745'};">
                         ${equipment.issueCount}
                       </td>
                    </tr>
                  `;
                  }).join('')}
                </tbody>
              </table>
            </div>
          </div>

          <!-- Footer -->
          <div style="margin-top: 15px; padding-top: 10px; border-top: 1px solid #dee2e6; display: flex; justify-content: space-between; align-items: center; font-size: 9px; color: #6c757d;">
            <span>Generated by Auburn Engineering PPM System</span>
            <span>${new Date().toLocaleString()}</span>
          </div>
        </div>
      </body>
    </html>
  `;
} 
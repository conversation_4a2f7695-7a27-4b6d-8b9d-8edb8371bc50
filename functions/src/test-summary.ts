/**
 * Test file to demonstrate the bulk export summary functionality
 * This file shows how the new summary overlay works
 */

import { analyzeBulkExportData, generateStatisticsPieChart } from './utils/bulk-export-analytics';
import { generateBulkPDFSummaryHTML } from './pdf-template-cf';

// Sample test data
const sampleChecklists = [
  {
    id: 'test-1',
    generalInfo: {
      clientName: 'Test Client A',
      building: 'Building 1',
      equipmentName: 'AHU-001',
      location: 'Basement Level',
      tagNo: 'AHU-001',
      date: '2024-01-15',
      ppmAttempt: 'Monthly',
      inspectedBy: 'John Engineer',
      approvedBy: 'Mike Supervisor'
    },
    mechanicalChecks: {
      airflowVelocity: '5.2',
      beltWearPulleyAlignment: 'OK',
      bladeImpellerDamage: 'OK',
      excessiveVibration: 'Faulty',
      motorOverheating: 'OK'
    },
    electricalChecks: {
      currentAmps: '12.5',
      potentialVoltage: '415',
      circuitBreakerFunctional: 'OK',
      burntMarksDiscolorMelted: 'Faulty'
    },
    sequenceControlsChecks: {
      vfdVariableFrequencyDrive: 'OK',
      msfdDamperFunctional: 'N/A'
    },
    remarks: 'Minor vibration detected, requires attention',
    isCompleted: true
  },
  {
    id: 'test-2',
    generalInfo: {
      clientName: 'Test Client B',
      building: 'Building 2',
      equipmentName: 'FCU-102',
      location: 'Floor 2',
      tagNo: 'FCU-102',
      date: '2024-01-16',
      ppmAttempt: 'Weekly',
      inspectedBy: 'Sarah Engineer',
      approvedBy: 'Mike Supervisor'
    },
    mechanicalChecks: {
      airflowVelocity: '3.8',
      beltWearPulleyAlignment: 'OK',
      bladeImpellerDamage: 'OK',
      excessiveVibration: 'OK',
      motorOverheating: 'OK'
    },
    electricalChecks: {
      currentAmps: '8.2',
      potentialVoltage: '415',
      circuitBreakerFunctional: 'OK',
      burntMarksDiscolorMelted: 'OK'
    },
    sequenceControlsChecks: {
      vfdVariableFrequencyDrive: 'OK',
      msfdDamperFunctional: 'OK'
    },
    remarks: 'All systems operating normally',
    isCompleted: true
  }
];

const sampleMetadata = {
  clientName: 'Test Clients',
  dateRange: '2024-01-15 - 2024-01-16',
  exportMethod: 'cloud-function' as const,
  requestedBy: 'Test Admin'
};

/**
 * Test function to demonstrate summary generation
 */
export function testSummaryGeneration() {
  console.log('🧪 Testing Auburn Engineering Bulk Export Summary Generation');
  
  // Analyze sample data
  const summary = analyzeBulkExportData(sampleChecklists, sampleMetadata);
  
  console.log('📊 Summary Analysis Results:');
  console.log(`- Total Checklists: ${summary.totalChecklists}`);
  console.log(`- Overall Health: ${summary.insights.overallHealth}`);
  console.log(`- Critical Issues: ${summary.insights.criticalIssues}`);
  console.log(`- Equipment at Risk: ${summary.insights.equipmentAtRisk}`);
  
  console.log('\n📈 Statistics:');
  console.log(`- OK: ${summary.statistics.OK} (${summary.statistics.percentages.OK}%)`);
  console.log(`- Faulty: ${summary.statistics.Faulty} (${summary.statistics.percentages.Faulty}%)`);
  console.log(`- N/A: ${summary.statistics['N/A']} (${summary.statistics.percentages['N/A']}%)`);
  console.log(`- Missing: ${summary.statistics.Missing} (${summary.statistics.percentages.Missing}%)`);
  
  console.log('\n🔧 Problem Areas:');
  summary.problemAreas.forEach(area => {
    console.log(`- ${area.category}: ${area.issues} issues (${area.percentage}%) - Priority: ${area.priority}`);
  });
  
  console.log('\n💡 Recommendations:');
  summary.insights.recommendations.forEach(rec => {
    console.log(`- ${rec}`);
  });
  
  // Generate pie chart SVG
  const pieChart = generateStatisticsPieChart(summary.statistics);
  console.log('\n🎨 Pie Chart SVG Generated:', pieChart.length > 0 ? 'Success' : 'Failed');
  
  // Generate summary HTML (without logo for test)
  const summaryHTML = generateBulkPDFSummaryHTML({
    summary,
    pageNumber: 1,
    totalPages: 1
  });
  
  console.log('\n📄 Summary HTML Generated:', summaryHTML.length > 0 ? 'Success' : 'Failed');
  console.log(`   HTML Length: ${summaryHTML.length} characters`);
  
  console.log('\n✅ Test completed successfully!');
  console.log('\n🎯 Key Features Implemented:');
  console.log('   ✓ Beautiful summary overlay page');
  console.log('   ✓ Statistical analysis with pie charts');
  console.log('   ✓ Problem area identification');
  console.log('   ✓ Equipment overview table');
  console.log('   ✓ Smart pagination (up to 400 checklists)');
  console.log('   ✓ Auburn Engineering branding');
  console.log('   ✓ Professional insights and recommendations');
  
  return summary;
}

// Export for potential use in other files
export { sampleChecklists, sampleMetadata }; 
import { CallableRequest, HttpsError } from 'firebase-functions/v2/https';
import { logger } from 'firebase-functions';

/**
 * Configuration for App Check enforcement
 */
export interface AppCheckConfig {
  enforceAppCheck: boolean;
  logOnly: boolean;
  allowedOrigins?: string[];
  exemptFunctions?: string[];
}

/**
 * Default App Check configuration
 */
const defaultConfig: AppCheckConfig = {
  enforceAppCheck: process.env.NODE_ENV === 'production',
  logOnly: process.env.NODE_ENV === 'development',
  allowedOrigins: [
    'https://auburn-engineering.web.app',
    'https://auburn-engineering.firebaseapp.com',
    'http://localhost:3000' // For local development
  ],
  exemptFunctions: []
};

/**
 * Get App Check configuration from environment or use defaults
 */
function getAppCheckConfig(): AppCheckConfig {
  return {
    enforceAppCheck: process.env.APPCHECK_ENFORCE === 'true' || process.env.NODE_ENV === 'production',
    logOnly: process.env.APPCHECK_LOG_ONLY === 'true' || process.env.NODE_ENV === 'development',
    allowedOrigins: process.env.APPCHECK_ALLOWED_ORIGINS?.split(',') || defaultConfig.allowedOrigins,
    exemptFunctions: process.env.APPCHECK_EXEMPT_FUNCTIONS?.split(',') || defaultConfig.exemptFunctions
  };
}

/**
 * Verify App Check token from the request
 */
async function verifyAppCheckToken(request: CallableRequest): Promise<{
  isValid: boolean;
  error?: string;
  tokenInfo?: any;
}> {
  try {
    // Check if the request has an App Check token
    // For Firebase Functions v2, the token is automatically verified by Firebase
    // We just need to check if request.app exists
    if (!request.app) {
      return {
        isValid: false,
        error: 'App Check token is missing or invalid'
      };
    }

    // If we get here, the token was already verified by Firebase
    // request.app contains the decoded App Check token information
    return {
      isValid: true,
      tokenInfo: request.app
    };
    
  } catch (error) {
    logger.error('App Check token verification failed:', error);
    
    return {
      isValid: false,
      error: error instanceof Error ? error.message : 'Token verification failed'
    };
  }
}

/**
 * Middleware to enforce App Check for callable functions
 */
export function requireAppCheck(functionName?: string) {
  return async (request: CallableRequest): Promise<void> => {
    const config = getAppCheckConfig();
    
    // Skip if function is exempt
    if (functionName && config.exemptFunctions?.includes(functionName)) {
      logger.info(`App Check: Skipping verification for exempt function: ${functionName}`);
      return;
    }

    // Skip if App Check is disabled
    if (!config.enforceAppCheck && !config.logOnly) {
      logger.debug('App Check: Disabled, skipping verification');
      return;
    }

    const verification = await verifyAppCheckToken(request);
    
    if (!verification.isValid) {
      const errorMessage = `App Check verification failed: ${verification.error}`;
      
      if (config.logOnly) {
        logger.warn(errorMessage, {
          functionName,
          clientIP: request.rawRequest.ip,
          userAgent: request.rawRequest.get('user-agent')
        });
        return; // Continue execution in log-only mode
      }
      
      // Enforce App Check - throw error
      logger.error(errorMessage, {
        functionName,
        clientIP: request.rawRequest.ip,
        userAgent: request.rawRequest.get('user-agent')
      });
      
      throw new HttpsError(
        'failed-precondition',
        'App Check verification failed. This request is not authorized.',
        { code: 'app-check-required' }
      );
    }
    
    logger.info('App Check: Token verified successfully', {
      functionName,
      appId: verification.tokenInfo?.appId || verification.tokenInfo?.app_id,
      clientIP: request.rawRequest.ip
    });
  };
}

/**
 * Utility function to check if origin is allowed
 */
function isOriginAllowed(origin: string | undefined, allowedOrigins: string[]): boolean {
  if (!origin) return false;
  
  return allowedOrigins.some(allowed => {
    if (allowed.includes('*')) {
      // Support wildcard matching
      const pattern = allowed.replace(/\*/g, '.*');
      return new RegExp(`^${pattern}$`).test(origin);
    }
    return origin === allowed;
  });
}

/**
 * Enhanced middleware that also checks origin
 */
export function requireAppCheckWithOrigin(functionName?: string) {
  return async (request: CallableRequest): Promise<void> => {
    const config = getAppCheckConfig();
    
    // Check origin first
    const origin = request.rawRequest.get('origin') || request.rawRequest.get('referer');
    
    if (origin && !isOriginAllowed(origin, config.allowedOrigins || [])) {
      const errorMessage = `App Check: Request from unauthorized origin: ${origin}`;
      
      if (config.logOnly) {
        logger.warn(errorMessage, { functionName });
        return;
      }
      
      throw new HttpsError(
        'permission-denied',
        'Request from unauthorized origin',
        { code: 'unauthorized-origin' }
      );
    }
    
    // Then check App Check token
    await requireAppCheck(functionName)(request);
  };
}

/**
 * Utility to get App Check status for debugging
 */
export async function getAppCheckStatus(request: CallableRequest): Promise<{
  hasToken: boolean;
  isValid: boolean;
  appId?: string;
  error?: string;
}> {
  const verification = await verifyAppCheckToken(request);
  
  return {
    hasToken: !!request.app,
    isValid: verification.isValid,
    appId: verification.tokenInfo?.appId || verification.tokenInfo?.app_id,
    error: verification.error
  };
}

export default {
  requireAppCheck,
  requireAppCheckWithOrigin,
  getAppCheckStatus
}; 
/**
 * Import function triggers from their respective submodules:
 *
 * import {onCall} from "firebase-functions/v2/https";
 * import {onDocumentWritten} from "firebase-functions/v2/firestore";
 *
 * See a full list of supported triggers at https://firebase.google.com/docs/functions
 */

import { onCall } from 'firebase-functions/v2/https';
import { onDocumentDeleted } from 'firebase-functions/v2/firestore';
import * as functions from 'firebase-functions/v1';
import { logger } from 'firebase-functions';
import * as admin from 'firebase-admin';
import { requireAppCheck, getAppCheckStatus } from './middleware/app-check';

// Initialize Firebase Admin SDK
if (admin.apps.length === 0) {
  admin.initializeApp();
}

// Define user roles
export enum UserRole {
  USER = 'user',
  TECHNICIAN = 'technician',
  ADMIN = 'admin'
}

// User document interface
export interface UserDocument {
  uid: string;
  email: string;
  displayName: string | null;
  photoURL: string | null;
  role: UserRole;
  createdAt: admin.firestore.FieldValue;
  updatedAt: admin.firestore.FieldValue;
  isActive: boolean;
  metadata: {
    lastLoginAt: admin.firestore.FieldValue | null;
    emailVerified: boolean;
    creationMethod: 'email' | 'google' | 'other';
  };
}

/**
 * Authentication trigger (v1) - automatically creates user document when a new user is created
 * This runs automatically whenever a new user signs up
 * Note: v2 functions don't support auth triggers yet, so we use v1
 */
export const createUserDocument = functions.auth.user().onCreate(async (user) => {
  const { uid, email, emailVerified, providerData } = user;
  let { displayName, photoURL } = user;

  try {
    logger.info('Auto-creating user document for new user', { uid, email, hasDisplayName: !!displayName });

    // Handle potential timing issue where displayName might not be immediately available
    // This can happen if updateProfile is called after createUser but before this function processes
    if (!displayName && providerData?.some((provider: any) => provider.providerId === 'password')) {
      // For email/password users, wait a brief moment and check again for displayName
      await new Promise(resolve => setTimeout(resolve, 1000));
      try {
        const refreshedUser = await admin.auth().getUser(uid);
        displayName = refreshedUser.displayName || undefined;
        photoURL = refreshedUser.photoURL || undefined;
        logger.info('Refreshed user data after delay', { uid, hasDisplayName: !!displayName });
      } catch (refreshError) {
        logger.warn('Could not refresh user data', { uid, error: refreshError });
      }
    }

    // Determine creation method based on provider data
    let creationMethod: 'email' | 'google' | 'other' = 'other';
    if (providerData?.some((provider: any) => provider.providerId === 'google.com')) {
      creationMethod = 'google';
    } else if (providerData?.some((provider: any) => provider.providerId === 'password')) {
      creationMethod = 'email';
    }

    // Create user document data
    const userDoc: UserDocument = {
      uid,
      email: email || '',
      displayName: displayName || null,
      photoURL: photoURL || null,
      role: UserRole.USER, // Default role for new users
      createdAt: admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp(),
      isActive: true,
      metadata: {
        lastLoginAt: admin.firestore.FieldValue.serverTimestamp(),
        emailVerified: emailVerified || false,
        creationMethod
      }
    };

    // Create the user document in Firestore
    await admin.firestore()
      .collection('users')
      .doc(uid)
      .set(userDoc);

    logger.info('User document created automatically', { 
      uid, 
      email,
      displayName: displayName || 'null',
      role: UserRole.USER 
    });

  } catch (error) {
    logger.error('Error auto-creating user document', { 
      uid, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
    // Log error but don't throw to avoid blocking user creation
  }
});

/**
 * HTTP callable function to update user login time
 * Called from the client after user sign in
 */
export const updateUserLoginTime = onCall(async (request) => {
  // Validate App Check token
  await requireAppCheck('updateUserLoginTime')(request);
  
  const { auth } = request;
  
  if (!auth) {
    throw new Error('Authentication required');
  }
  
  try {
    // Update last login time (user document should already exist from auth trigger)
    const userDocRef = admin.firestore().collection('users').doc(auth.uid);
    await userDocRef.update({
      'metadata.lastLoginAt': admin.firestore.FieldValue.serverTimestamp(),
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    });

    logger.info('Updated user login time', { uid: auth.uid });
    return { success: true, message: 'Login time updated successfully' };
  } catch (error) {
    logger.error('Error updating user login time', { 
      uid: auth.uid, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
    throw error;
  }
});

/**
 * Cloud function to get user profile data
 */
export const getUserProfile = onCall(async (request) => {
  // Validate App Check token
  await requireAppCheck('getUserProfile')(request);
  
  const { auth } = request;
  
  if (!auth) {
    throw new Error('Authentication required');
  }

  try {
    const userDoc = await admin.firestore()
      .collection('users')
      .doc(auth.uid)
      .get();

    if (!userDoc.exists) {
      throw new Error('User document not found');
    }

    return userDoc.data();
  } catch (error) {
    logger.error('Error getting user profile', { 
      uid: auth.uid, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
    throw error;
  }
});

// Import and export proxy functions
export { proxyImage, proxyPdf } from './proxy-functions';

// Import and export admin functions
export { queueExport, getExportQueueStats, queueExportHttp } from './admin-functions';

/**
 * Cloud function to update user role (admin only)
 */
export const updateUserRole = onCall(async (request) => {
  // Validate App Check token
  await requireAppCheck('updateUserRole')(request);
  
  const { auth, data } = request;
  
  if (!auth) {
    throw new Error('Authentication required');
  }

  const { targetUserId, newRole } = data;

  if (!targetUserId || !newRole) {
    throw new Error('Target user ID and new role are required');
  }

  if (!Object.values(UserRole).includes(newRole)) {
    throw new Error('Invalid role specified');
  }

  try {
    // Check if the requesting user is an admin
    const requestingUserDoc = await admin.firestore()
      .collection('users')
      .doc(auth.uid)
      .get();

    if (!requestingUserDoc.exists || requestingUserDoc.data()?.role !== UserRole.ADMIN) {
      throw new Error('Insufficient permissions. Admin role required.');
    }

    // Update the target user's role
    await admin.firestore()
      .collection('users')
      .doc(targetUserId)
      .update({
        role: newRole,
        updatedAt: admin.firestore.FieldValue.serverTimestamp()
      });

    logger.info('User role updated', { 
      adminUid: auth.uid, 
      targetUserId, 
      newRole 
    });

    return { success: true, message: 'User role updated successfully' };
  } catch (error) {
    logger.error('Error updating user role', { 
      adminUid: auth.uid, 
      targetUserId, 
      newRole,
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
    throw error;
  }
});

/**
 * Clean up user data when a user document is deleted
 */
export const cleanupUserData = onDocumentDeleted('users/{userId}', async (event) => {
  const userId = event.params.userId;
  
  try {
    logger.info('Starting user data cleanup', { userId });

    // Delete all user's checklists
    const checklistsQuery = admin.firestore()
      .collection('checklists')
      .where('userId', '==', userId);

    const checklistsSnapshot = await checklistsQuery.get();
    const batch = admin.firestore().batch();

    checklistsSnapshot.docs.forEach(doc => {
      batch.delete(doc.ref);
    });

    await batch.commit();

    logger.info('User data cleanup completed', { 
      userId, 
      deletedChecklists: checklistsSnapshot.size 
    });

  } catch (error) {
    logger.error('Error during user data cleanup', { 
      userId, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
  }
});

/**
 * Health check function
 */
export const healthCheck = onCall(async () => {
  return {
    status: 'healthy',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  };
});

/**
 * App Check status function for debugging
 */
export const getAppCheckTokenStatus = onCall(async (request) => {
  const { auth } = request;
  
  if (!auth) {
    throw new Error('Authentication required');
  }

  try {
    const status = await getAppCheckStatus(request);
    
    logger.info('App Check status requested', { 
      uid: auth.uid,
      hasToken: status.hasToken,
      isValid: status.isValid 
    });
    
    return {
      ...status,
      timestamp: new Date().toISOString(),
      environment: process.env.NODE_ENV || 'unknown'
    };
  } catch (error) {
    logger.error('Error getting App Check status', { 
      uid: auth.uid, 
      error: error instanceof Error ? error.message : 'Unknown error' 
    });
    throw error;
  }
});

// Export bulk export processor
export { processBulkExport } from './bulk-export-processor';

// Add individual PDF export function (HTTP version)
export { generateIndividualPDFHttp } from './individual-pdf-export';
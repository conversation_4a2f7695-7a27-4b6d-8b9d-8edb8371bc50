import { onRequest } from 'firebase-functions/v2/https';
import { logger } from 'firebase-functions';
import { getStorage } from 'firebase-admin/storage';
import cors from 'cors';

// CORS configuration
const corsHandler = cors({
  origin: [
    'https://auburnengineering.com',
    'https://auburn-engineering.web.app',
    'https://auburn-engineering.firebaseapp.com',
    /^https:\/\/.*\.web\.app$/,
    /^https:\/\/.*\.firebaseapp\.com$/,
    ...(process.env.NODE_ENV === 'development' ? ['http://localhost:3000', 'http://127.0.0.1:3000'] : [])
  ],
  methods: ['GET', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization'],
  credentials: false
});

/**
 * Cloud Function to proxy images from Firebase Storage
 * Replaces /api/proxy-image Next.js route
 */
export const proxyImage = onRequest({
  region: 'asia-east1',
  memory: '512MiB',
  timeoutSeconds: 60,
  maxInstances: 100
}, async (req, res) => {
  return new Promise<void>((resolve) => {
    corsHandler(req, res, async () => {
      try {
        if (req.method !== 'GET') {
          res.status(405).json({ error: 'Method not allowed' });
          resolve();
          return;
        }

        const imageUrl = req.query.url as string;
        const width = req.query.w as string;
        const quality = req.query.q as string;

        if (!imageUrl) {
          res.status(400).json({ error: 'Missing image URL' });
          resolve();
          return;
        }

        // Validate that it's a Firebase Storage URL
        if (!imageUrl.includes('firebasestorage.googleapis.com') && !imageUrl.includes('firebasestorage.app')) {
          res.status(400).json({ error: 'Invalid image URL' });
          resolve();
          return;
        }

        logger.info('Proxying Firebase Storage image', {
          url: imageUrl,
          width: width || 'auto',
          quality: quality || 'auto'
        });

        // Extract file path from Firebase Storage URL
        const urlMatch = imageUrl.match(/\/o\/(.+?)\?/);
        if (!urlMatch) {
          res.status(400).json({ error: 'Invalid Firebase Storage URL format' });
          resolve();
          return;
        }

        const filePath = decodeURIComponent(urlMatch[1]);
        logger.info('Extracted file path from URL', { filePath });

        // Use Firebase Admin SDK to get file directly (bypasses App Check)
        const bucket = getStorage().bucket();
        const file = bucket.file(filePath);

        // Check if file exists
        const [exists] = await file.exists();
        if (!exists) {
          logger.error('File not found in Firebase Storage', { filePath });
          res.status(404).json({ error: 'File not found' });
          resolve();
          return;
        }

        // Download file content
        const [buffer] = await file.download();
        const [metadata] = await file.getMetadata();
        const contentType = metadata.contentType || 'image/jpeg';

        logger.info('Successfully loaded image via Admin SDK', {
          filePath,
          contentType,
          size: buffer.length
        });

        // Set response headers
        res.set({
          'Content-Type': contentType,
          'Content-Length': buffer.length.toString(),
          'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET',
          'Access-Control-Allow-Headers': 'Content-Type',
          // Add debugging headers
          'X-Proxy-Source': 'firebase-admin-sdk',
          'X-Original-URL': imageUrl,
          'X-File-Path': filePath,
        });

        // Send the image buffer
        res.status(200).send(buffer);
        resolve();

      } catch (error) {
        logger.error('Error proxying image', {
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
          url: req.query.url
        });
        res.status(500).json({ error: 'Internal server error' });
        resolve();
      }
    });
  });
});

/**
 * Cloud Function to proxy PDFs from Firebase Storage
 * Replaces /api/proxy-pdf Next.js route
 */
export const proxyPdf = onRequest({
  region: 'asia-east1',
  memory: '1GiB',
  timeoutSeconds: 120,
  maxInstances: 50
}, async (req, res) => {
  return new Promise<void>((resolve) => {
    corsHandler(req, res, async () => {
      try {
        if (req.method !== 'GET') {
          res.status(405).json({ error: 'Method not allowed' });
          resolve();
          return;
        }

        const pdfUrl = req.query.url as string;

        if (!pdfUrl) {
          res.status(400).json({ error: 'Missing PDF URL' });
          resolve();
          return;
        }

        // Validate that it's a Firebase Storage URL
        if (!pdfUrl.includes('firebasestorage.googleapis.com') && !pdfUrl.includes('firebasestorage.app')) {
          res.status(400).json({ error: 'Invalid PDF URL' });
          resolve();
          return;
        }

        logger.info('Proxying Firebase Storage PDF', { url: pdfUrl });

        // Fetch the PDF from Firebase Storage
        const response = await fetch(pdfUrl, {
          method: 'GET',
          headers: {
            'User-Agent': 'Mozilla/5.0 (compatible; Auburn Engineering PDF Proxy)',
            'Accept': 'application/pdf',
          },
        });

        if (!response.ok) {
          logger.error('Failed to fetch PDF from Firebase Storage', {
            status: response.status,
            statusText: response.statusText,
            url: pdfUrl
          });
          res.status(response.status).json({
            error: `Failed to fetch PDF: ${response.status} ${response.statusText}`
          });
          resolve();
          return;
        }

        const pdfBuffer = await response.arrayBuffer();
        const contentType = response.headers.get('content-type') || 'application/pdf';

        // Set response headers
        res.set({
          'Content-Type': contentType,
          'Content-Length': pdfBuffer.byteLength.toString(),
          'Cache-Control': 'public, max-age=86400', // Cache for 24 hours
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET',
          'Access-Control-Allow-Headers': 'Content-Type',
          // Add debugging headers
          'X-Proxy-Source': 'firebase-storage',
          'X-Original-URL': pdfUrl,
          'X-Content-Type': 'pdf',
        });

        // Send the PDF buffer
        res.status(200).send(Buffer.from(pdfBuffer));
        resolve();

      } catch (error) {
        logger.error('Error proxying PDF', {
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
          url: req.query.url
        });
        res.status(500).json({ error: 'Internal server error' });
        resolve();
      }
    });
  });
}); 
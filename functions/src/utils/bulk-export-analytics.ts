interface ChecklistData {
  id: string;
  generalInfo: {
    clientName: string;
    building: string;
    equipmentName: string;
    location: string;
    tagNo: string;
    date: string;
    ppmAttempt: string;
    inspectedBy: string;
    approvedBy: string;
  };
  mechanicalChecks: Record<string, any>;
  electricalChecks: Record<string, any>;
  sequenceControlsChecks: Record<string, any>;
  remarks?: string;
  beforeImage?: string;
  afterImage?: string;
  signature?: string;
  isCompleted: boolean;
}

interface ExportMetadata {
  clientName?: string;
  dateRange?: string;
  exportMethod: 'cloud-function';
  requestedBy: string;
}

export interface BulkExportSummary {
  totalChecklists: number;
  dateRange: string;
  clients: string[];
  requestedBy: string;
  exportDate: string;
  statistics: {
    OK: number;
    Faulty: number;
    'N/A': number;
    Missing: number;
    total: number;
    percentages: {
      OK: number;
      Faulty: number;
      'N/A': number;
      Missing: number;
    };
  };
  faultyFocus: {
    totalFaultyItems: number;
    faultyEquipmentCount: number;
    faultyPercentage: number;
    criticalEquipmentCount: number;
    urgentRepairsNeeded: number;
  };
  categoryBreakdown: {
    mechanical: { OK: number; Faulty: number; 'N/A': number; Missing: number; total: number };
    electrical: { OK: number; Faulty: number; 'N/A': number; Missing: number; total: number };
    sequence: { OK: number; Faulty: number; 'N/A': number; Missing: number; total: number };
  };
  faultyCategoryBreakdown: {
    mechanical: { count: number; percentage: number; priority: 'critical' | 'high' | 'medium' | 'low' };
    electrical: { count: number; percentage: number; priority: 'critical' | 'high' | 'medium' | 'low' };
    sequence: { count: number; percentage: number; priority: 'critical' | 'high' | 'medium' | 'low' };
  };
  problemAreas: Array<{
    category: string;
    issues: number;
    percentage: number;
    priority: 'high' | 'medium' | 'low';
  }>;
  topIssues: Array<{
    field: string;
    label: string;
    count: number;
    equipmentAffected: string[];
    category: string;
  }>;
  faultyEquipmentOverview: Array<{
    tagNo: string;
    equipmentName: string;
    location: string;
    client: string;
    faultyItemsCount: number;
    criticalityLevel: 'critical' | 'high' | 'medium' | 'low';
    faultyItems: string[];
    urgencyScore: number;
  }>;
  equipmentOverview: Array<{
    tagNo: string;
    equipmentName: string;
    location: string;
    client: string;
    overallStatus: 'OK' | 'Faulty';
    issueCount: number;
    completionPercentage: number;
  }>;
  insights: {
    criticalIssues: number;
    equipmentAtRisk: number;
    maintenanceRequired: number;
    overallHealth: 'Excellent' | 'Good' | 'Fair' | 'Poor';
    recommendations: string[];
    urgentActions: string[];
    safetyRisks: string[];
    estimatedRepairTime: string;
  };
}

// Field configuration for analysis
const FIELD_CONFIGS = {
  mechanical: [
    { key: 'airflowVelocity', label: 'Airflow in M/S - Face Velocity', type: 'number' },
    { key: 'beltWearPulleyAlignment', label: 'Belt wear & Pulley alignment', type: 'status' },
    { key: 'bladeImpellerDamage', label: 'Blade / Impeller damage crack', type: 'status' },
    { key: 'boltSetScrewTightness', label: 'Bolt & Set Screw Tightness', type: 'status' },
    { key: 'bladeTipClearance', label: 'Check Blade tip clearance', type: 'status' },
    { key: 'excessiveVibration', label: 'Check Excessive Vibration', type: 'status' },
    { key: 'fanGuardProtection', label: 'Check Fan Guard protection', type: 'status' },
    { key: 'fanPowerOff', label: 'Check Fan Power Off', type: 'status' },
    { key: 'motorOverheating', label: 'Check Motor Overheating', type: 'status' },
    { key: 'rotationDirection', label: 'Check Rotation Clockwise/Anti', type: 'status' },
    { key: 'cleanBladesHousing', label: 'Clean Blades & Housing', type: 'status' },
    { key: 'dustDebrisRemoval', label: 'Dust & Debris removal', type: 'status' },
    { key: 'erraticOperation', label: 'Erratic Operation / Malfunctioning', type: 'status' },
    { key: 'inletVanesOperation', label: 'Inlet Vanes Operation freedom', type: 'status' },
    { key: 'bearingLubrication', label: 'Lubrication on Bearings', type: 'status' },
    { key: 'noObstructionsBackflow', label: 'No obstructions / Backflow', type: 'status' },
    { key: 'physicalDamageStability', label: 'Physical Damage stability of fan', type: 'status' },
    { key: 'speedRpm', label: 'Speed in Rotation per Minute', type: 'number' },
    { key: 'springMountVibrationIsolator', label: 'Spring Mount / Vibration Isolator', type: 'status' },
    { key: 'unusualSoundDecibel', label: 'Unusual Sound in Decibel', type: 'number' }
  ],
  electrical: [
    { key: 'bmsControlsInterlocks', label: 'BMS Controls & Interlocks', type: 'status' },
    { key: 'burntMarksDiscolorMelted', label: 'Burnt Marks, Discolor, Melted', type: 'status' },
    { key: 'circuitBreakerFunctional', label: 'Check Circuit Breaker Functional', type: 'status' },
    { key: 'contractorsBreakers', label: 'Check Contractors & Breakers', type: 'status' },
    { key: 'fireAlarmConnected', label: 'Check Fire Alarm Connected', type: 'status' },
    { key: 'fuseTerminals', label: 'Check Fuse & Terminals', type: 'status' },
    { key: 'mccPowerOffBreaker', label: 'Check MCC Power Off - Breaker', type: 'status' },
    { key: 'signsLiquidLeaks', label: 'Check Signs of liquid Leaks', type: 'status' },
    { key: 'tripSettingsFunction', label: 'Check Trip settings & Function', type: 'status' },
    { key: 'controlRelaysOperations', label: 'Control Relays operations', type: 'status' },
    { key: 'currentAmps', label: 'Current in Amps', type: 'number' },
    { key: 'doorsCoversCloseProperly', label: 'Doors & Covers close properly', type: 'status' },
    { key: 'frayingExposedWires', label: 'Fraying / Exposed Wires', type: 'status' },
    { key: 'highLowSpeedVerification', label: 'High / Low Speed Verification', type: 'status' },
    { key: 'indicationsOnOffTrip', label: 'Indications [ON] [OFF] & [TRIP]', type: 'status' },
    { key: 'looseWiresToBeTightened', label: 'Loose wires to be tightened', type: 'status' },
    { key: 'motorPowerKw', label: 'Motor Power in KW', type: 'number' },
    { key: 'potentialVoltage', label: 'Potential Voltage', type: 'number' },
    { key: 'selectorHandStopAuto', label: 'Selector [HAND] [STOP] [AUTO]', type: 'status' },
    { key: 'testEmergencyStopButton', label: 'Test Emergency Stop Button', type: 'status' }
  ],
  sequence: [
    { key: 'dptDifferentialPressureTransmitter', label: 'DPT - Differential Pressure Transmitter', type: 'status' },
    { key: 'erraticOperationMalfunctioning', label: 'Erratic Operation / Malfunctioning', type: 'status' },
    { key: 'indicationsOnOffTrip', label: 'Indications [ON] [OFF] & [TRIP]', type: 'status' },
    { key: 'mccOffOverrideFunction', label: 'MCC Off - Override Function', type: 'status' },
    { key: 'msfdDamperFunctional', label: 'MSFD - Damper Functional', type: 'status' },
    { key: 'offWithDuctDetectorActivation', label: 'Off with Duct Detector activation', type: 'status' },
    { key: 'overrideFscsPanelStatus', label: 'Override (FSCS) Panel Status', type: 'status' },
    { key: 'sameTagNameInMccFan', label: 'Same Tag Name in MCC & Fan', type: 'status' },
    { key: 'selectorRunStopAuto', label: 'Selector [RUN] [STOP] [AUTO]', type: 'status' },
    { key: 'vfdVariableFrequencyDrive', label: 'VFD - Variable Frequency Drive', type: 'status' }
  ]
};

/**
 * Analyze bulk export data and generate comprehensive summary with faulty-focus
 */
export function analyzeBulkExportData(
  checklists: ChecklistData[],
  metadata: ExportMetadata
): BulkExportSummary {
  const summary: BulkExportSummary = {
    totalChecklists: checklists.length,
    dateRange: metadata.dateRange || 'Not specified',
    clients: [],
    requestedBy: metadata.requestedBy,
    exportDate: new Date().toLocaleDateString(),
    statistics: {
      OK: 0,
      Faulty: 0,
      'N/A': 0,
      Missing: 0,
      total: 0,
      percentages: { OK: 0, Faulty: 0, 'N/A': 0, Missing: 0 }
    },
    faultyFocus: {
      totalFaultyItems: 0,
      faultyEquipmentCount: 0,
      faultyPercentage: 0,
      criticalEquipmentCount: 0,
      urgentRepairsNeeded: 0
    },
    categoryBreakdown: {
      mechanical: { OK: 0, Faulty: 0, 'N/A': 0, Missing: 0, total: 0 },
      electrical: { OK: 0, Faulty: 0, 'N/A': 0, Missing: 0, total: 0 },
      sequence: { OK: 0, Faulty: 0, 'N/A': 0, Missing: 0, total: 0 }
    },
    faultyCategoryBreakdown: {
      mechanical: { count: 0, percentage: 0, priority: 'low' },
      electrical: { count: 0, percentage: 0, priority: 'low' },
      sequence: { count: 0, percentage: 0, priority: 'low' }
    },
    problemAreas: [],
    topIssues: [],
    faultyEquipmentOverview: [],
    equipmentOverview: [],
    insights: {
      criticalIssues: 0,
      equipmentAtRisk: 0,
      maintenanceRequired: 0,
      overallHealth: 'Good',
      recommendations: [],
      urgentActions: [],
      safetyRisks: [],
      estimatedRepairTime: 'To be determined'
    }
  };

  // Extract unique clients
  const clientSet = new Set<string>();
  checklists.forEach(checklist => {
    if (checklist.generalInfo.clientName) {
      clientSet.add(checklist.generalInfo.clientName);
    }
  });
  summary.clients = Array.from(clientSet);

  // Analyze each checklist with focus on faulty items
  const fieldIssueCount: Record<string, { count: number; label: string; category: string; equipment: Set<string> }> = {};
  
  checklists.forEach(checklist => {
    const equipmentId = `${checklist.generalInfo.tagNo} - ${checklist.generalInfo.equipmentName}`;
    let equipmentIssueCount = 0;
    let equipmentTotalChecks = 0;
    let equipmentStatuses: string[] = [];
    let faultyItems: string[] = [];

    // Analyze each category
    ['mechanical', 'electrical', 'sequence'].forEach(category => {
      const categoryData = category === 'mechanical' ? checklist.mechanicalChecks :
                          category === 'electrical' ? checklist.electricalChecks :
                          checklist.sequenceControlsChecks;
      
      const categoryFields = FIELD_CONFIGS[category as keyof typeof FIELD_CONFIGS];
      
      categoryFields.forEach(field => {
        const value = categoryData[field.key];
        const status = getStatusFromValue(value);
        
        if (status) {
          summary.statistics[status]++;
          summary.statistics.total++;
          summary.categoryBreakdown[category as keyof typeof summary.categoryBreakdown][status]++;
          summary.categoryBreakdown[category as keyof typeof summary.categoryBreakdown].total++;
          
          equipmentTotalChecks++;
          equipmentStatuses.push(status);
          
          // Focus on faulty items
          if (status === 'Faulty') {
            equipmentIssueCount++;
            faultyItems.push(field.label);
            summary.faultyFocus.totalFaultyItems++;
            
            // Track field-specific issues
            const fieldKey = `${category}.${field.key}`;
            if (!fieldIssueCount[fieldKey]) {
              fieldIssueCount[fieldKey] = {
                count: 0,
                label: field.label,
                category,
                equipment: new Set()
              };
            }
            fieldIssueCount[fieldKey].count++;
            fieldIssueCount[fieldKey].equipment.add(equipmentId);
          }
        }
      });
    });

    // Determine overall equipment status
    const overallStatus = determineOverallStatus(equipmentStatuses);
    const completionPercentage = equipmentTotalChecks > 0 ? 
      Math.round(((equipmentTotalChecks - equipmentIssueCount) / equipmentTotalChecks) * 100) : 0;

    summary.equipmentOverview.push({
      tagNo: checklist.generalInfo.tagNo,
      equipmentName: checklist.generalInfo.equipmentName,
      location: checklist.generalInfo.location,
      client: checklist.generalInfo.clientName,
      overallStatus,
      issueCount: equipmentIssueCount,
      completionPercentage
    });

    // Add to faulty equipment overview if it has faulty items
    if (faultyItems.length > 0) {
      const criticalityLevel = faultyItems.length >= 5 ? 'critical' :
                              faultyItems.length >= 3 ? 'high' :
                              faultyItems.length >= 2 ? 'medium' : 'low';
      
      const urgencyScore = Math.min(10, Math.max(1, faultyItems.length * 2));

      summary.faultyEquipmentOverview.push({
        tagNo: checklist.generalInfo.tagNo,
        equipmentName: checklist.generalInfo.equipmentName,
        location: checklist.generalInfo.location,
        client: checklist.generalInfo.clientName,
        faultyItemsCount: faultyItems.length,
        criticalityLevel,
        faultyItems,
        urgencyScore
      });

      summary.faultyFocus.faultyEquipmentCount++;
      if (faultyItems.length >= 3) {
        summary.faultyFocus.criticalEquipmentCount++;
      }
      if (urgencyScore >= 7) {
        summary.faultyFocus.urgentRepairsNeeded++;
      }
    }
  });

  // Calculate faulty-focused statistics
  summary.faultyFocus.faultyPercentage = summary.statistics.total > 0 ? 
    Math.round((summary.faultyFocus.totalFaultyItems / summary.statistics.total) * 100) : 0;

  // Calculate percentages
  if (summary.statistics.total > 0) {
    summary.statistics.percentages.OK = Math.round((summary.statistics.OK / summary.statistics.total) * 100);
    summary.statistics.percentages.Faulty = Math.round((summary.statistics.Faulty / summary.statistics.total) * 100);
    summary.statistics.percentages['N/A'] = Math.round((summary.statistics['N/A'] / summary.statistics.total) * 100);
    summary.statistics.percentages.Missing = Math.round((summary.statistics.Missing / summary.statistics.total) * 100);
  }

  // Generate faulty-focused category breakdown
  ['mechanical', 'electrical', 'sequence'].forEach(category => {
    const categoryData = summary.categoryBreakdown[category as keyof typeof summary.categoryBreakdown];
    const faultyCount = categoryData.Faulty;
    const totalFaulty = summary.faultyFocus.totalFaultyItems;
    const percentage = totalFaulty > 0 ? Math.round((faultyCount / totalFaulty) * 100) : 0;
    
    const priority = faultyCount >= 10 ? 'critical' :
                    faultyCount >= 5 ? 'high' :
                    faultyCount >= 2 ? 'medium' : 'low';

    summary.faultyCategoryBreakdown[category as keyof typeof summary.faultyCategoryBreakdown] = {
      count: faultyCount,
      percentage,
      priority
    };
  });

  // Generate problem areas (only for categories with faulty items)
  ['mechanical', 'electrical', 'sequence'].forEach(category => {
    const categoryData = summary.categoryBreakdown[category as keyof typeof summary.categoryBreakdown];
    const faultyCount = categoryData.Faulty;
    
    if (faultyCount > 0) {
      const percentage = categoryData.total > 0 ? Math.round((faultyCount / categoryData.total) * 100) : 0;
      
      summary.problemAreas.push({
        category: category.charAt(0).toUpperCase() + category.slice(1),
        issues: faultyCount,
        percentage,
        priority: faultyCount >= 10 ? 'high' : faultyCount >= 5 ? 'medium' : 'low'
      });
    }
  });

  // Sort problem areas by faulty count (descending)
  summary.problemAreas.sort((a, b) => b.issues - a.issues);

  // Generate top issues (only faulty items)
  summary.topIssues = Object.entries(fieldIssueCount)
    .sort(([, a], [, b]) => b.count - a.count)
    .slice(0, 10)
    .map(([fieldKey, data]) => ({
      field: fieldKey,
      label: data.label,
      count: data.count,
      equipmentAffected: Array.from(data.equipment),
      category: data.category
    }));

  // Sort faulty equipment by urgency score (descending)
  summary.faultyEquipmentOverview.sort((a, b) => b.urgencyScore - a.urgencyScore);

  // Generate faulty-focused insights
  summary.insights = generateFaultyFocusedInsights(summary);

  return summary;
}

function getStatusFromValue(value: any): 'OK' | 'Faulty' | 'N/A' | 'Missing' | null {
  if (value === undefined || value === null || String(value).trim() === '') {
    return 'Missing';
  }
  
  if (typeof value === 'string' && ['OK', 'Faulty', 'N/A', 'Missing'].includes(value)) {
    return value as 'OK' | 'Faulty' | 'N/A' | 'Missing';
  }
  
  // For numeric values, consider them OK if they exist
  if (typeof value === 'number' || (typeof value === 'string' && !isNaN(Number(value)))) {
    return 'OK';
  }
  
  return 'N/A';
}

function determineOverallStatus(statuses: string[]): 'OK' | 'Faulty' {
  if (statuses.length === 0) return 'OK';
  
  // If any status is Faulty, the equipment is considered Faulty
  if (statuses.includes('Faulty')) {
    return 'Faulty';
  }
  
  // Otherwise, it's OK
  return 'OK';
}

function generateFaultyFocusedInsights(summary: BulkExportSummary): BulkExportSummary['insights'] {
  const criticalIssues = summary.faultyFocus.totalFaultyItems;
  const equipmentAtRisk = summary.faultyFocus.faultyEquipmentCount;
  const maintenanceRequired = summary.faultyFocus.urgentRepairsNeeded;
  
  const faultyPercentage = summary.faultyFocus.faultyPercentage;
  const overallHealth = faultyPercentage <= 5 ? 'Excellent' :
                       faultyPercentage <= 15 ? 'Good' :
                       faultyPercentage <= 30 ? 'Fair' : 'Poor';

  const recommendations: string[] = [];
  const urgentActions: string[] = [];
  const safetyRisks: string[] = [];
  
  // Generate urgent actions based on faulty items
  if (summary.faultyFocus.criticalEquipmentCount > 0) {
    urgentActions.push(`${summary.faultyFocus.criticalEquipmentCount} equipment units require immediate attention`);
  }
  
  if (summary.faultyFocus.urgentRepairsNeeded > 0) {
    urgentActions.push(`${summary.faultyFocus.urgentRepairsNeeded} urgent repairs needed within 24-48 hours`);
  }

  // Generate safety risks
  summary.topIssues.forEach(issue => {
    if (issue.label.toLowerCase().includes('safety') || 
        issue.label.toLowerCase().includes('emergency') ||
        issue.label.toLowerCase().includes('fire') ||
        issue.label.toLowerCase().includes('overheating')) {
      safetyRisks.push(`${issue.label}: ${issue.count} instances found`);
    }
  });

  // Generate recommendations based on problem areas
  if (summary.problemAreas.length > 0) {
    const topProblem = summary.problemAreas[0];
    recommendations.push(`Priority focus: ${topProblem.category.toLowerCase()} systems (${topProblem.issues} faulty items)`);
  }
  
  if (summary.faultyFocus.faultyPercentage > 20) {
    recommendations.push('Schedule comprehensive maintenance review');
    urgentActions.push('Escalate to senior maintenance team');
  }
  
  if (summary.faultyFocus.faultyPercentage > 10) {
    recommendations.push('Increase inspection frequency for affected equipment');
  }

  // Estimate repair time
  const estimatedRepairTime = summary.faultyFocus.totalFaultyItems <= 5 ? '1-2 days' :
                             summary.faultyFocus.totalFaultyItems <= 15 ? '3-5 days' :
                             summary.faultyFocus.totalFaultyItems <= 30 ? '1-2 weeks' : '2-4 weeks';

  // Don't add default recommendations - only specific actionable ones

  if (urgentActions.length === 0) {
    urgentActions.push('No urgent actions required at this time');
  }

  if (safetyRisks.length === 0) {
    safetyRisks.push('No immediate safety risks identified');
  }

  return {
    criticalIssues,
    equipmentAtRisk,
    maintenanceRequired,
    overallHealth,
    recommendations,
    urgentActions,
    safetyRisks,
    estimatedRepairTime
  };
}

/**
 * Generate SVG pie chart for statistics
 */
export function generateStatisticsPieChart(statistics: BulkExportSummary['statistics']): string {
  const { OK, Faulty, 'N/A': NA, Missing } = statistics;
  const total = statistics.total;
  
  if (total === 0) {
    return `
      <svg width="200" height="200" viewBox="0 0 200 200">
        <circle cx="100" cy="100" r="80" fill="#f8f9fa" stroke="#dee2e6" stroke-width="2"/>
        <text x="100" y="100" text-anchor="middle" dy="0.3em" font-size="14" fill="#6c757d">No Data</text>
      </svg>
    `;
  }
  
  const colors = {
    OK: '#28a745',
    Faulty: '#dc3545',
    'N/A': '#ffc107',
    Missing: '#6c757d'
  };
  
  let currentAngle = 0;
  const radius = 80;
  const centerX = 100;
  const centerY = 100;
  
  const slices = [
    { label: 'OK', value: OK, color: colors.OK },
    { label: 'Faulty', value: Faulty, color: colors.Faulty },
    { label: 'N/A', value: NA, color: colors['N/A'] },
    { label: 'Missing', value: Missing, color: colors.Missing }
  ].filter(slice => slice.value > 0);
  
  const pathElements = slices.map(slice => {
    const angle = (slice.value / total) * 360;
    const startAngle = currentAngle;
    const endAngle = currentAngle + angle;
    
    const x1 = centerX + radius * Math.cos((startAngle - 90) * Math.PI / 180);
    const y1 = centerY + radius * Math.sin((startAngle - 90) * Math.PI / 180);
    const x2 = centerX + radius * Math.cos((endAngle - 90) * Math.PI / 180);
    const y2 = centerY + radius * Math.sin((endAngle - 90) * Math.PI / 180);
    
    const largeArcFlag = angle > 180 ? 1 : 0;
    
    const pathData = [
      `M ${centerX} ${centerY}`,
      `L ${x1} ${y1}`,
      `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
      'Z'
    ].join(' ');
    
    currentAngle = endAngle;
    
    return `<path d="${pathData}" fill="${slice.color}" stroke="white" stroke-width="2"/>`;
  }).join('');
  
  return `
    <svg width="200" height="200" viewBox="0 0 200 200">
      ${pathElements}
    </svg>
  `;
}

/**
 * Paginate equipment overview for large datasets
 */
export function paginateEquipmentOverview(
  equipment: BulkExportSummary['equipmentOverview'],
  itemsPerPage: number = 200
): BulkExportSummary['equipmentOverview'][] {
  const pages: BulkExportSummary['equipmentOverview'][] = [];
  
  for (let i = 0; i < equipment.length; i += itemsPerPage) {
    pages.push(equipment.slice(i, i + itemsPerPage));
  }
  
  return pages;
} 
/**
 * PDF Compression Configuration
 * 
 * This file contains all PDF compression settings that can be easily adjusted
 * for different deployment environments or performance requirements.
 */

export interface PDFCompressionConfig {
  level: 'low' | 'medium' | 'high';
  puppeteerOptions: {
    tagged: boolean;
    displayHeaderFooter: boolean;
    headerTemplate: string;
    footerTemplate: string;
    omitBackground: boolean;
    preferCSSPageSize: boolean;
  };
  pdfLibOptions: {
    useObjectStreams: boolean;
    addDefaultPage: boolean;
    objectsPerTick: number;
    updateFieldAppearances: boolean;
  };
}

// Default compression settings for Firebase cloud functions
export const DEFAULT_COMPRESSION_CONFIG: Record<'low' | 'medium' | 'high', PDFCompressionConfig> = {
  low: {
    level: 'low',
    puppeteerOptions: {
      tagged: true,
      displayHeaderFooter: false,
      headerTemplate: '',
      footerTemplate: '',
      omitBackground: false,
      preferCSSPageSize: true
    },
    pdfLibOptions: {
      useObjectStreams: true,
      addDefaultPage: false,
      objectsPerTick: 100,
      updateFieldAppearances: false
    }
  },
  medium: {
    level: 'medium',
    puppeteerOptions: {
      tagged: false,
      displayHeaderFooter: false,
      headerTemplate: '',
      footerTemplate: '',
      omitBackground: false,
      preferCSSPageSize: false
    },
    pdfLibOptions: {
      useObjectStreams: false,
      addDefaultPage: false,
      objectsPerTick: 50,
      updateFieldAppearances: false
    }
  },
  high: {
    level: 'high',
    puppeteerOptions: {
      tagged: false,
      displayHeaderFooter: false,
      headerTemplate: '',
      footerTemplate: '',
      omitBackground: false,
      preferCSSPageSize: false
    },
    pdfLibOptions: {
      useObjectStreams: false,
      addDefaultPage: false,
      objectsPerTick: 25,
      updateFieldAppearances: false
    }
  }
};

// Alternative configurations for different scenarios
export const COMPRESSION_PRESETS = {
  // For very fast processing with minimal compression
  speed: {
    level: 'low' as const,
    puppeteerOptions: {
      tagged: true,
      displayHeaderFooter: false,
      headerTemplate: '',
      footerTemplate: '',
      omitBackground: false,
      preferCSSPageSize: true
    },
    pdfLibOptions: {
      useObjectStreams: true,
      addDefaultPage: false,
      objectsPerTick: 200,
      updateFieldAppearances: false
    }
  },

  // Balanced approach (default)
  balanced: DEFAULT_COMPRESSION_CONFIG.medium,

  // For maximum compression when storage/bandwidth is critical
  maxCompression: {
    level: 'high' as const,
    puppeteerOptions: {
      tagged: false,
      displayHeaderFooter: false,
      headerTemplate: '',
      footerTemplate: '',
      omitBackground: true,
      preferCSSPageSize: false
    },
    pdfLibOptions: {
      useObjectStreams: false,
      addDefaultPage: false,
      objectsPerTick: 10,
      updateFieldAppearances: false
    }
  }
} as const;

// Application settings
export const PDF_COMPRESSION_SETTINGS = {
  // Default compression level for bulk exports
  bulkExportDefault: 'medium' as const,
  
  // Default compression level for individual exports
  individualExportDefault: 'medium' as const,
  
  // Whether to log compression statistics
  enableCompressionLogging: true,
  
  // Fallback to original PDF if compression fails
  fallbackOnError: true,
  
  // Maximum compression time before timeout (in ms)
  compressionTimeoutMs: 30000,
  
  // Minimum compression ratio to consider successful (in %)
  minCompressionRatio: 5,
  
  // Memory usage optimization
  memoryOptimization: {
    enabled: true,
    maxConcurrentCompressions: 2,
    gcAfterCompression: true
  }
};

/**
 * Get compression configuration based on file size
 */
export function getCompressionConfigBySize(fileSizeBytes: number): PDFCompressionConfig {
  // For small files (< 1MB), use low compression to save processing time
  if (fileSizeBytes < 1024 * 1024) {
    return DEFAULT_COMPRESSION_CONFIG.low;
  }
  
  // For medium files (1MB - 5MB), use medium compression
  if (fileSizeBytes < 5 * 1024 * 1024) {
    return DEFAULT_COMPRESSION_CONFIG.medium;
  }
  
  // For large files (> 5MB), use high compression
  return DEFAULT_COMPRESSION_CONFIG.high;
}

/**
 * Get compression configuration based on export type
 */
export function getCompressionConfigByType(exportType: 'individual' | 'bulk'): PDFCompressionConfig {
  if (exportType === 'individual') {
    return DEFAULT_COMPRESSION_CONFIG[PDF_COMPRESSION_SETTINGS.individualExportDefault];
  }
  
  return DEFAULT_COMPRESSION_CONFIG[PDF_COMPRESSION_SETTINGS.bulkExportDefault];
}

/**
 * Validate if compression was successful
 */
export function isCompressionSuccessful(
  originalSize: number, 
  compressedSize: number
): boolean {
  const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100;
  return compressionRatio >= PDF_COMPRESSION_SETTINGS.minCompressionRatio;
}

/**
 * Calculate compression statistics
 */
export function calculateCompressionStats(
  originalSize: number, 
  compressedSize: number
) {
  const compressionRatio = ((originalSize - compressedSize) / originalSize) * 100;
  const compressionFactor = originalSize / compressedSize;
  
  return {
    originalSize,
    compressedSize,
    compressionRatio: parseFloat(compressionRatio.toFixed(2)),
    compressionFactor: parseFloat(compressionFactor.toFixed(2)),
    spaceSaved: originalSize - compressedSize,
    isSuccessful: compressionRatio >= PDF_COMPRESSION_SETTINGS.minCompressionRatio
  };
} 
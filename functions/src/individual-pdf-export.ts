import { onRequest } from 'firebase-functions/v2/https';
import { logger } from 'firebase-functions';
import * as admin from 'firebase-admin';
import { getStorage } from 'firebase-admin/storage';
import puppeteer from 'puppeteer-core';
import chromium from '@sparticuz/chromium';

// Import our Cloud Function PDF template
import { generatePDFHTML } from './pdf-template-cf';

// PDF generation imports
import { PDFDocument } from 'pdf-lib';

interface ChecklistData {
  id: string;
  userId?: string; // Optional userId field for ownership verification
  generalInfo: {
    clientName: string;
    building: string;
    equipmentName: string;
    location: string;
    tagNo: string;
    date: string;
    ppmAttempt: string;
    inspectedBy: string;
    approvedBy: string;
  };
  mechanicalChecks: Record<string, any>;
  electricalChecks: Record<string, any>;
  sequenceControlsChecks: Record<string, any>;
  remarks?: string;
  beforeImage?: string;
  afterImage?: string;
  signature?: string;
  isCompleted: boolean;
}

interface GeneratePDFRequest {
  checklistId: string;
}

interface GeneratePDFResponse {
  success: boolean;
  data?: {
    downloadUrl: string;
    fileName: string;
    fileSize: number;
    expiresAt: string;
  };
  error?: string;
}

// Image cache for performance
const imageCache = new Map<string, string>();

/**
 * HTTP Cloud Function to generate individual PDF with proper CORS support
 * Replaces client-side PDF generation with server-side Puppeteer
 */
export const generateIndividualPDFHttp = onRequest({
  region: 'asia-east1',
  memory: '1GiB',
  timeoutSeconds: 300,
  cors: true
}, async (request, response) => {
  try {
    // Handle preflight requests
    if (request.method === 'OPTIONS') {
      response.set('Access-Control-Allow-Origin', request.get('Origin') || '*');
      response.set('Access-Control-Allow-Methods', 'POST, OPTIONS');
      response.set('Access-Control-Allow-Headers', 'Content-Type, Authorization, X-Firebase-AppCheck');
      response.set('Access-Control-Max-Age', '3600');
      response.status(204).send('');
      return;
    }

    // Only allow POST requests
    if (request.method !== 'POST') {
      response.status(405).json({ success: false, error: 'Method not allowed' });
      return;
    }

    // Verify authentication
    const authHeader = request.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      response.status(401).json({ success: false, error: 'Authentication required' });
      return;
    }

    const idToken = authHeader.split('Bearer ')[1];
    const decodedToken = await admin.auth().verifyIdToken(idToken);
    
    // Get request data
    const data: GeneratePDFRequest = request.body;
    const { checklistId } = data;

    // Validate input
    if (!checklistId) {
      response.status(400).json({ success: false, error: 'Checklist ID is required' });
      return;
    }

    logger.info('Starting individual PDF generation', {
      checklistId,
      userId: decodedToken.uid
    });

    // Get checklist data
    const checklistDoc = await admin.firestore()
      .collection('checklists')
      .doc(checklistId)
      .get();
    
    if (!checklistDoc.exists) {
      response.status(404).json({ success: false, error: 'Checklist not found' });
      return;
    }

    const checklistData = checklistDoc.data() as ChecklistData;
    
    logger.info('Processing PDF generation request', {
      checklistId,
      userId: decodedToken.uid,
      equipmentName: checklistData.generalInfo?.equipmentName,
      tagNo: checklistData.generalInfo?.tagNo
    });

    // Pre-load images to cache
    await preloadImages([checklistData]);

    // Generate PDF using Puppeteer
    const pdfBytes = await generatePDFWithPuppeteer(checklistData);

    // Upload to Firebase Storage
    const storageResult = await uploadToStorage(
      pdfBytes,
      decodedToken.uid,
      checklistId
    );

    logger.info('Individual PDF generated successfully', {
      checklistId,
      userId: decodedToken.uid,
      fileSize: pdfBytes.length,
      downloadUrl: storageResult.downloadUrl
    });

    const result: GeneratePDFResponse = {
      success: true,
      data: {
        downloadUrl: storageResult.downloadUrl,
        fileName: storageResult.fileName,
        fileSize: pdfBytes.length,
        expiresAt: storageResult.expiresAt.toISOString()
      }
    };

    // Set CORS headers
    response.set('Access-Control-Allow-Origin', request.get('Origin') || '*');
    response.set('Access-Control-Allow-Credentials', 'true');
    response.json(result);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
    
    logger.error('Failed to generate individual PDF', {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined
    });

    const result: GeneratePDFResponse = {
      success: false,
      error: `Failed to generate PDF: ${errorMessage}`
    };

    response.set('Access-Control-Allow-Origin', request.get('Origin') || '*');
    response.status(500).json(result);
  }
});

// Import compression configuration
import { 
  DEFAULT_COMPRESSION_CONFIG, 
  PDF_COMPRESSION_SETTINGS,
  calculateCompressionStats,
  isCompressionSuccessful
} from './utils/pdf-compression-config';

/**
 * Compress PDF using PDF-lib with configurable compression settings
 */
async function compressPDF(
  pdfBytes: Uint8Array, 
  compressionLevel: 'low' | 'medium' | 'high' = 'medium',
  exportType: 'individual' | 'bulk' = 'individual'
): Promise<Uint8Array> {
  try {
    const config = DEFAULT_COMPRESSION_CONFIG[compressionLevel];
    
    if (PDF_COMPRESSION_SETTINGS.enableCompressionLogging) {
      logger.info('Starting PDF compression', { 
        originalSize: pdfBytes.length,
        compressionLevel,
        exportType
      });
    }

    const pdfDoc = await PDFDocument.load(pdfBytes);
    const compressedBytes = await pdfDoc.save(config.pdfLibOptions);
    
    // Calculate compression statistics
    const stats = calculateCompressionStats(pdfBytes.length, compressedBytes.length);
    
    if (PDF_COMPRESSION_SETTINGS.enableCompressionLogging) {
      logger.info('PDF compression completed', {
        ...stats,
        compressionLevel,
        exportType
      });
    }

    // Check if compression was successful
    if (!isCompressionSuccessful(pdfBytes.length, compressedBytes.length)) {
      logger.warn('PDF compression ratio below threshold, but keeping compressed version', {
        compressionRatio: stats.compressionRatio,
        minRatio: PDF_COMPRESSION_SETTINGS.minCompressionRatio
      });
    }

    // Force garbage collection if enabled
    if (PDF_COMPRESSION_SETTINGS.memoryOptimization.gcAfterCompression && global.gc) {
      global.gc();
    }

    return compressedBytes;
  } catch (error) {
    logger.error('PDF compression failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      originalSize: pdfBytes.length,
      compressionLevel,
      exportType
    });
    
    // Return original if compression fails and fallback is enabled
    if (PDF_COMPRESSION_SETTINGS.fallbackOnError) {
      logger.info('Returning original PDF due to compression failure');
      return pdfBytes;
    }
    
    throw error;
  }
}

/**
 * Generate PDF using Puppeteer with compression optimization
 */
async function generatePDFWithPuppeteer(checklist: ChecklistData): Promise<Uint8Array> {
  let browser;
  
  try {
    // Launch browser
    browser = await puppeteer.launch({
      args: chromium.args,
      defaultViewport: chromium.defaultViewport,
      executablePath: await chromium.executablePath(),
      headless: chromium.headless,
      ignoreHTTPSErrors: true,
    });

    const page = await browser.newPage();
    
    try {
      // Set viewport for consistent rendering
      await page.setViewport({ width: 794, height: 1123 }); // A4 dimensions

      // Get cached images
      const logoDataUrl = imageCache.get('logo') || '';
      const beforeImageDataUrl = checklist.beforeImage ? imageCache.get(checklist.beforeImage) : undefined;
      const afterImageDataUrl = checklist.afterImage ? imageCache.get(checklist.afterImage) : undefined;
      const companySignatureDataUrl = imageCache.get('signature') || '';

      // Generate HTML using our template function
      const htmlContent = generatePDFHTML({
        checklist,
        logoDataUrl,
        beforeImageDataUrl,
        afterImageDataUrl,
        signatureDataUrl: companySignatureDataUrl
      });

      // Set content and generate PDF with compression optimizations
      await page.setContent(htmlContent, { waitUntil: 'networkidle0' });
      
      const pdfBuffer = await page.pdf({
        format: 'a4',
        printBackground: true,
        preferCSSPageSize: false,
        margin: {
          top: '5mm',
          right: '10mm',
          bottom: '10mm',
          left: '10mm'
        },
        // Compression optimizations
        tagged: false,
        displayHeaderFooter: false,
        headerTemplate: '',
        footerTemplate: '',
        omitBackground: false
      });

      const rawPdfBytes = new Uint8Array(pdfBuffer);
      
      // Apply additional PDF compression for individual export
      const compressedPdfBytes = await compressPDF(rawPdfBytes, 'medium', 'individual');
      
      return compressedPdfBytes;
      
    } finally {
      await page.close();
    }
    
  } finally {
    if (browser) {
      await browser.close();
    }
  }
}

/**
 * Pre-load company assets and checklist images to cache
 */
async function preloadImages(checklists: ChecklistData[]): Promise<void> {
  try {
    // Load company assets first (same approach as bulk export)
    const logoDataUrl = await loadCompanyAssetAsDataUrl('logo');
    if (logoDataUrl) {
      imageCache.set('logo', logoDataUrl);
    }
    
    const signatureDataUrl = await loadCompanyAssetAsDataUrl('signature');
    if (signatureDataUrl) {
      imageCache.set('signature', signatureDataUrl);
    }

    // Collect all unique image URLs from checklists
    const imageUrls = new Set<string>();
    checklists.forEach(checklist => {
      if (checklist.beforeImage) imageUrls.add(checklist.beforeImage);
      if (checklist.afterImage) imageUrls.add(checklist.afterImage);
      if (checklist.signature) imageUrls.add(checklist.signature);
    });

    // Load all checklist images in parallel using proxy for Firebase Storage images
    const imagePromises = Array.from(imageUrls).map(async (url) => {
      if (!imageCache.has(url)) {
        try {
          // Use proxy endpoint for checklist images (same as bulk export)
          const proxyUrl = `https://asia-east1-auburn-engineering.cloudfunctions.net/proxyImage?url=${encodeURIComponent(url)}`;
          const response = await fetch(proxyUrl);
          
          if (response.ok) {
            const buffer = await response.arrayBuffer();
            const base64 = Buffer.from(buffer).toString('base64');
            const contentType = response.headers.get('content-type') || 'image/jpeg';
            const dataUrl = `data:${contentType};base64,${base64}`;
            imageCache.set(url, dataUrl);
            logger.info('Successfully loaded checklist image via proxy', { url, dataUrlLength: dataUrl.length });
          } else {
            logger.warn('Failed to load image via proxy:', { url, status: response.status });
          }
        } catch (error) {
          logger.warn('Failed to preload checklist image:', { url, error: error instanceof Error ? error.message : 'Unknown' });
        }
      }
    });

    await Promise.all(imagePromises);
    logger.info('Images preloaded to cache', { 
      totalCached: imageCache.size,
      logoLoaded: imageCache.has('logo'),
      signatureLoaded: imageCache.has('signature')
    });

  } catch (error) {
    logger.warn('Some images failed to preload', { error: error instanceof Error ? error.message : 'Unknown error' });
  }
}

/**
 * Load company asset as data URL using Firebase Admin SDK (same as bulk export)
 */
async function loadCompanyAssetAsDataUrl(assetKey: 'logo' | 'signature'): Promise<string> {
  try {
    // Use correct paths (same as bulk export)
    const bucket = getStorage().bucket();
    const fileName = assetKey === 'logo' ? 'assets/logo.jpeg' : 'assets/signature.png';
    const file = bucket.file(fileName);
    
    logger.info(`Loading ${assetKey} using Firebase Admin SDK`, { fileName });
    
    // Check if file exists
    const [exists] = await file.exists();
    if (!exists) {
      throw new Error(`File not found: ${fileName}`);
    }
    
    // Download file content
    const [buffer] = await file.download();
    
    // Get file metadata for content type
    const [metadata] = await file.getMetadata();
    const contentType = metadata.contentType || (assetKey === 'logo' ? 'image/jpeg' : 'image/png');
    
    // Convert to data URL
    const base64 = buffer.toString('base64');
    const dataUrl = `data:${contentType};base64,${base64}`;
    
    logger.info(`Successfully loaded ${assetKey} using Admin SDK`, { 
      bufferSize: buffer.length,
      contentType,
      dataUrlLength: dataUrl.length
    });
    
    return dataUrl;
    
  } catch (error) {
    logger.error(`Failed to load ${assetKey} using Admin SDK`, {
      error: error instanceof Error ? error.message : 'Unknown error',
      assetKey
    });
    
    // Create placeholder image (same as bulk export)
    try {
      const placeholder = createPlaceholderImageDataUrl(assetKey);
      logger.warn(`Using placeholder for ${assetKey}`, { placeholderLength: placeholder.length });
      return placeholder;
    } catch (placeholderError) {
      logger.error(`Failed to create placeholder for ${assetKey}`, { placeholderError });
      return '';
    }
  }
}

/**
 * Create a simple placeholder image data URL (same as bulk export)
 */
function createPlaceholderImageDataUrl(assetKey: 'logo' | 'signature'): string {
  // Create a simple SVG placeholder
  const isLogo = assetKey === 'logo';
  const text = isLogo ? 'AUBURN ENGINEERING' : 'SIGNATURE';
  const width = isLogo ? 200 : 150;
  const height = isLogo ? 60 : 40;
  
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#f0f0f0" stroke="#ccc" stroke-width="1"/>
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="12" 
            text-anchor="middle" dominant-baseline="middle" fill="#666">
        ${text}
      </text>
    </svg>
  `.trim();
  
  const base64 = Buffer.from(svg).toString('base64');
  return `data:image/svg+xml;base64,${base64}`;
}

/**
 * Upload PDF to Firebase Storage
 */
async function uploadToStorage(
  pdfBytes: Uint8Array,
  userId: string,
  checklistId: string
): Promise<{
  downloadUrl: string;
  fileName: string;
  expiresAt: Date;
}> {
  try {
    const bucket = getStorage().bucket();
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `individual-pdf-${checklistId}-${timestamp}.pdf`;
    const storagePath = `exports/${userId}/individual/${fileName}`;
    
    const file = bucket.file(storagePath);
    
    // Upload file
    await file.save(Buffer.from(pdfBytes), {
      metadata: {
        contentType: 'application/pdf',
        metadata: {
          userId,
          checklistId,
          type: 'individual-pdf',
          createdAt: new Date().toISOString()
        }
      }
    });
    
    // Make file publicly readable
    await file.makePublic();
    
    // Set expiration (24 hours for individual PDFs)
    const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000);
    
    // Generate public download URL
    const downloadUrl = `https://storage.googleapis.com/${bucket.name}/${storagePath}`;
    
    logger.info('Individual PDF uploaded to storage', {
      storagePath,
      fileName,
      fileSize: pdfBytes.length,
      downloadUrl,
      expiresAt: expiresAt.toISOString()
    });
    
    return {
      downloadUrl,
      fileName,
      expiresAt
    };
    
  } catch (error) {
    logger.error('Storage upload failed', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
} 
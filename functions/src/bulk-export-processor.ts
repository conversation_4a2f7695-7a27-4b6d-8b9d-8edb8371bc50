import { onDocumentCreated } from 'firebase-functions/v2/firestore';
import { logger } from 'firebase-functions';
import * as admin from 'firebase-admin';
import { getStorage } from 'firebase-admin/storage';
import puppeteer, { <PERSON><PERSON><PERSON> } from 'puppeteer-core';
import chromium from '@sparticuz/chromium';

// PDF generation imports
import { PDFDocument } from 'pdf-lib';

// Import our Cloud Function PDF template
import { generatePDFHTML, generateBulkPDFSummaryHTML } from './pdf-template-cf';

// Import analytics utility
import { analyzeBulkExportData, paginateEquipmentOverview } from './utils/bulk-export-analytics';

// Types
interface ExportQueueItem {
  id: string;
  userId: string;
  type: 'pdf' | 'excel';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  checklistIds: string[];
  checklistCount: number;
  createdAt: admin.firestore.Timestamp;
  startedAt?: admin.firestore.Timestamp;
  completedAt?: admin.firestore.Timestamp;
  progress: {
    currentIndex: number;
    totalCount: number;
    percentage: number;
    stage: 'queued' | 'generating' | 'merging' | 'uploading' | 'complete';
    currentChecklist?: string;
    estimatedTimeRemaining?: number;
    processingSpeed?: number;
    averageTimePerItem?: number;
  };
  metadata: {
    clientName?: string;
    dateRange?: string;
    exportMethod: 'cloud-function';
    requestedBy: string;
  };
}

interface ChecklistData {
  id: string;
  generalInfo: {
    clientName: string;
    building: string;
    equipmentName: string;
    location: string;
    tagNo: string;
    date: string;
    ppmAttempt: string;
    inspectedBy: string;
    approvedBy: string;
  };
  mechanicalChecks: Record<string, any>;
  electricalChecks: Record<string, any>;
  sequenceControlsChecks: Record<string, any>;
  remarks?: string;
  beforeImage?: string;
  afterImage?: string;
  signature?: string;
  isCompleted: boolean;
}

// Global browser instance for reuse
let globalBrowser: Browser | null = null;
let browserInitPromise: Promise<Browser> | null = null;

// Image cache to avoid repeated downloads
const imageCache = new Map<string, string>();

/**
 * Cloud Function triggered when a new export queue item is created
 */
export const processBulkExport = onDocumentCreated({
  document: 'export-queue/{queueId}',
  memory: '8GiB', // Increased memory for parallel processing
  timeoutSeconds: 540, // 9 minutes
  region: 'asia-east1',
  concurrency: 1 // Limit concurrent executions to avoid resource conflicts
}, async (event) => {
  const queueId = event.params.queueId;
  const queueData = event.data?.data() as ExportQueueItem;
  
  if (!queueData) {
    logger.error('No queue data found', { queueId });
    return;
  }

  logger.info('Processing bulk export', { 
    queueId, 
    userId: queueData.userId, 
    type: queueData.type,
    checklistCount: queueData.checklistCount 
  });

  try {
    // Update status to processing with start time
    await updateQueueStatus(queueId, 'processing', {
      currentIndex: 0,
      totalCount: queueData.checklistCount,
      percentage: 0,
      stage: 'generating'
    }, true); // Set startedAt timestamp

    // Fetch checklist data with optimized batching
    const checklists = await fetchChecklistData(queueData.checklistIds, queueData.userId);
    
    if (checklists.length === 0) {
      throw new Error('No valid checklists found');
    }

    // Generate export based on type
    let exportResult;
    if (queueData.type === 'pdf') {
      exportResult = await generateBulkPDF(queueId, checklists, queueData);
    } else {
      exportResult = await generateBulkExcel(queueId, checklists, queueData);
    }

    // Upload to Firebase Storage
    await updateQueueStatus(queueId, 'processing', {
      currentIndex: checklists.length,
      totalCount: checklists.length,
      percentage: 90,
      stage: 'uploading'
    });

    const storageResult = await uploadToStorage(
      exportResult.fileBytes,
      queueData.userId,
      queueId,
      queueData.type,
      {
        checklistCount: checklists.length,
        clientName: queueData.metadata.clientName,
        dateRange: queueData.metadata.dateRange,
        requestedBy: queueData.metadata.requestedBy
      }
    );

    // Complete the queue item
    await completeQueueItem(queueId, {
      ...exportResult,
      downloadUrl: storageResult.downloadUrl,
      fileName: storageResult.fileName,
      storagePath: storageResult.storagePath,
      fileSize: exportResult.fileBytes.length,
      expiresAt: storageResult.expiresAt
    });

    logger.info('Bulk export completed successfully', {
      queueId,
      type: queueData.type,
      processedCount: exportResult.processedCount,
      failedCount: exportResult.failedCount,
      fileSize: exportResult.fileBytes.length
    });

  } catch (error) {
    logger.error('Bulk export failed', { queueId, error });
    await failQueueItem(queueId, error instanceof Error ? error.message : 'Unknown error');
  } finally {
    // Clean up browser instance if this is the last operation
    await cleanupBrowser();
  }
});

/**
 * Get or create shared browser instance
 */
async function getSharedBrowser(): Promise<Browser> {
  if (globalBrowser && globalBrowser.isConnected()) {
    return globalBrowser;
  }

  // If browser initialization is already in progress, wait for it
  if (browserInitPromise) {
    return browserInitPromise;
  }

  browserInitPromise = puppeteer.launch({
    args: [
      ...chromium.args,
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-zygote',
      '--single-process',
      '--disable-gpu',
      '--disable-web-security',
      '--disable-features=VizDisplayCompositor',
      '--disable-background-timer-throttling',
      '--disable-backgrounding-occluded-windows',
      '--disable-renderer-backgrounding',
      '--max-old-space-size=4096' // Increase memory limit
    ],
    defaultViewport: { width: 1280, height: 720 },
    executablePath: await chromium.executablePath(),
    headless: 'new',
  });

  globalBrowser = await browserInitPromise;
  browserInitPromise = null;
  
  logger.info('Shared browser instance created');
  return globalBrowser;
}

/**
 * Clean up browser instance
 */
async function cleanupBrowser(): Promise<void> {
  if (globalBrowser) {
    try {
      await globalBrowser.close();
      globalBrowser = null;
      logger.info('Browser instance cleaned up');
    } catch (error) {
      logger.warn('Error cleaning up browser:', error);
    }
  }
}

/**
 * Pre-load and cache all images for the batch
 */
async function preloadImages(checklists: ChecklistData[]): Promise<void> {
  const imageUrls = new Set<string>();
  
  // Collect all unique image URLs
  checklists.forEach(checklist => {
    if (checklist.beforeImage) imageUrls.add(checklist.beforeImage);
    if (checklist.afterImage) imageUrls.add(checklist.afterImage);
    if (checklist.signature) imageUrls.add(checklist.signature);
  });

  // Load company assets once
  const logoDataUrl = await loadCompanyAssetAsDataUrl('logo');
  if (logoDataUrl) {
    imageCache.set('logo', logoDataUrl);
  }
  
  const signatureDataUrl = await loadCompanyAssetAsDataUrl('signature');
  if (signatureDataUrl) {
    imageCache.set('signature', signatureDataUrl);
  }

  // Load all images in parallel using proxy for checklist images
  const imagePromises = Array.from(imageUrls).map(async (url) => {
    if (!imageCache.has(url)) {
      try {
        // Use proxy endpoint for checklist images (App Check protected)
        const proxyUrl = `https://asia-east1-auburn-engineering.cloudfunctions.net/proxyImage?url=${encodeURIComponent(url)}`;
        const response = await fetch(proxyUrl);
        
        if (response.ok) {
          const buffer = await response.arrayBuffer();
          const base64 = Buffer.from(buffer).toString('base64');
          const contentType = response.headers.get('content-type') || 'image/jpeg';
          const dataUrl = `data:${contentType};base64,${base64}`;
          imageCache.set(url, dataUrl);
        } else {
          logger.warn('Failed to load image via proxy:', { url, status: response.status });
        }
      } catch (error) {
        logger.warn('Failed to preload image:', url, error);
      }
    }
  });

  await Promise.all(imagePromises);
  logger.info('Preloaded images', { count: imageCache.size });
}

// Import compression configuration
import { 
  DEFAULT_COMPRESSION_CONFIG, 
  PDF_COMPRESSION_SETTINGS,
  calculateCompressionStats,
  isCompressionSuccessful
} from './utils/pdf-compression-config';

/**
 * Compress PDF using PDF-lib with configurable compression settings
 */
async function compressPDF(
  pdfBytes: Uint8Array, 
  compressionLevel: 'low' | 'medium' | 'high' = 'medium',
  exportType: 'individual' | 'bulk' = 'bulk'
): Promise<Uint8Array> {
  try {
    const config = DEFAULT_COMPRESSION_CONFIG[compressionLevel];
    
    if (PDF_COMPRESSION_SETTINGS.enableCompressionLogging) {
      logger.info('Starting PDF compression', { 
        originalSize: pdfBytes.length,
        compressionLevel,
        exportType
      });
    }

    const pdfDoc = await PDFDocument.load(pdfBytes);
    const compressedBytes = await pdfDoc.save(config.pdfLibOptions);
    
    // Calculate compression statistics
    const stats = calculateCompressionStats(pdfBytes.length, compressedBytes.length);
    
    if (PDF_COMPRESSION_SETTINGS.enableCompressionLogging) {
      logger.info('PDF compression completed', {
        ...stats,
        compressionLevel,
        exportType
      });
    }

    // Check if compression was successful
    if (!isCompressionSuccessful(pdfBytes.length, compressedBytes.length)) {
      logger.warn('PDF compression ratio below threshold, but keeping compressed version', {
        compressionRatio: stats.compressionRatio,
        minRatio: PDF_COMPRESSION_SETTINGS.minCompressionRatio
      });
    }

    // Force garbage collection if enabled
    if (PDF_COMPRESSION_SETTINGS.memoryOptimization.gcAfterCompression && global.gc) {
      global.gc();
    }

    return compressedBytes;
  } catch (error) {
    logger.error('PDF compression failed', {
      error: error instanceof Error ? error.message : 'Unknown error',
      originalSize: pdfBytes.length,
      compressionLevel,
      exportType
    });
    
    // Return original if compression fails and fallback is enabled
    if (PDF_COMPRESSION_SETTINGS.fallbackOnError) {
      logger.info('Returning original PDF due to compression failure');
      return pdfBytes;
    }
    
    throw error;
  }
}

/**
 * Generate bulk PDF using optimized parallel processing with consistent field ordering and summary overlay
 */
async function generateBulkPDF(
  queueId: string, 
  checklists: ChecklistData[], 
  queueData: ExportQueueItem
): Promise<{
  fileBytes: Uint8Array;
  processedCount: number;
  failedCount: number;
  failedChecklists: string[];
}> {
  const result = {
    fileBytes: new Uint8Array(),
    processedCount: 0,
    failedCount: 0,
    failedChecklists: [] as string[]
  };

  try {
    // Pre-load all images to cache
    await preloadImages(checklists);

    // Get shared browser instance
    const browser = await getSharedBrowser();
    
    // Create merged PDF document
    const mergedPdf = await PDFDocument.create();
    
    // Add metadata
    mergedPdf.setTitle(`Auburn Engineering - Bulk PPM Report (${checklists.length} Items)`);
    mergedPdf.setAuthor('Auburn Engineering');
    mergedPdf.setCreationDate(new Date());
    mergedPdf.setSubject('Preventive Maintenance Inspection Summary');

    logger.info('Starting bulk PDF generation with summary overlay', { 
      totalChecklists: checklists.length,
      includeSummary: true
    });

    // STEP 1: Generate Summary Pages
    await updateQueueStatus(queueId, 'processing', {
      currentIndex: 0,
      totalCount: checklists.length,
      percentage: 5,
      stage: 'generating',
      currentChecklist: 'Generating summary overlay...'
    });

    // Analyze data for summary
    const summaryData = analyzeBulkExportData(checklists, queueData.metadata);
    logger.info('Summary data analyzed', {
      totalChecklists: summaryData.totalChecklists,
      overallHealth: summaryData.insights.overallHealth,
      criticalIssues: summaryData.insights.criticalIssues
    });

    // Get logo for summary pages
    const logoDataUrl = imageCache.get('logo') || '';

    // Sort equipment to prioritize faulty items first, then generate summary pages
    const sortedEquipment = summaryData.equipmentOverview.sort((a, b) => {
      // First sort by faulty status (faulty equipment first)
      if (a.overallStatus === 'Faulty' && b.overallStatus !== 'Faulty') return -1;
      if (a.overallStatus !== 'Faulty' && b.overallStatus === 'Faulty') return 1;
      
      // Then sort by issue count (higher issues first)
      if (a.issueCount !== b.issueCount) return b.issueCount - a.issueCount;
      
      // Finally sort by completion percentage (lower completion first)
      return a.completionPercentage - b.completionPercentage;
    });
    
    // Generate summary pages (paginated if needed)
    const equipmentPages = paginateEquipmentOverview(sortedEquipment, 200);
    const totalSummaryPages = equipmentPages.length;

    logger.info('Generating summary pages', { 
      totalSummaryPages,
      equipmentCount: summaryData.equipmentOverview.length 
    });

    for (let pageIndex = 0; pageIndex < totalSummaryPages; pageIndex++) {
      const equipmentPage = equipmentPages[pageIndex];
      const pageNumber = pageIndex + 1;
      
      const summaryHTML = generateBulkPDFSummaryHTML({
        summary: summaryData,
        logoDataUrl,
        pageNumber,
        totalPages: totalSummaryPages,
        equipmentPage
      });

      // Generate summary page PDF
      const summaryPagePDF = await generateSummaryPagePDF(browser, summaryHTML);
      const summaryPdf = await PDFDocument.load(summaryPagePDF);
      const summaryPages = await mergedPdf.copyPages(summaryPdf, summaryPdf.getPageIndices());
      summaryPages.forEach(page => mergedPdf.addPage(page));

      logger.info(`Summary page ${pageNumber}/${totalSummaryPages} generated`);
    }

    // STEP 2: Generate Individual Checklist PDFs
    await updateQueueStatus(queueId, 'processing', {
      currentIndex: 0,
      totalCount: checklists.length,
      percentage: 15,
      stage: 'generating',
      currentChecklist: 'Starting individual checklist generation...'
    });

    // Process checklists in parallel batches
    const parallelBatchSize = Math.min(8, Math.max(4, Math.floor(checklists.length / 25)));
    const batches: ChecklistData[][] = [];
    
    for (let i = 0; i < checklists.length; i += parallelBatchSize) {
      batches.push(checklists.slice(i, i + parallelBatchSize));
    }

    logger.info('Processing individual PDFs in parallel', { 
      totalChecklists: checklists.length, 
      batches: batches.length, 
      parallelBatchSize 
    });

    let itemsProcessed = 0;
    const startTime = Date.now();

    for (let batchIndex = 0; batchIndex < batches.length; batchIndex++) {
      const batch = batches[batchIndex];
      
      const batchPromises = batch.map(async (checklist, index) => {
        const globalIndex = batchIndex * parallelBatchSize + index;
        
        try {
          // Calculate progress metrics (15% to 85% for individual PDFs)
          const baseProgress = 15;
          const maxProgress = 85;
          const currentProgress = baseProgress + ((itemsProcessed / checklists.length) * (maxProgress - baseProgress));
          const elapsed = Date.now() - startTime;
          const processingSpeed = itemsProcessed > 0 ? (itemsProcessed / elapsed) * 60000 : 0;
          const averageTimePerItem = itemsProcessed > 0 ? elapsed / itemsProcessed / 1000 : 0;
          const estimatedTimeRemaining = itemsProcessed > 0 ? ((checklists.length - itemsProcessed) * averageTimePerItem) : null;

          if (globalIndex % 5 === 0) {
            const progressUpdate: any = {
              currentIndex: itemsProcessed,
              totalCount: checklists.length,
              percentage: Math.round(currentProgress),
              stage: 'generating',
              currentChecklist: `${checklist.generalInfo.tagNo} - ${checklist.generalInfo.equipmentName}`,
              processingSpeed: Math.round(processingSpeed * 10) / 10,
              averageTimePerItem: Math.round(averageTimePerItem * 10) / 10
            };
            
            if (estimatedTimeRemaining !== null) {
              progressUpdate.estimatedTimeRemaining = estimatedTimeRemaining;
            }
            
            await updateQueueStatus(queueId, 'processing', progressUpdate);
          }

          // Generate individual PDF
          const pdfBytes = await generateIndividualPDFWithSharedBrowser(browser, checklist);
          
          return {
            success: true,
            checklistId: checklist.id,
            pdfBytes,
            index: globalIndex
          };
          
        } catch (error) {
          logger.error(`Failed to process checklist ${checklist.id}:`, error);
          return {
            success: false,
            checklistId: checklist.id,
            error: error instanceof Error ? error.message : 'Unknown error',
            index: batchIndex * parallelBatchSize + index
          };
        }
      });

      // Wait for all PDFs in this batch to complete
      const batchResults = await Promise.all(batchPromises);
      
      // Process results and merge successful PDFs
      for (const batchResult of batchResults) {
        if (batchResult.success && batchResult.pdfBytes) {
          try {
            const individualPdf = await PDFDocument.load(batchResult.pdfBytes);
            const pages = await mergedPdf.copyPages(individualPdf, individualPdf.getPageIndices());
            pages.forEach(page => mergedPdf.addPage(page));
            result.processedCount++;
          } catch (mergeError) {
            logger.error(`Failed to merge PDF for checklist ${batchResult.checklistId}:`, mergeError);
            result.failedChecklists.push(batchResult.checklistId);
            result.failedCount++;
          }
        } else {
          result.failedChecklists.push(batchResult.checklistId);
          result.failedCount++;
        }
      }

      itemsProcessed += batch.length;

      // Small delay between batches
      if (batchIndex < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // STEP 3: Finalize PDF
    await updateQueueStatus(queueId, 'processing', {
      currentIndex: checklists.length,
      totalCount: checklists.length,
      percentage: 90,
      stage: 'merging',
      currentChecklist: 'Finalizing combined PDF with summary overlay...'
    });

    result.fileBytes = new Uint8Array(await mergedPdf.save({
      useObjectStreams: false,
      objectsPerTick: 100
    }));
    
    logger.info('Bulk PDF generation completed with summary overlay', {
      processedCount: result.processedCount,
      failedCount: result.failedCount,
      finalFileSize: result.fileBytes.length,
      summaryPagesCount: totalSummaryPages,
      totalPagesCount: mergedPdf.getPageCount()
    });
    
    return result;
    
  } catch (error) {
    logger.error('Error generating bulk PDF with summary overlay:', error);
    throw error;
  }
}

  /**
   * Generate PDF using Puppeteer with optimized compression settings
   */
  async function generateSummaryPagePDF(browser: Browser, htmlContent: string): Promise<Uint8Array> {
    const page = await browser.newPage();
    
    try {
      // Set viewport for consistent rendering
      await page.setViewport({ width: 794, height: 1123 }); // A4 dimensions
      
      // Set content and generate PDF with compression optimizations
      await page.setContent(htmlContent, { waitUntil: 'networkidle0' });
      
      const pdfBuffer = await page.pdf({
        format: 'a4',
        printBackground: true,
        preferCSSPageSize: false,
        margin: {
          top: '5mm',
          right: '10mm',
          bottom: '10mm',
          left: '10mm'
        },
        // Compression optimizations
        tagged: false,
        displayHeaderFooter: false,
        headerTemplate: '',
        footerTemplate: '',
        omitBackground: false
      });

      const rawPdfBytes = new Uint8Array(pdfBuffer);
      
      // Apply additional PDF compression for bulk export
      const compressedPdfBytes = await compressPDF(rawPdfBytes, 'medium', 'bulk');
      
      return compressedPdfBytes;
      
    } finally {
      await page.close();
    }
  }

/**
 * Generate individual PDF with shared browser and compression
 */
async function generateIndividualPDFWithSharedBrowser(
  browser: Browser, 
  checklist: ChecklistData
): Promise<Uint8Array> {
  const page = await browser.newPage();
  
  try {
    // Set viewport for consistent rendering
    await page.setViewport({ width: 794, height: 1123 }); // A4 dimensions

    // Get cached images
    const logoDataUrl = imageCache.get('logo') || '';
    const beforeImageDataUrl = checklist.beforeImage ? imageCache.get(checklist.beforeImage) : undefined;
    const afterImageDataUrl = checklist.afterImage ? imageCache.get(checklist.afterImage) : undefined;
    const companySignatureDataUrl = imageCache.get('signature') || '';

    // Generate HTML using our template function
    const htmlContent = generatePDFHTML({
      checklist,
      logoDataUrl,
      beforeImageDataUrl,
      afterImageDataUrl,
      signatureDataUrl: companySignatureDataUrl
    });

    // Set content and generate PDF with compression optimizations
    await page.setContent(htmlContent, { waitUntil: 'networkidle0' });
    
    const pdfBuffer = await page.pdf({
      format: 'a4',
      printBackground: true,
      preferCSSPageSize: false,
      margin: {
        top: '5mm',
        right: '10mm',
        bottom: '10mm',
        left: '10mm'
      },
      // Compression optimizations
      tagged: false,
      displayHeaderFooter: false,
      headerTemplate: '',
      footerTemplate: '',
      omitBackground: false
    });

    const rawPdfBytes = new Uint8Array(pdfBuffer);
    
    // Apply additional PDF compression for individual checklist in bulk export
    const compressedPdfBytes = await compressPDF(rawPdfBytes, 'medium', 'bulk');
    
    return compressedPdfBytes;
    
  } finally {
    await page.close();
  }
}

/**
 * Load company asset as data URL using Firebase Admin SDK (bypasses App Check)
 */
async function loadCompanyAssetAsDataUrl(assetKey: 'logo' | 'signature'): Promise<string> {
  try {
    // Use Firebase Admin SDK to get file directly (bypasses App Check)
    const bucket = getStorage().bucket();
    const fileName = assetKey === 'logo' ? 'assets/logo.jpeg' : 'assets/signature.png';
    const file = bucket.file(fileName);
    
    logger.info(`Loading ${assetKey} using Firebase Admin SDK`, { fileName });
    
    // Check if file exists
    const [exists] = await file.exists();
    if (!exists) {
      throw new Error(`File not found: ${fileName}`);
    }
    
    // Download file content
    const [buffer] = await file.download();
    
    // Get file metadata for content type
    const [metadata] = await file.getMetadata();
    const contentType = metadata.contentType || (assetKey === 'logo' ? 'image/jpeg' : 'image/png');
    
    // Convert to data URL
    const base64 = buffer.toString('base64');
    const dataUrl = `data:${contentType};base64,${base64}`;
    
    logger.info(`Successfully loaded ${assetKey} using Admin SDK`, { 
      bufferSize: buffer.length,
      contentType,
      dataUrlLength: dataUrl.length
    });
    
    return dataUrl;
    
  } catch (error) {
    logger.error(`Failed to load ${assetKey} using Admin SDK`, {
      error: error instanceof Error ? error.message : 'Unknown error',
      assetKey
    });
    
    // Try to create a simple placeholder image
    try {
      const placeholder = createPlaceholderImageDataUrl(assetKey);
      logger.warn(`Using placeholder for ${assetKey}`, { placeholderLength: placeholder.length });
      return placeholder;
    } catch (placeholderError) {
      logger.error(`Failed to create placeholder for ${assetKey}`, { placeholderError });
      return '';
    }
  }
}

/**
 * Create a simple placeholder image data URL
 */
function createPlaceholderImageDataUrl(assetKey: 'logo' | 'signature'): string {
  // Create a simple SVG placeholder
  const isLogo = assetKey === 'logo';
  const text = isLogo ? 'AUBURN ENGINEERING' : 'SIGNATURE';
  const width = isLogo ? 200 : 150;
  const height = isLogo ? 60 : 40;
  
  const svg = `
    <svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
      <rect width="100%" height="100%" fill="#f0f0f0" stroke="#ccc" stroke-width="1"/>
      <text x="50%" y="50%" font-family="Arial, sans-serif" font-size="12" 
            text-anchor="middle" dominant-baseline="middle" fill="#666">
        ${text}
      </text>
    </svg>
  `.trim();
  
  const base64 = Buffer.from(svg).toString('base64');
  return `data:image/svg+xml;base64,${base64}`;
}

/**
 * Generate bulk Excel export (placeholder)
 */
async function generateBulkExcel(
  queueId: string,
  checklists: ChecklistData[],
  queueData: ExportQueueItem
): Promise<{
  fileBytes: Uint8Array;
  processedCount: number;
  failedCount: number;
  failedChecklists: string[];
}> {
  // Excel export not implemented yet
  logger.warn('Excel export not yet implemented', { queueId });
  throw new Error('Excel export not yet implemented in Cloud Function');
}

/**
 * Fetch checklist data from Firestore with optimized batching
 */
async function fetchChecklistData(checklistIds: string[], requestingUserId: string): Promise<ChecklistData[]> {
  const checklists: ChecklistData[] = [];
  
  try {
    const db = admin.firestore();
    
    logger.info('Fetching checklist data for admin export', {
      requestingUserId,
      checklistIds: checklistIds.length,
      checklistIdsPreview: checklistIds.slice(0, 3)
    });

    // Use larger batch size for better performance (Firestore supports up to 500 in a batch read)
    const batchSize = Math.min(500, checklistIds.length);
    const batches: string[][] = [];
    
    for (let i = 0; i < checklistIds.length; i += batchSize) {
      batches.push(checklistIds.slice(i, i + batchSize));
    }

    // Process all batches in parallel for maximum speed
    const batchPromises = batches.map(async (batch, batchIndex) => {
      try {
        // Use batch read for better performance
        const docRefs = batch.map(id => db.collection('checklists').doc(id));
        const docs = await db.getAll(...docRefs);
        
        const batchResults: ChecklistData[] = [];
        docs.forEach((doc) => {
          if (doc.exists) {
            const data = doc.data();
            batchResults.push({
              id: doc.id,
              ...data
            } as ChecklistData);
          } else {
            logger.warn('Checklist not found', { checklistId: doc.id });
          }
        });
        
        logger.debug(`Batch ${batchIndex + 1}/${batches.length} completed`, {
          requested: batch.length,
          found: batchResults.length
        });
        
        return batchResults;
        
      } catch (error) {
        logger.error(`Error fetching batch ${batchIndex + 1}:`, error);
        // Return empty array for failed batch rather than failing entire operation
        return [];
      }
    });

    // Wait for all batches to complete
    const batchResults = await Promise.all(batchPromises);
    
    // Flatten results
    batchResults.forEach(batch => checklists.push(...batch));

    logger.info('Fetched checklist data', {
      requested: checklistIds.length,
      fetched: checklists.length,
      missing: checklistIds.length - checklists.length,
      batches: batches.length
    });

    return checklists;

  } catch (error) {
    logger.error('Failed to fetch checklist data', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Upload file to Firebase Storage
 */
async function uploadToStorage(
  fileBytes: Uint8Array,
  userId: string,
  queueId: string,
  type: 'pdf' | 'excel',
  metadata: any
): Promise<{
  downloadUrl: string;
  storagePath: string;
  fileName: string;
  expiresAt: Date;
}> {
  try {
    const bucket = getStorage().bucket();
    const extension = type === 'pdf' ? 'pdf' : 'xlsx';
    const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
    const fileName = `bulk-export-${timestamp}-${queueId}.${extension}`;
    const storagePath = `exports/${userId}/${fileName}`;
    
    const file = bucket.file(storagePath);
    
    // Upload file with public read access
    await file.save(Buffer.from(fileBytes), {
      metadata: {
        contentType: type === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        metadata: {
          userId,
          queueId,
          type,
          checklistCount: metadata.checklistCount?.toString() || '0',
          clientName: metadata.clientName || '',
          dateRange: metadata.dateRange || '',
          requestedBy: metadata.requestedBy || '',
          createdAt: new Date().toISOString()
        }
      }
    });
    
    // Make file publicly readable
    await file.makePublic();
    
    // Set expiration (7 days from now)
    const expiresAt = new Date(Date.now() + 7 * 24 * 60 * 60 * 1000);
    
    // Generate public download URL (no signing required)
    const downloadUrl = `https://storage.googleapis.com/${bucket.name}/${storagePath}`;
    
    logger.info('File uploaded to storage', {
      storagePath,
      fileName,
      fileSize: fileBytes.length,
      downloadUrl,
      expiresAt: expiresAt.toISOString()
    });
    
    return {
      downloadUrl,
      storagePath,
      fileName,
      expiresAt
    };
    
  } catch (error) {
    logger.error('Storage upload failed', {
      error: error instanceof Error ? error.message : 'Unknown error'
    });
    throw error;
  }
}

/**
 * Update queue status
 */
async function updateQueueStatus(
  queueId: string,
  status: string,
  progress?: any,
  setStartTime?: boolean
): Promise<void> {
  try {
    const db = admin.firestore();
    const updateData: any = {
      status,
      updatedAt: admin.firestore.FieldValue.serverTimestamp()
    };
    
    if (progress) {
      // Filter out undefined values to prevent Firestore errors
      const cleanProgress = Object.fromEntries(
        Object.entries(progress).filter(([_, value]) => value !== undefined)
      );
      updateData.progress = cleanProgress;
    }
    
    if (setStartTime && status === 'processing') {
      updateData.startedAt = admin.firestore.FieldValue.serverTimestamp();
    }
    
    await db.collection('export-queue').doc(queueId).update(updateData);
    
  } catch (error) {
    logger.error('Failed to update queue status', {
      queueId,
      status,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
}

/**
 * Complete queue item
 */
async function completeQueueItem(queueId: string, result: any): Promise<void> {
  try {
    const db = admin.firestore();
    
    // Ensure expiresAt is properly formatted for Firestore
    const expiresAt = result.expiresAt instanceof Date 
      ? admin.firestore.Timestamp.fromDate(result.expiresAt)
      : result.expiresAt;
    
    // Validate required fields to prevent undefined values
    if (!result.fileName || !result.downloadUrl || !result.storagePath) {
      throw new Error(`Missing required fields: fileName=${result.fileName}, downloadUrl=${result.downloadUrl}, storagePath=${result.storagePath}`);
    }
    
    await db.collection('export-queue').doc(queueId).update({
      status: 'completed',
      completedAt: admin.firestore.FieldValue.serverTimestamp(),
      progress: {
        currentIndex: result.processedCount || 0,
        totalCount: result.processedCount || 0,
        percentage: 100,
        stage: 'complete'
      },
      result: {
        fileName: result.fileName,
        fileSize: result.fileSize || 0,
        downloadUrl: result.downloadUrl,
        storagePath: result.storagePath,
        processedCount: result.processedCount || 0,
        failedCount: result.failedCount || 0,
        failedChecklists: result.failedChecklists || [],
        expiresAt: expiresAt
      }
    });
    
    logger.info('Queue item completed successfully', { queueId });
    
  } catch (error) {
    logger.error('Failed to complete queue item', {
      queueId,
      error: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined
    });
    throw error; // Re-throw to let the caller handle it
  }
}

/**
 * Fail queue item
 */
async function failQueueItem(queueId: string, errorMessage: string): Promise<void> {
  try {
    const db = admin.firestore();
    
    await db.collection('export-queue').doc(queueId).update({
      status: 'failed',
      completedAt: admin.firestore.FieldValue.serverTimestamp(),
      error: errorMessage,
      progress: {
        currentIndex: 0,
        totalCount: 0,
        percentage: 0,
        stage: 'failed'
      }
    });
    
  } catch (error) {
    logger.error('Failed to update queue item as failed', {
      queueId,
      error: error instanceof Error ? error.message : 'Unknown error'
    });
  }
} 
[debug] [2025-06-15T05:19:30.633Z] ----------------------------------------------------------------------
[debug] [2025-06-15T05:19:30.635Z] Command:       /home/<USER>/.nvm/versions/node/v22.12.0/bin/node /home/<USER>/.nvm/versions/node/v22.12.0/bin/firebase deploy --only functions
[debug] [2025-06-15T05:19:30.636Z] CLI Version:   14.2.2
[debug] [2025-06-15T05:19:30.636Z] Platform:      linux
[debug] [2025-06-15T05:19:30.636Z] Node Version:  v22.12.0
[debug] [2025-06-15T05:19:30.636Z] Time:          Sun Jun 15 2025 10:49:30 GMT+0530 (India Standard Time)
[debug] [2025-06-15T05:19:30.636Z] ----------------------------------------------------------------------
[debug] 
[debug] [2025-06-15T05:19:30.842Z] Object "/storage" in "firebase.json" is missing required property: {"missingProperty":"rules"}
[debug] [2025-06-15T05:19:30.843Z] Field "/storage" in "firebase.json" is possibly invalid: must be array
[debug] [2025-06-15T05:19:30.843Z] Field "/storage" in "firebase.json" is possibly invalid: must match a schema in anyOf
[debug] [2025-06-15T05:19:30.846Z] > command requires scopes: ["email","openid","https://www.googleapis.com/auth/cloudplatformprojects.readonly","https://www.googleapis.com/auth/firebase","https://www.googleapis.com/auth/cloud-platform"]
[debug] [2025-06-15T05:19:30.847Z] > authorizing via signed-in user (<EMAIL>)
[debug] [2025-06-15T05:19:30.847Z] [iam] checking project auburn-engineering for permissions ["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]
[debug] [2025-06-15T05:19:30.848Z] Checked if tokens are valid: true, expires at: 1749967979659
[debug] [2025-06-15T05:19:30.848Z] Checked if tokens are valid: true, expires at: 1749967979659
[debug] [2025-06-15T05:19:30.849Z] >>> [apiv2][query] POST https://cloudresourcemanager.googleapis.com/v1/projects/auburn-engineering:testIamPermissions [none]
[debug] [2025-06-15T05:19:30.849Z] >>> [apiv2][(partial)header] POST https://cloudresourcemanager.googleapis.com/v1/projects/auburn-engineering:testIamPermissions x-goog-quota-user=projects/auburn-engineering
[debug] [2025-06-15T05:19:30.849Z] >>> [apiv2][body] POST https://cloudresourcemanager.googleapis.com/v1/projects/auburn-engineering:testIamPermissions {"permissions":["cloudfunctions.functions.create","cloudfunctions.functions.delete","cloudfunctions.functions.get","cloudfunctions.functions.list","cloudfunctions.functions.update","cloudfunctions.operations.get","firebase.projects.get"]}

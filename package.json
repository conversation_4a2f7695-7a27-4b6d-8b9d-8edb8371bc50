{"name": "auburn_engineering", "version": "1.0.1", "private": true, "engines": {"node": "20"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "build:static": "next build", "build:functions": "cd functions && npm run build", "build:all": "npm run build:static && npm run build:functions", "deploy": "firebase deploy", "deploy:hosting": "firebase deploy --only hosting", "deploy:functions": "firebase deploy --only functions", "version:patch": "node scripts/update-version.js patch", "version:minor": "node scripts/update-version.js minor", "version:major": "node scripts/update-version.js major"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-visually-hidden": "^1.2.3", "@react-three/drei": "^10.1.2", "@react-three/fiber": "^9.1.2", "@types/qrcode": "^1.5.5", "@types/recharts": "^1.8.29", "@types/three": "^0.177.0", "@types/xlsx": "^0.0.35", "@yudiel/react-qr-scanner": "^2.3.1", "browser-image-compression": "^2.0.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "firebase": "^11.8.1", "firebase-admin": "^13.4.0", "framer-motion": "^12.16.0", "html2canvas": "^1.4.1", "jspdf": "^3.0.1", "lucide-react": "^0.511.0", "next": "15.3.3", "next-themes": "^0.4.6", "pdf-lib": "^1.17.1", "puppeteer": "^22.15.0", "qrcode": "^1.5.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.57.0", "recharts": "^2.15.3", "sonner": "^2.0.5", "tailwind-merge": "^3.3.0", "three": "^0.177.0", "xlsx": "^0.18.5", "zod": "^3.25.49"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.3", "null-loader": "^4.0.1", "tailwindcss": "^4", "tw-animate-css": "^1.3.3", "typescript": "^5"}}
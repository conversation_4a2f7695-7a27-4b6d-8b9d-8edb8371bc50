import { useState, useEffect } from 'react';
import { StorageService } from '@/lib/services/storage/storage-adapter';
import { ChecklistData } from '@/types/checklist';
import { useAuth } from '@/components/auth';

/**
 * Custom hook that safely handles StorageService initialization
 * and provides methods to interact with the storage service
 */
export function useStorageService() {
  const { user } = useAuth();
  const [isStorageReady, setIsStorageReady] = useState(false);
  const [initializationError, setInitializationError] = useState<string | null>(null);

  useEffect(() => {
    if (!user) {
      setIsStorageReady(false);
      setInitializationError(null);
      return;
    }

    const checkInitialization = async () => {
      try {
        // Check if already initialized
        if (StorageService.isInitialized()) {
          setIsStorageReady(true);
          setInitializationError(null);
          return;
        }

        // Wait for initialization with increased timeout for Firebase Storage
        let retries = 0;
        const maxRetries = 30; // 6 seconds total wait (increased for production)
        
        while (!StorageService.isInitialized() && retries < maxRetries) {
          await new Promise(resolve => setTimeout(resolve, 200));
          retries++;
          
          // Check again after each wait
          if (StorageService.isInitialized()) {
            setIsStorageReady(true);
            setInitializationError(null);
            return;
          }
        }

        // Final check after waiting
        if (StorageService.isInitialized()) {
          setIsStorageReady(true);
          setInitializationError(null);
        } else {
          // Provide more specific error information
          throw new Error(`Storage service failed to initialize after ${maxRetries * 200}ms. This may be due to network issues, Firebase configuration problems, or authentication delays.`);
        }
      } catch (error) {
        console.error('Storage service initialization failed:', error);
        setIsStorageReady(false);
        setInitializationError(error instanceof Error ? error.message : 'Unknown initialization error');
      }
    };

    checkInitialization();
  }, [user]);

  /**
   * Safely execute a storage operation with error handling
   */
  const safeStorageOperation = async <T>(
    operation: () => Promise<T>,
    defaultValue: T
  ): Promise<T> => {
    try {
      // Check direct StorageService state first to avoid race conditions
      const isServiceInitialized = StorageService.isInitialized();
      
      // Update local state if service is initialized but local state isn't updated yet
      if (isServiceInitialized && !isStorageReady) {
        setIsStorageReady(true);
        setInitializationError(null);
      }
      
      // If service is not initialized, throw error
      if (!isServiceInitialized) {
        throw new Error('Storage service not ready');
      }
      
      return await operation();
    } catch (error) {
      console.error('Storage operation failed:', error);
      return defaultValue;
    }
  };

  /**
   * Get all checklists with safe initialization check
   */
  const getAllChecklists = async (): Promise<ChecklistData[]> => {
    // Ensure service is ready before operation
    if (!StorageService.isInitialized()) {
      console.warn('Storage service not initialized, returning empty array');
      return [];
    }
    return safeStorageOperation(
      () => StorageService.getAllChecklists(),
      []
    );
  };

  /**
   * Save a checklist with safe initialization check
   */
  const saveChecklist = async (checklist: ChecklistData): Promise<boolean> => {
    // Ensure service is ready before operation
    if (!StorageService.isInitialized()) {
      console.warn('Storage service not initialized, cannot save checklist');
      return false;
    }
    return safeStorageOperation(
      () => StorageService.saveChecklist(checklist),
      false
    );
  };

  /**
   * Delete a checklist with safe initialization check
   */
  const deleteChecklist = async (id: string): Promise<boolean> => {
    // Ensure service is ready before operation
    if (!StorageService.isInitialized()) {
      console.warn('Storage service not initialized, cannot delete checklist');
      return false;
    }
    return safeStorageOperation(
      () => StorageService.deleteChecklist(id),
      false
    );
  };

  /**
   * Clear all checklists with safe initialization check
   */
  const clearAllChecklists = async (): Promise<boolean> => {
    // Ensure service is ready before operation
    if (!StorageService.isInitialized()) {
      console.warn('Storage service not initialized, cannot clear checklists');
      return false;
    }
    return safeStorageOperation(
      () => StorageService.clearAllChecklists(),
      false
    );
  };

  return {
    isStorageReady: isStorageReady || StorageService.isInitialized(), // Use actual service state as fallback
    initializationError,
    getAllChecklists,
    saveChecklist,
    deleteChecklist,
    clearAllChecklists,
    safeStorageOperation,
  };
} 
'use client';

import { useCallback, useRef } from 'react';

interface InitializationState {
  isInitialized: boolean;
  isInitializing: boolean;
  lastUserId: string | null;
}

// Global state to track initialization across components
const globalInitState: InitializationState = {
  isInitialized: false,
  isInitializing: false,
  lastUserId: null
};

export function useInitializationOptimization() {
  const lastCheckRef = useRef<string | null>(null);

  const shouldInitialize = useCallback((userId: string | null): boolean => {
    // No user = no initialization needed
    if (!userId) {
      return false;
    }

    // Different user = need to reinitialize
    if (globalInitState.lastUserId !== userId) {
      return true;
    }

    // Same user but not initialized = need to initialize
    if (!globalInitState.isInitialized && !globalInitState.isInitializing) {
      return true;
    }

    // Already initialized or initializing for this user
    return false;
  }, []);

  const markInitializationStart = useCallback((userId: string) => {
    globalInitState.isInitializing = true;
    globalInitState.lastUserId = userId;
    globalInitState.isInitialized = false;
  }, []);

  const markInitializationComplete = useCallback((userId: string) => {
    globalInitState.isInitializing = false;
    globalInitState.isInitialized = true;
    globalInitState.lastUserId = userId;
  }, []);

  const markInitializationFailed = useCallback(() => {
    globalInitState.isInitializing = false;
    globalInitState.isInitialized = false;
  }, []);

  const resetInitialization = useCallback(() => {
    globalInitState.isInitialized = false;
    globalInitState.isInitializing = false;
    globalInitState.lastUserId = null;
  }, []);

  const getInitializationStatus = useCallback(() => ({
    ...globalInitState
  }), []);

  return {
    shouldInitialize,
    markInitializationStart,
    markInitializationComplete,
    markInitializationFailed,
    resetInitialization,
    getInitializationStatus
  };
} 
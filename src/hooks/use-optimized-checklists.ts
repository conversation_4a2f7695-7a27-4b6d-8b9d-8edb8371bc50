import { useState, useCallback, useMemo, useRef, useEffect } from 'react';
import { QueryDocumentSnapshot } from 'firebase/firestore';
import { 
  OptimizedAdminChecklistService, 
  ChecklistWithUser, 
  PaginatedResult,
  ChecklistStats 
} from '@/lib/services/checklist/admin-checklist-service-optimized';
import { calculateChecklistSummary } from '@/lib/services/storage/firestore-storage';

interface FilterCriteria {
  searchTerm: string;
  statusFilter: 'all' | 'completed' | 'pending';
  buildingFilter: string;
  locationFilter: string;
  clientFilter: string;
}

interface UseOptimizedChecklistsReturn {
  // Data
  checklists: ChecklistWithUser[];
  stats: ChecklistStats | null;
  isLoading: boolean;
  isLoadingMore: boolean;
  hasMore: boolean;
  error: string | null;
  
  // Actions
  loadMore: () => Promise<void>;
  refresh: () => Promise<void>;
  updateFilters: (filters: Partial<FilterCriteria>) => void;
  initialize: () => Promise<void>;
  
  // Computed data
  filteredChecklists: ChecklistWithUser[];
  checklistSummaries: Map<string, any>;
  
  // Pagination info
  currentPage: number;
  totalLoaded: number;
}

const DEFAULT_PAGE_SIZE = 50;

export function useOptimizedChecklists(options?: { autoInitialize?: boolean }): UseOptimizedChecklistsReturn {
  const { autoInitialize = true } = options || {};
  // Core state
  const [checklists, setChecklists] = useState<ChecklistWithUser[]>([]);
  const [stats, setStats] = useState<ChecklistStats | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Pagination state
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);
  const nextCursor = useRef<QueryDocumentSnapshot | undefined>(undefined);
  
  // Filter state
  const [filters, setFilters] = useState<FilterCriteria>({
    searchTerm: '',
    statusFilter: 'all',
    buildingFilter: 'all',
    locationFilter: 'all',
    clientFilter: 'all'
  });

  // Memoized checklist summaries to avoid recalculation
  const checklistSummaries = useMemo(() => {
    const summariesMap = new Map();
    checklists.forEach(checklist => {
      if (!summariesMap.has(checklist.id)) {
        summariesMap.set(checklist.id, calculateChecklistSummary(checklist));
      }
    });
    return summariesMap;
  }, [checklists]);

  // Client-side filtering for immediate feedback
  const filteredChecklists = useMemo(() => {
    if (!filters.searchTerm && filters.statusFilter === 'all' && 
        filters.buildingFilter === 'all' && filters.locationFilter === 'all' && 
        filters.clientFilter === 'all') {
      return checklists;
    }

    return checklists.filter(checklist => {
      // Search term filter
      if (filters.searchTerm) {
        const searchLower = filters.searchTerm.toLowerCase();
        const matchesSearch = 
          checklist.generalInfo?.tagNo?.toLowerCase().includes(searchLower) ||
          checklist.generalInfo?.clientName?.toLowerCase().includes(searchLower) ||
          checklist.userDisplayName?.toLowerCase().includes(searchLower) ||
          checklist.userEmail?.toLowerCase().includes(searchLower) ||
          checklist.equipmentTag?.tagNumber?.toLowerCase().includes(searchLower) ||
          checklist.equipmentTag?.equipmentName?.toLowerCase().includes(searchLower) ||
          checklist.generalInfo?.equipmentName?.toLowerCase().includes(searchLower) ||
          checklist.generalInfo?.location?.toLowerCase().includes(searchLower);
        
        if (!matchesSearch) return false;
      }

      // Status filter
      if (filters.statusFilter === 'completed' && !checklist.isCompleted) return false;
      if (filters.statusFilter === 'pending' && checklist.isCompleted) return false;

      // Building filter
      if (filters.buildingFilter !== 'all' && 
          checklist.equipmentTag?.building !== filters.buildingFilter &&
          checklist.generalInfo?.building !== filters.buildingFilter) return false;

      // Location filter
      if (filters.locationFilter !== 'all' && 
          checklist.equipmentTag?.location !== filters.locationFilter &&
          checklist.generalInfo?.location !== filters.locationFilter) return false;

      // Client filter
      if (filters.clientFilter !== 'all' && 
          checklist.equipmentTag?.clientName !== filters.clientFilter &&
          checklist.generalInfo?.clientName !== filters.clientFilter) return false;

      return true;
    });
  }, [checklists, filters]);

  // Load initial data and stats
  const loadInitialData = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const [checklistResult, statsResult] = await Promise.all([
        OptimizedAdminChecklistService.getChecklistsPaginated(DEFAULT_PAGE_SIZE),
        OptimizedAdminChecklistService.getOptimizedChecklistStats()
      ]);

      setChecklists(checklistResult.items);
      setStats(statsResult);
      setHasMore(checklistResult.hasMore);
      setCurrentPage(1);
      nextCursor.current = checklistResult.nextCursor;
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load checklists');
      console.error('Error loading initial checklist data:', err);
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Load more data for pagination
  const loadMore = useCallback(async () => {
    if (!hasMore || isLoadingMore) return;

    setIsLoadingMore(true);
    setError(null);

    try {
      const result = await OptimizedAdminChecklistService.getChecklistsPaginated(
        DEFAULT_PAGE_SIZE,
        nextCursor.current
      );

      setChecklists(prev => [...prev, ...result.items]);
      setHasMore(result.hasMore);
      setCurrentPage(prev => prev + 1);
      nextCursor.current = result.nextCursor;

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load more checklists');
      console.error('Error loading more checklists:', err);
    } finally {
      setIsLoadingMore(false);
    }
  }, [hasMore, isLoadingMore]);

  // Refresh data (reset pagination)
  const refresh = useCallback(async () => {
    setChecklists([]);
    setCurrentPage(0);
    setHasMore(true);
    nextCursor.current = undefined;
    await loadInitialData();
  }, [loadInitialData]);

  // Update filters
  const updateFilters = useCallback((newFilters: Partial<FilterCriteria>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  }, []);

  // Auto-load data when hook is first used
  const [hasInitialized, setHasInitialized] = useState(false);
  
  // Manual initialization function
  const initialize = useCallback(async () => {
    if (!hasInitialized) {
      setHasInitialized(true);
      await loadInitialData();
    }
  }, [hasInitialized, loadInitialData]);
  
  useEffect(() => {
    // Auto-initialize only if enabled and not already initialized
    if (autoInitialize && !hasInitialized) {
      initialize();
    }
  }, [autoInitialize, hasInitialized, initialize]);

  return {
    // Data
    checklists,
    stats,
    isLoading,
    isLoadingMore,
    hasMore,
    error,
    
    // Actions
    loadMore,
    refresh,
    updateFilters,
    initialize,
    
    // Computed data
    filteredChecklists,
    checklistSummaries,
    
    // Pagination info
    currentPage,
    totalLoaded: checklists.length
  };
}

// Additional hook for infinite scroll
export function useInfiniteScroll(
  loadMore: () => Promise<void>,
  hasMore: boolean,
  isLoading: boolean
) {
  const observerRef = useRef<IntersectionObserver | undefined>(undefined);
  
  const lastElementRef = useCallback((node: HTMLElement | null) => {
    if (isLoading) return;
    
    if (observerRef.current) observerRef.current.disconnect();
    
    observerRef.current = new IntersectionObserver(entries => {
      if (entries[0].isIntersecting && hasMore) {
        loadMore();
      }
    }, {
      threshold: 0.1,
      rootMargin: '100px'
    });
    
    if (node) observerRef.current.observe(node);
  }, [isLoading, hasMore, loadMore]);

  return lastElementRef;
} 
import { useState, useCallback, useRef } from 'react';
import { useAuth } from '@/components/auth/auth-provider';
import { ExportQueueService } from '@/lib/services/export/export-queue-service';
import { ChecklistWithUser } from '@/lib/services/checklist/admin-checklist-service';
import { toast } from 'sonner';

export interface ExportProgress {
  stage: 'idle' | 'authenticating' | 'queuing' | 'queued' | 'processing' | 'completed' | 'failed';
  message: string;
  queueId?: string;
  startTime?: Date;
  completedTime?: Date;
  error?: string;
}

export interface ExportStats {
  totalItems: number;
  estimatedDuration?: number; // in seconds
  averageTimePerItem?: number; // in seconds
}

export function useBulkExport() {
  const { user } = useAuth();
  const [exportStates, setExportStates] = useState<{
    pdf: ExportProgress;
    excel: ExportProgress;
  }>({
    pdf: { stage: 'idle', message: '' },
    excel: { stage: 'idle', message: '' }
  });

  const [exportStats, setExportStats] = useState<ExportStats>({ totalItems: 0 });
  const abortControllerRef = useRef<AbortController | null>(null);

  const updateExportState = useCallback((type: 'pdf' | 'excel', update: Partial<ExportProgress>) => {
    setExportStates(prev => ({
      ...prev,
      [type]: { ...prev[type], ...update }
    }));
  }, []);

  const calculateEstimatedDuration = useCallback((itemCount: number, type: 'pdf' | 'excel'): number => {
    // Base estimates (in seconds per item)
    const baseTimePerItem = type === 'pdf' ? 3 : 1.5; // PDF takes longer due to rendering
    const overhead = 10; // Base overhead for setup/upload
    
    // Scale factor for larger exports (efficiency improves with size)
    const scaleFactor = Math.max(0.7, 1 - (itemCount / 1000));
    
    return Math.ceil((itemCount * baseTimePerItem * scaleFactor) + overhead);
  }, []);

  const startExport = useCallback(async (
    type: 'pdf' | 'excel',
    checklists: ChecklistWithUser[],
    userDisplayName: string
  ): Promise<string | null> => {
    if (!user?.uid) {
      toast.error('Authentication required', {
        description: 'Please sign in to export checklists'
      });
      return null;
    }

    // Prevent multiple concurrent exports of same type
    if (exportStates[type].stage !== 'idle') {
      toast.warning('Export already in progress', {
        description: `Please wait for the current ${type.toUpperCase()} export to complete`
      });
      return null;
    }

    const startTime = new Date();
    const estimatedDuration = calculateEstimatedDuration(checklists.length, type);
    
    setExportStats({
      totalItems: checklists.length,
      estimatedDuration,
      averageTimePerItem: estimatedDuration / checklists.length
    });

    // Create abort controller for this export
    abortControllerRef.current = new AbortController();

    try {
      // Stage 1: Authentication
      updateExportState(type, {
        stage: 'authenticating',
        message: 'Verifying authentication...',
        startTime
      });

      toast.loading(`Starting ${type.toUpperCase()} export...`, {
        id: `export-${type}`,
        description: `Preparing to export ${checklists.length} checklists`
      });

      const { getFirebaseAuth } = await import('@/config/firebase-config');
      const auth = getFirebaseAuth();
      let currentUser = auth.currentUser;

      // Wait for auth state if user is not immediately available
      if (!currentUser) {
        updateExportState(type, {
          stage: 'authenticating',
          message: 'Waiting for authentication...'
        });

        await new Promise((resolve) => {
          const unsubscribe = auth.onAuthStateChanged((user) => {
            if (user) {
              currentUser = user;
              unsubscribe();
              resolve(user);
            }
          });

          setTimeout(() => {
            unsubscribe();
            resolve(null);
          }, 5000);
        });
      }

      if (!currentUser) {
        throw new Error('Authentication required - please sign in again');
      }

      // Stage 2: Queuing
      updateExportState(type, {
        stage: 'queuing',
        message: 'Adding to export queue...'
      });

      toast.loading(`Queuing ${type.toUpperCase()} export...`, {
        id: `export-${type}`,
        description: `Processing ${checklists.length} checklists`
      });

      // Force refresh token to ensure it's valid
      const token = await currentUser.getIdToken(true);

      const { getCloudFunctionUrl } = await import('@/lib/config/cloud-functions');
      const queueExportUrl = getCloudFunctionUrl('queueExport');
      
      const response = await fetch(queueExportUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`,
        },
        body: JSON.stringify({
          checklistIds: checklists.map(c => c.id),
          type,
          options: {}
        }),
        signal: abortControllerRef.current.signal
      });

      const result = await response.json();

      if (!response.ok) {
        throw new Error(result.error || 'Failed to queue export');
      }

      // Stage 3: Successfully queued
      const queueId = result.data.queueId;
      updateExportState(type, {
        stage: 'queued',
        message: 'Export queued successfully!',
        queueId
      });

      toast.success(`${type.toUpperCase()} export queued!`, {
        id: `export-${type}`,
        description: `Processing ${checklists.length} checklists. Estimated completion: ${Math.ceil(estimatedDuration / 60)} minutes`,
        action: {
          label: 'View Queue',
          onClick: () => {
            // This will be passed from the component
            window.dispatchEvent(new CustomEvent('openExportQueue'));
          }
        },
        duration: 8000
      });

      return queueId;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      updateExportState(type, {
        stage: 'failed',
        message: 'Export failed',
        error: errorMessage
      });

      // Handle specific error types
      let toastTitle = 'Export failed';
      let toastDescription = errorMessage;

      if (errorMessage.includes('Authentication required') || 
          errorMessage.includes('auth/user-token-expired') ||
          errorMessage.includes('auth/invalid-user-token')) {
        toastTitle = 'Session expired';
        toastDescription = 'Please refresh the page and try again';
      } else if (errorMessage.includes('Queue limit')) {
        toastTitle = 'Queue limit reached';
        toastDescription = 'Please wait for current exports to complete';
      }

      toast.error(toastTitle, {
        id: `export-${type}`,
        description: toastDescription,
        action: {
          label: 'Retry',
          onClick: () => startExport(type, checklists, userDisplayName)
        }
      });

      return null;
    } finally {
      // Reset to idle after a delay to show completion state
      setTimeout(() => {
        updateExportState(type, { stage: 'idle', message: '' });
      }, 3000);
    }
  }, [user?.uid, exportStates, updateExportState, calculateEstimatedDuration]);

  const cancelExport = useCallback((type: 'pdf' | 'excel') => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }
    
    updateExportState(type, {
      stage: 'idle',
      message: 'Export cancelled'
    });

    toast.info('Export cancelled', {
      description: 'The export request has been cancelled'
    });
  }, [updateExportState]);

  const isExporting = useCallback((type: 'pdf' | 'excel') => {
    return exportStates[type].stage !== 'idle';
  }, [exportStates]);

  const getExportButtonText = useCallback((type: 'pdf' | 'excel', selectedCount: number) => {
    const state = exportStates[type];
    
    switch (state.stage) {
      case 'authenticating':
        return 'Authenticating...';
      case 'queuing':
        return 'Queuing Export...';
      case 'queued':
        return 'Queued Successfully!';
      case 'failed':
        return 'Export Failed - Retry';
      default:
        return `Queue ${type.toUpperCase()} Export (${selectedCount})`;
    }
  }, [exportStates]);

  const getExportButtonVariant = useCallback((type: 'pdf' | 'excel') => {
    const state = exportStates[type];
    
    switch (state.stage) {
      case 'queued':
        return 'default' as const;
      case 'failed':
        return 'destructive' as const;
      default:
        return 'outline' as const;
    }
  }, [exportStates]);

  return {
    exportStates,
    exportStats,
    startExport,
    cancelExport,
    isExporting,
    getExportButtonText,
    getExportButtonVariant,
    updateExportState
  };
} 
import { Timestamp } from 'firebase/firestore';

export interface ExportQueueItem {
  id: string;
  userId: string;
  type: 'pdf' | 'excel';
  status: 'pending' | 'processing' | 'completed' | 'failed';
  checklistIds: string[];
  checklistCount: number;
  createdAt: Timestamp;
  startedAt?: Timestamp;
  completedAt?: Timestamp;
  progress: {
    currentIndex: number;
    totalCount: number;
    percentage: number;
    stage: 'queued' | 'generating' | 'merging' | 'uploading' | 'complete';
    currentChecklist?: string;
    estimatedTimeRemaining?: number; // in seconds
    processingSpeed?: number; // items per minute
    averageTimePerItem?: number; // in seconds
  };
  result?: {
    fileName: string;
    fileSize: number;
    downloadUrl: string;
    storagePath: string;
    processedCount: number;
    failedCount: number;
    failedChecklists: string[];
    expiresAt: Timestamp; // 7 days from completion
  };
  error?: string;
  metadata: {
    clientName?: string;
    dateRange?: string;
    exportMethod: 'cloud-function';
    requestedBy: string; // user display name
  };
}

export interface ExportQueueOptions {
  priority?: 'normal' | 'high';
  notifyOnComplete?: boolean;
  customFileName?: string;
}

export interface ExportUploadResult {
  downloadUrl: string;
  storagePath: string;
  fileName: string;
  fileSize: number;
  expiresAt: Date;
}

export interface ExportMetadata {
  checklistCount: number;
  clientName?: string;
  dateRange?: string;
  requestedBy: string;
} 
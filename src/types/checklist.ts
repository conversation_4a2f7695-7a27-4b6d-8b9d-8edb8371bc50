export type CheckStatus = 'OK' | 'Faulty' | 'N/A' | 'Missing';

export type SyncStatus = 'local-only' | 'ready-to-submit' | 'synced' | 'pending' | 'conflict' | 'error';

export interface SyncMetadata {
  status: SyncStatus;
  lastSyncedAt?: string;
  lastLocalUpdateAt: string;
  cloudVersion?: number;
  localVersion: number;
  cloudDocumentId?: string; // Track the actual cloud document ID
  conflictData?: ChecklistData; // Store conflicting cloud data
  conflictReason?: string; // Reason for the conflict
  error?: string;
}

export interface GeneralInfo {
  clientName: string;
  building: string;
  inspectedBy: string;
  approvedBy: string;
  date: string;
  ppmAttempt: number;
  equipmentName: string;
  location: string;
  tagNo: string;
}

export interface MechanicalCheck {
  airflowVelocity?: number; // M/S
  beltWearPulleyAlignment: CheckStatus;
  bladeImpellerDamage: CheckStatus;
  boltSetScrewTightness: CheckStatus;
  bladeTipClearance: CheckStatus;
  excessiveVibration: CheckStatus;
  fanGuardProtection: CheckStatus;
  fanPowerOff: CheckStatus;
  motorOverheating: CheckStatus;
  rotationDirection: CheckStatus;
  cleanBladesHousing: CheckStatus;
  dustDebrisRemoval: CheckStatus;
  erraticOperation: CheckStatus;
  inletVanesOperation: CheckStatus;
  bearingLubrication: CheckStatus;
  noObstructionsBackflow: CheckStatus;
  physicalDamageStability: CheckStatus;
  speedRpm?: number; // RPM
  springMountVibrationIsolator: CheckStatus;
  unusualSoundDecibel?: number; // Decibel
}

export interface ElectricalCheck {
  bmsControlsInterlocks: CheckStatus;
  burntMarksDiscolorMelted: CheckStatus;
  circuitBreakerFunctional: CheckStatus;
  contractorsBreakers: CheckStatus;
  fireAlarmConnected: CheckStatus;
  fuseTerminals: CheckStatus;
  mccPowerOffBreaker: CheckStatus;
  signsLiquidLeaks: CheckStatus;
  tripSettingsFunction: CheckStatus;
  controlRelaysOperations: CheckStatus;
  currentAmps?: number; // Amps
  doorsCoversCloseProperly: CheckStatus;
  frayingExposedWires: CheckStatus;
  highLowSpeedVerification: CheckStatus;
  indicationsOnOffTrip: CheckStatus;
  looseWiresToBeTightened: CheckStatus;
  motorPowerKw?: number; // KW
  potentialVoltage?: number; // Voltage
  selectorHandStopAuto: CheckStatus;
  testEmergencyStopButton: CheckStatus;
}

export interface SequenceControlsCheck {
  dptDifferentialPressureTransmitter: CheckStatus;
  erraticOperationMalfunctioning: CheckStatus;
  indicationsOnOffTrip: CheckStatus;
  mccOffOverrideFunction: CheckStatus;
  msfdDamperFunctional: CheckStatus;
  offWithDuctDetectorActivation: CheckStatus;
  overrideFscsPanelStatus: CheckStatus;
  sameTagNameInMccFan: CheckStatus;
  selectorRunStopAuto: CheckStatus;
  vfdVariableFrequencyDrive: CheckStatus;
}

export interface ChecklistData {
  id: string;
  createdAt: string;
  updatedAt: string;
  userId?: string; // Associate with authenticated user
  equipmentTagId?: string; // Reference to equipment tag
  originalLocalId?: string; // Track original local ID when uploaded to cloud
  syncMetadata: SyncMetadata;
  isCompleted: boolean; // Marked as complete when synced to cloud
  completedAt?: string; // Timestamp when marked as complete
  completedBy?: string; // User ID who marked it as complete
  generalInfo: GeneralInfo;
  mechanicalChecks: MechanicalCheck;
  electricalChecks: ElectricalCheck;
  sequenceControlsChecks: SequenceControlsCheck;
  remarks: string;
  beforeImage?: string; // Firebase Storage URL
  afterImage?: string; // Firebase Storage URL
  inspectorSignature?: string; // Firebase Storage URL
  clientSignature?: string; // Firebase Storage URL
  // Storage metadata for cleanup
  imageMetadata?: {
    beforeImage?: { path: string; originalName: string };
    afterImage?: { path: string; originalName: string };
    inspectorSignature?: { path: string; originalName: string };
    clientSignature?: { path: string; originalName: string };
  };
  // PDF caching metadata (optional for backward compatibility)
  pdfMetadata?: {
    url?: string;           // Firebase Storage URL
    path?: string;          // Storage path for cleanup
    generatedAt?: string;   // ISO timestamp
    contentHash?: string;   // Hash of checklist content
    isStale?: boolean;      // Mark for regeneration
    size?: number;          // File size in bytes
  };
}

export interface ChecklistSummary {
  totalOk: number;
  totalFaulty: number;
  totalNA: number;
  totalMissing: number;
  totalChecks: number;
}

export type ChecklistField = keyof (MechanicalCheck & ElectricalCheck & SequenceControlsCheck);

export interface ChecklistFieldConfig {
  key: ChecklistField;
  label: string;
  type: 'status' | 'number';
  unit?: string;
  section: 'mechanical' | 'electrical' | 'sequence';
} 
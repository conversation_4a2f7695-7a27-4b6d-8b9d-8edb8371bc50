import { Timestamp } from 'firebase/firestore';

// User roles enum
export enum UserRole {
  USER = 'user',
  TECHNICIAN = 'technician', 
  ADMIN = 'admin'
}

// User document interface for Firestore
export interface UserDocument {
  uid: string;
  email: string;
  displayName: string | null;
  photoURL: string | null;
  role: UserRole;
  createdAt: Timestamp | string;
  updatedAt: Timestamp | string;
  isActive: boolean;
  metadata: {
    lastLoginAt: Timestamp | string | null;
    emailVerified: boolean;
    creationMethod: 'email' | 'google' | 'other';
  };
}

// Extended user interface that includes Firestore data
export interface ExtendedUser {
  // Firebase Auth fields
  uid: string;
  email: string | null;
  displayName: string | null;
  photoURL: string | null;
  emailVerified: boolean;
  
  // Firestore user document fields
  role: UserRole;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
  metadata: {
    lastLoginAt: string | null;
    emailVerified: boolean;
    creationMethod: 'email' | 'google' | 'other';
  };
}

// Role permissions helper
export const ROLE_PERMISSIONS = {
  [UserRole.USER]: {
    canCreateChecklists: true,
    canViewOwnChecklists: true,
    canEditOwnChecklists: true,
    canDeleteOwnChecklists: true,
    canViewAllChecklists: false,
    canManageUsers: false,
    canViewReports: false,
  },
  [UserRole.TECHNICIAN]: {
    canCreateChecklists: true,
    canViewOwnChecklists: true,
    canEditOwnChecklists: true,
    canDeleteOwnChecklists: true,
    canViewAllChecklists: true,
    canManageUsers: false,
    canViewReports: true,
  },
  [UserRole.ADMIN]: {
    canCreateChecklists: true,
    canViewOwnChecklists: true,
    canEditOwnChecklists: true,
    canDeleteOwnChecklists: true,
    canViewAllChecklists: true,
    canManageUsers: true,
    canViewReports: true,
  }
} as const;

// Helper functions
export const hasPermission = (role: UserRole, permission: keyof typeof ROLE_PERMISSIONS[UserRole.USER]): boolean => {
  return ROLE_PERMISSIONS[role][permission];
};

export const isAdmin = (role: UserRole): boolean => {
  return role === UserRole.ADMIN;
};

export const canManageUsers = (role: UserRole): boolean => {
  return hasPermission(role, 'canManageUsers');
};

export const getRoleDisplayName = (role: UserRole): string => {
  switch (role) {
    case UserRole.USER:
      return 'User';
    case UserRole.TECHNICIAN:
      return 'Technician';
    case UserRole.ADMIN:
      return 'Administrator';
    default:
      return 'User';
  }
};

export const getRoleColor = (role: UserRole): string => {
  switch (role) {
    case UserRole.USER:
      return 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-300';
    case UserRole.TECHNICIAN:
      return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
    case UserRole.ADMIN:
      return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
    default:
      return 'bg-slate-100 text-slate-800 dark:bg-slate-900/30 dark:text-slate-300';
  }
}; 
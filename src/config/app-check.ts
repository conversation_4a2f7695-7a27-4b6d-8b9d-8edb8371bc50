import { initializeAppCheck, ReCaptchaV3Provider, getToken } from 'firebase/app-check';
import { getFirebaseApp } from './firebase-config';

// App Check configuration interface
export interface AppCheckConfig {
  provider: 'recaptcha-v3' | 'debug' | 'custom';
  siteKey?: string;
  debugToken?: string;
  isTokenAutoRefreshEnabled: boolean;
  forceRefresh: boolean;
}

// Environment-based configuration
const getAppCheckConfig = (): AppCheckConfig => {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isTest = process.env.NODE_ENV === 'test';
  const isLocalhost = typeof window !== 'undefined' && (
    window.location.hostname === 'localhost' || 
    window.location.hostname === '127.0.0.1' ||
    window.location.hostname === '::1'
  );
  
  // Force debug mode if on localhost, regardless of NODE_ENV
  if (isDevelopment || isTest || isLocalhost) {
    return {
      provider: 'debug',
      debugToken: process.env.NEXT_PUBLIC_FIREBASE_APPCHECK_DEBUG_TOKEN,
      isTokenAutoRefreshEnabled: true,
      forceRefresh: false
    };
  }
  
  // Use reCAPTCHA v3 in production
  return {
    provider: 'recaptcha-v3',
    siteKey: process.env.NEXT_PUBLIC_FIREBASE_APPCHECK_RECAPTCHA_SITE_KEY,
    isTokenAutoRefreshEnabled: true,
    forceRefresh: false
  };
};

// Global App Check instance
let appCheckInstance: any = null;
let initializationPromise: Promise<any> | null = null;

/**
 * Initialize Firebase App Check
 * Must be called before using other Firebase services
 */
export const initializeFirebaseAppCheck = async (): Promise<void> => {
  // Return existing initialization promise if already in progress
  if (initializationPromise) {
    return initializationPromise;
  }

  // Return immediately if already initialized
  if (appCheckInstance) {
    return Promise.resolve();
  }

  // Only initialize App Check in the browser
  if (typeof window === 'undefined') {
    console.log('App Check: Skipping initialization on server side');
    return Promise.resolve();
  }

  // Configuration constants
  const INITIALIZATION_TIMEOUT = 15000; // 15 seconds for initialization
  const TOKEN_TIMEOUT = 8000; // 8 seconds for token fetch
  const MAX_RETRIES = 2;

  initializationPromise = (async () => {
    try {
      const app = getFirebaseApp();
      const config = getAppCheckConfig();

      console.log(`App Check: Initializing with ${config.provider} provider`);

      let provider;
      
      switch (config.provider) {
        case 'recaptcha-v3':
          if (!config.siteKey) {
            throw new Error('reCAPTCHA v3 site key is required for production');
          }
          provider = new ReCaptchaV3Provider(config.siteKey);
          break;
          
        case 'debug':
          if (config.debugToken) {
            // Set debug token for development
            (globalThis as any).FIREBASE_APPCHECK_DEBUG_TOKEN = config.debugToken;
          } else {
            console.warn('App Check: Debug token not provided, App Check may not work in development');
          }
          provider = new ReCaptchaV3Provider('6LdJaP8pAAAAANkR5JVJOaKIYLZPRULaZm47QKLm'); // Dummy key for debug
          break;
          
        default:
          throw new Error(`Unsupported App Check provider: ${config.provider}`);
      }

      // Initialize with timeout
      const initPromise = Promise.resolve().then(() => {
        appCheckInstance = initializeAppCheck(app, {
          provider,
          isTokenAutoRefreshEnabled: config.isTokenAutoRefreshEnabled
        });
        return appCheckInstance;
      });

      const timeoutPromise = new Promise((_, reject) =>
        setTimeout(() => reject(new Error('App Check initialization timeout')), INITIALIZATION_TIMEOUT)
      );

      await Promise.race([initPromise, timeoutPromise]);
      console.log('App Check: Initialized successfully');

      // Enhanced token verification with retry logic
      let tokenObtained = false;
      for (let attempt = 1; attempt <= MAX_RETRIES && !tokenObtained; attempt++) {
        try {
          console.log(`App Check: Attempting to get initial token (attempt ${attempt}/${MAX_RETRIES})`);
          
          const tokenPromise = getToken(appCheckInstance, config.forceRefresh);
          const tokenTimeoutPromise = new Promise((_, reject) => 
            setTimeout(() => reject(new Error('Token fetch timeout')), TOKEN_TIMEOUT)
          );
          
          await Promise.race([tokenPromise, tokenTimeoutPromise]);
          console.log('App Check: Initial token obtained successfully');
          tokenObtained = true;
        } catch (tokenError) {
          console.warn(`App Check: Token attempt ${attempt} failed:`, tokenError);
          
          if (attempt < MAX_RETRIES) {
            // Wait before retry with exponential backoff
            const waitTime = 1000 * Math.pow(2, attempt - 1);
            console.log(`App Check: Waiting ${waitTime}ms before retry...`);
            await new Promise(resolve => setTimeout(resolve, waitTime));
          }
        }
      }

      if (!tokenObtained) {
        console.warn('App Check: Failed to obtain initial token after all retries, but App Check is initialized');
        // Don't throw here - the app can still function, and tokens will be retried automatically
      }

    } catch (error) {
      console.error('App Check: Initialization failed:', error);
      
      // In development, we might want to continue without App Check
      if (process.env.NODE_ENV === 'development') {
        console.warn('App Check: Continuing without App Check in development mode');
        return;
      }
      
      // In production, log error but don't throw to prevent app breakage
      console.warn('App Check: Continuing with degraded functionality due to initialization failure');
      // Most Firebase operations will still work, they'll just bypass App Check verification
    }
  })();

  return initializationPromise;
};

/**
 * Get the App Check instance
 */
export const getAppCheckInstance = (): any => {
  return appCheckInstance;
};

/**
 * Get an App Check token manually
 */
export const getAppCheckToken = async (forceRefresh: boolean = false): Promise<string | null> => {
  if (!appCheckInstance) {
    console.warn('App Check: Not initialized, cannot get token');
    return null;
  }

  const TOKEN_TIMEOUT = 8000; // 8 seconds timeout
  const MAX_RETRIES = 2;

  for (let attempt = 1; attempt <= MAX_RETRIES; attempt++) {
    try {
      console.log(`App Check: Getting token (attempt ${attempt}/${MAX_RETRIES}, forceRefresh: ${forceRefresh})`);
      
      const tokenPromise = getToken(appCheckInstance, forceRefresh);
      const timeoutPromise = new Promise<never>((_, reject) =>
        setTimeout(() => reject(new Error('Token fetch timeout')), TOKEN_TIMEOUT)
      );
      
      const appCheckTokenResponse = await Promise.race([tokenPromise, timeoutPromise]);
      console.log('App Check: Token obtained successfully');
      return appCheckTokenResponse.token;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      console.warn(`App Check: Token attempt ${attempt} failed:`, errorMessage);
      
      if (attempt < MAX_RETRIES) {
        // Wait before retry with exponential backoff
        const waitTime = 1000 * Math.pow(2, attempt - 1);
        console.log(`App Check: Waiting ${waitTime}ms before token retry...`);
        await new Promise(resolve => setTimeout(resolve, waitTime));
      } else {
        console.error('App Check: Failed to get token after all retries:', errorMessage);
      }
    }
  }
  
  return null;
};

/**
 * Check if App Check is initialized and working
 */
export const isAppCheckInitialized = (): boolean => {
  return appCheckInstance !== null;
};

/**
 * Validate App Check configuration
 */
export const validateAppCheckConfig = (): { isValid: boolean; errors: string[] } => {
  const errors: string[] = [];
  const config = getAppCheckConfig();
  
  if (config.provider === 'recaptcha-v3' && !config.siteKey) {
    errors.push('reCAPTCHA v3 site key is missing');
  }
  
  if (config.provider === 'debug' && !config.debugToken) {
    errors.push('Debug token is missing for development environment');
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Reset App Check instance (for testing)
 */
export const resetAppCheck = (): void => {
  appCheckInstance = null;
  initializationPromise = null;
};

export default {
  initializeFirebaseAppCheck,
  getAppCheckInstance,
  getAppCheckToken,
  isAppCheckInitialized,
  validateAppCheckConfig,
  resetAppCheck
}; 
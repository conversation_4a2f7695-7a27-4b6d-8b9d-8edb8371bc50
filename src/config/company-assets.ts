/**
 * Company Assets Configuration
 * Centralized configuration for company logo and signature assets
 * Uses public Firebase Storage URLs for optimal performance
 */

export const COMPANY_ASSETS = {
  logo: {
    // Public Firebase Storage URL (no token required due to storage rules)
    url: 'https://firebasestorage.googleapis.com/v0/b/auburn-engineering.firebasestorage.app/o/assets%2Flogo.jpeg?alt=media',
    fallback: '/images/logo.jpeg',
    alt: 'Auburn Engineering Logo'
  },
  signature: {
    // Public Firebase Storage URL (no token required due to storage rules)
    url: 'https://firebasestorage.googleapis.com/v0/b/auburn-engineering.firebasestorage.app/o/assets%2Fsignature.png?alt=media',
    fallback: '/images/signature.png',
    alt: 'Engineer Signature'
  }
} as const;

export type CompanyAssetKey = keyof typeof COMPANY_ASSETS;

/**
 * Get asset configuration by key
 */
export function getAssetConfig(key: CompanyAssetKey) {
  return COMPANY_ASSETS[key];
}

/**
 * Get all asset URLs for environment variables or external usage
 */
export function getAssetUrls() {
  return {
    logoUrl: COMPANY_ASSETS.logo.url,
    signatureUrl: COMPANY_ASSETS.signature.url
  };
}

/**
 * Get public asset URL (for use in environments where proxies aren't needed)
 */
export function getPublicAssetUrl(key: CompanyAssetKey): string {
  return COMPANY_ASSETS[key].url;
} 
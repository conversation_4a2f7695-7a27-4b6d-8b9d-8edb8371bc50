import { initializeApp, getApps, getApp, FirebaseApp } from 'firebase/app';
import { getAuth, Auth } from 'firebase/auth';
import { getAnalytics, Analytics } from 'firebase/analytics';
import { 
  getFirestore, 
  initializeFirestore, 
  Firestore, 
  persistentLocalCache, 
  persistentMultipleTabManager 
} from 'firebase/firestore';
import { getFunctions, Functions } from 'firebase/functions';
import { getStorage, FirebaseStorage } from 'firebase/storage';
import { initializeFirebaseAppCheck } from './app-check';

const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
};

// Global flag to track if we've validated config
let configValidated = false;

// Validate Firebase configuration
function validateFirebaseConfig() {
  if (configValidated) return; // Skip if already validated
  
  const requiredKeys: (keyof typeof firebaseConfig)[] = ['apiKey', 'authDomain', 'projectId', 'storageBucket', 'messagingSenderId', 'appId'];
  const missingKeys = requiredKeys.filter(key => !firebaseConfig[key]);
  
  if (missingKeys.length > 0) {
    console.error('Missing Firebase configuration keys:', missingKeys);
    throw new Error(`Firebase configuration is incomplete. Missing: ${missingKeys.join(', ')}`);
  }
  
  configValidated = true;
  console.log('Firebase configuration validated');
}

// Singleton instances
let firebaseApp: FirebaseApp | null = null;
let firebaseAuth: Auth | null = null;
let firebaseFirestore: Firestore | null = null;
let firebaseFunctions: Functions | null = null;
let firebaseStorage: FirebaseStorage | null = null;
let firebaseAnalytics: Analytics | null = null;

// Initialize Firebase App (singleton)
export function getFirebaseApp(): FirebaseApp {
  if (!firebaseApp) {
    try {
      validateFirebaseConfig();
      
      if (getApps().length === 0) {
        firebaseApp = initializeApp(firebaseConfig);
        console.log('Firebase app initialized successfully');
        
        // Initialize App Check after Firebase app but before other services
        if (typeof window !== 'undefined') {
          initializeFirebaseAppCheck().catch(error => {
            console.warn('App Check initialization failed, continuing without App Check:', error);
          });
        }
      } else {
        firebaseApp = getApp();
        console.log('Firebase app already initialized, using existing instance');
      }
    } catch (error) {
      console.error('Failed to initialize Firebase app:', error);
      throw error;
    }
  }
  return firebaseApp;
}

// Initialize Firebase Auth (singleton) - Lazy initialization
export function getFirebaseAuth(): Auth {
  if (!firebaseAuth) {
    try {
      firebaseAuth = getAuth(getFirebaseApp());
      console.log('Firebase Auth initialized successfully');
      
      // Only log current user in development
      if (process.env.NODE_ENV === 'development') {
        const currentUser = firebaseAuth.currentUser;
        console.log('Firebase Auth current user on init:', currentUser ? { 
          uid: currentUser.uid, 
          email: currentUser.email,
          emailVerified: currentUser.emailVerified 
        } : null);
      }
      
    } catch (error) {
      console.error('Failed to initialize Firebase Auth:', error);
      throw error;
    }
  }
  return firebaseAuth;
}

// Initialize Firestore with persistence (singleton) - Lazy initialization
export function getFirebaseFirestore(enablePersistence: boolean = false): Firestore {
  if (!firebaseFirestore) {
    const app = getFirebaseApp();
    
    if (enablePersistence) {
      try {
        // Try to initialize with persistence
        firebaseFirestore = initializeFirestore(app, {
          localCache: persistentLocalCache({
            cacheSizeBytes: 50 * 1024 * 1024, // Reduced to 50MB for better performance
            tabManager: persistentMultipleTabManager()
          })
        });
        console.log('Firestore initialized with persistence (50MB cache)');
      } catch (error: any) {
        // If already initialized, get the existing instance
        if (error.code === 'already-initialized' || error.message?.includes('already been called')) {
          console.log('Firestore already initialized with persistence, using existing instance');
          firebaseFirestore = getFirestore(app);
        } else {
          console.error('Failed to initialize Firestore with persistence:', error);
          // Fallback to basic Firestore
          firebaseFirestore = getFirestore(app);
          console.log('Fallback: Firestore initialized without persistence');
        }
      }
    } else {
      // Basic Firestore without persistence
      firebaseFirestore = getFirestore(app);
      console.log('Firestore initialized without persistence');
    }
  }
  return firebaseFirestore;
}

// Initialize Firebase Functions (singleton) - Lazy initialization
export function getFirebaseFunctions(): Functions {
  if (!firebaseFunctions) {
    firebaseFunctions = getFunctions(getFirebaseApp());
    console.log('Firebase Functions initialized');
  }
  return firebaseFunctions;
}

// Initialize Firebase Analytics (singleton, browser only) - Lazy initialization
export function getFirebaseAnalytics(): Analytics | null {
  if (typeof window !== 'undefined' && !firebaseAnalytics) {
    try {
      firebaseAnalytics = getAnalytics(getFirebaseApp());
      console.log('Firebase Analytics initialized');
    } catch (error) {
      console.warn('Analytics not available:', error);
      firebaseAnalytics = null;
    }
  }
  return firebaseAnalytics;
}

// Initialize Firebase Storage (singleton) - Lazy initialization
export function getFirebaseStorage(): FirebaseStorage {
  if (!firebaseStorage) {
    try {
      const app = getFirebaseApp();
      firebaseStorage = getStorage(app);
      console.log('Firebase Storage initialized');
    } catch (error) {
      console.error('Failed to initialize Firebase Storage:', error);
      throw new Error(`Firebase Storage initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }
  return firebaseStorage;
}

// Check if Firebase services are initialized
export function isFirebaseInitialized(): boolean {
  return getApps().length > 0;
}

// Validate that Firebase configuration includes Storage
export function validateFirebaseStorageConfig(): boolean {
  try {
    const hasStorageBucket = !!firebaseConfig.storageBucket;
    if (!hasStorageBucket) {
      console.warn('Firebase Storage not configured: missing storageBucket');
      return false;
    }
    return true;
  } catch (error) {
    console.error('Error validating Firebase Storage config:', error);
    return false;
  }
}

// Reset Firebase instances (for testing or reinitialization)
export function resetFirebaseInstances(): void {
  firebaseApp = null;
  firebaseAuth = null;
  firebaseFirestore = null;
  firebaseFunctions = null;
  firebaseStorage = null;
  firebaseAnalytics = null;
  configValidated = false;
} 
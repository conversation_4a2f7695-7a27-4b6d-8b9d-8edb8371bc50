import { ChecklistFieldConfig } from '@/types/checklist';

export const MECHANICAL_CHECKS: ChecklistFieldConfig[] = [
  {
    key: 'airflowVelocity',
    label: 'Airflow in M/S - Face Velocity',
    type: 'number',
    unit: 'M/S',
    section: 'mechanical'
  },
  {
    key: 'beltWearPulleyAlignment',
    label: 'Belt wear & Pulley alignment',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'bladeImpellerDamage',
    label: 'Blade / Impeller damage crack',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'boltSetScrewTightness',
    label: 'Bolt & Set Screw Tightness',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'bladeTipClearance',
    label: 'Check Blade tip clearance',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'excessiveVibration',
    label: 'Check Excessive Vibration',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'fanGuardProtection',
    label: 'Check Fan Guard protection',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'fanPowerOff',
    label: 'Check Fan Power Off',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'motorOverheating',
    label: 'Check Motor Overheating',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'rotationDirection',
    label: 'Check Rotation Clockwise/Anti',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'cleanBladesHousing',
    label: 'Clean Blades & Housing',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'dustDebrisRemoval',
    label: 'Dust & Debris removal',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'erraticOperation',
    label: 'Erratic Operation / Malfunctioning',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'inletVanesOperation',
    label: 'Inlet Vanes Operation freedom',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'bearingLubrication',
    label: 'Lubrication on Bearings',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'noObstructionsBackflow',
    label: 'No obstructions / Backflow',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'physicalDamageStability',
    label: 'Physical Damage stability of fan',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'speedRpm',
    label: 'Speed in Rotation per Minute',
    type: 'number',
    unit: 'RPM',
    section: 'mechanical'
  },
  {
    key: 'springMountVibrationIsolator',
    label: 'Spring Mount / Vibration Isolator',
    type: 'status',
    section: 'mechanical'
  },
  {
    key: 'unusualSoundDecibel',
    label: 'Unusual Sound in Decibel',
    type: 'number',
    unit: 'dB',
    section: 'mechanical'
  }
];

export const ELECTRICAL_CHECKS: ChecklistFieldConfig[] = [
  {
    key: 'bmsControlsInterlocks',
    label: 'BMS Controls & Interlocks',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'burntMarksDiscolorMelted',
    label: 'Burnt Marks, Discolor, Melted',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'circuitBreakerFunctional',
    label: 'Check Circuit Breaker Functional',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'contractorsBreakers',
    label: 'Check Contractors & Breakers',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'fireAlarmConnected',
    label: 'Check Fire Alarm Connected',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'fuseTerminals',
    label: 'Check Fuse & Terminals',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'mccPowerOffBreaker',
    label: 'Check MCC Power Off - Breaker',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'signsLiquidLeaks',
    label: 'Check Signs of liquid Leaks',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'tripSettingsFunction',
    label: 'Check Trip settings & Function',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'controlRelaysOperations',
    label: 'Control Relays operations',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'currentAmps',
    label: 'Current in Amps',
    type: 'number',
    unit: 'A',
    section: 'electrical'
  },
  {
    key: 'doorsCoversCloseProperly',
    label: 'Doors & Covers close properly',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'frayingExposedWires',
    label: 'Fraying / Exposed Wires',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'highLowSpeedVerification',
    label: 'High / Low Speed Verification',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'indicationsOnOffTrip',
    label: 'Indications [ON] [OFF] & [TRIP]',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'looseWiresToBeTightened',
    label: 'Loose wires to be tightened',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'motorPowerKw',
    label: 'Motor Power in KW',
    type: 'number',
    unit: 'KW',
    section: 'electrical'
  },
  {
    key: 'potentialVoltage',
    label: 'Potential Voltage',
    type: 'number',
    unit: 'V',
    section: 'electrical'
  },
  {
    key: 'selectorHandStopAuto',
    label: 'Selector [HAND] [STOP] [AUTO]',
    type: 'status',
    section: 'electrical'
  },
  {
    key: 'testEmergencyStopButton',
    label: 'Test Emergency Stop Button',
    type: 'status',
    section: 'electrical'
  }
];

export const SEQUENCE_CONTROLS_CHECKS: ChecklistFieldConfig[] = [
  {
    key: 'dptDifferentialPressureTransmitter',
    label: 'DPT - Differential Pressure Transmitter',
    type: 'status',
    section: 'sequence'
  },
  {
    key: 'erraticOperationMalfunctioning',
    label: 'Erratic Operation / Malfunctioning',
    type: 'status',
    section: 'sequence'
  },
  {
    key: 'indicationsOnOffTrip',
    label: 'Indications [ON] [OFF] & [TRIP]',
    type: 'status',
    section: 'sequence'
  },
  {
    key: 'mccOffOverrideFunction',
    label: 'MCC Off - Override Function',
    type: 'status',
    section: 'sequence'
  },
  {
    key: 'msfdDamperFunctional',
    label: 'MSFD - Damper Functional',
    type: 'status',
    section: 'sequence'
  },
  {
    key: 'offWithDuctDetectorActivation',
    label: 'Off with Duct Detector activation',
    type: 'status',
    section: 'sequence'
  },
  {
    key: 'overrideFscsPanelStatus',
    label: 'Override (FSCS) Panel Status',
    type: 'status',
    section: 'sequence'
  },
  {
    key: 'sameTagNameInMccFan',
    label: 'Same Tag Name in MCC & Fan',
    type: 'status',
    section: 'sequence'
  },
  {
    key: 'selectorRunStopAuto',
    label: 'Selector [RUN] [STOP] [AUTO]',
    type: 'status',
    section: 'sequence'
  },
  {
    key: 'vfdVariableFrequencyDrive',
    label: 'VFD - Variable Frequency Drive',
    type: 'status',
    section: 'sequence'
  }
];

export const ALL_CHECKS = [
  ...MECHANICAL_CHECKS,
  ...ELECTRICAL_CHECKS,
  ...SEQUENCE_CONTROLS_CHECKS
];

export const CHECK_STATUS_OPTIONS = [
  { value: 'OK', label: 'OK' },
  { value: 'Faulty', label: 'Faulty' },
  { value: 'N/A', label: 'N/A' },
  { value: 'Missing', label: 'Missing' }
] as const;

/**
 * Universal field display order configuration
 * This ensures consistent ordering across ALL export methods
 */
export const FIELD_DISPLAY_ORDER = {
  sections: ['mechanical', 'electrical', 'sequence'] as const,
  mechanical: MECHANICAL_CHECKS.map(f => f.key),
  electrical: ELECTRICAL_CHECKS.map(f => f.key),
  sequence: SEQUENCE_CONTROLS_CHECKS.map(f => f.key)
} as const;

/**
 * Get the display order index for any field
 * Used for consistent sorting across all export methods
 */
export function getFieldDisplayOrder(fieldKey: string): number {
  return ALL_CHECKS.findIndex(field => field.key === fieldKey);
}

/**
 * Validate that field ordering matches the configured order
 */
export function validateExportFieldOrder(
  exportedFields: string[],
  section?: 'mechanical' | 'electrical' | 'sequence'
): { isValid: boolean; issues: string[] } {
  const issues: string[] = [];
  
  if (section) {
    const expectedOrder = FIELD_DISPLAY_ORDER[section];
    const sectionFields = exportedFields.filter(field => 
      expectedOrder.includes(field as any)
    );
    
    sectionFields.forEach((field, index) => {
      const expectedIndex = expectedOrder.indexOf(field as any);
      if (expectedIndex !== index) {
        issues.push(`Field "${field}" in wrong position: expected ${expectedIndex}, got ${index}`);
      }
    });
  } else {
    // Validate overall order
    exportedFields.forEach((field, index) => {
      const expectedIndex = getFieldDisplayOrder(field);
      if (expectedIndex !== -1 && expectedIndex !== index) {
        issues.push(`Field "${field}" in wrong position: expected ${expectedIndex}, got ${index}`);
      }
    });
  }
  
  return {
    isValid: issues.length === 0,
    issues
  };
} 
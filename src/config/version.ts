export interface AppVersion {
  major: number;
  minor: number;
  patch: number;
  build?: string;
  timestamp: string;
  commitHash?: string;
}

export interface VersionInfo {
  current: AppVersion;
  displayName: string;
  releaseDate: string;
  releaseNotes?: string[];
}

// Current application version
export const APP_VERSION: AppVersion = {
  major: 1,
  minor: 0,
  patch: 1,
  build: process.env.NEXT_PUBLIC_BUILD_NUMBER || 'dev',
  timestamp: '2025-06-08T08:03:55.443Z',
  commitHash: process.env.NEXT_PUBLIC_COMMIT_HASH || 'local'
};

// Version utilities
export class VersionUtils {
  static formatVersion(version: AppVersion): string {
    return `v${version.major}.${version.minor}.${version.patch}`;
  }

  static formatFullVersion(version: AppVersion): string {
    const base = this.formatVersion(version);
    if (version.build && version.build !== 'dev') {
      return `${base}-${version.build}`;
    }
    return base;
  }

  static compareVersions(v1: AppVersion, v2: AppVersion): number {
    if (v1.major !== v2.major) return v1.major - v2.major;
    if (v1.minor !== v2.minor) return v1.minor - v2.minor;
    if (v1.patch !== v2.patch) return v1.patch - v2.patch;
    return 0;
  }

  static isNewerVersion(current: AppVersion, latest: AppVersion): boolean {
    return this.compareVersions(latest, current) > 0;
  }

  static formatBuildInfo(version: AppVersion): string {
    const date = new Date(version.timestamp).toLocaleDateString();
    const time = new Date(version.timestamp).toLocaleTimeString();
    return `Built on ${date} at ${time}`;
  }

  static getShortCommitHash(version: AppVersion): string {
    if (!version.commitHash || version.commitHash === 'local') return 'local';
    return version.commitHash.substring(0, 7);
  }
}

// Current version info
export const CURRENT_VERSION_INFO: VersionInfo = {
  current: APP_VERSION,
  displayName: VersionUtils.formatFullVersion(APP_VERSION),
  releaseDate: '2025-06-08T08:03:55.443Z',
  releaseNotes: [
    'Version 1.0.1 release',
    'Updated system components',
    'Performance improvements'
  ]
};
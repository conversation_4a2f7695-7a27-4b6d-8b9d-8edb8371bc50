"use client";

import { useState } from "react";
import { EquipmentTag } from "@/types/equipment-tag";
import { EquipmentTagService } from "@/lib/services/equipment/equipment-tag-service";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  QrCode, 
  Download, 
  Printer, 
  Copy, 
  Check,
  Calendar,
  Building,
  MapPin,
  User,
  Package,
  Tag as TagIcon
} from "lucide-react";

interface QRCodeDisplayProps {
  equipmentTag: EquipmentTag;
  showDetails?: boolean;
  size?: "sm" | "md" | "lg";
}

export function QRCodeDisplay({ equipmentTag, showDetails = true, size = "md" }: QRCodeDisplayProps) {
  const [copied, setCopied] = useState(false);

  const qrSizes = {
    sm: "w-32 h-32",
    md: "w-48 h-48",
    lg: "w-64 h-64"
  };

  const handleDownload = () => {
    try {
      EquipmentTagService.downloadQRCode(equipmentTag.qrCodeData, equipmentTag.tagNumber);
    } catch (error) {
      console.error("Download failed:", error);
      alert("Failed to download QR code. Please try again.");
    }
  };

  const handlePrint = () => {
    try {
      EquipmentTagService.printQRCode(equipmentTag.qrCodeData, equipmentTag);
    } catch (error) {
      console.error("Print failed:", error);
      alert("Failed to print QR code. Please try again.");
    }
  };

  const handleCopyLink = async () => {
    try {
      // Copy the QR code content as text
      const qrText = `Contractor: ${equipmentTag.contractor}
Client: ${equipmentTag.clientName}
Equipment: ${equipmentTag.equipmentName}
Tag: ${equipmentTag.tagNumber}
Date: ${equipmentTag.dateOfCreation}
Building: ${equipmentTag.building}
Location: ${equipmentTag.location}`;

      if (typeof window !== 'undefined' && navigator.clipboard) {
        await navigator.clipboard.writeText(qrText);
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      }
    } catch (error) {
      console.error("Copy failed:", error);
      alert("Failed to copy QR code content. Please try again.");
    }
  };

  return (
    <Card className="w-full">
      <CardHeader className="text-center">
        <CardTitle className="flex items-center justify-center gap-2">
          <QrCode className="h-5 w-5" />
          Equipment QR Code
        </CardTitle>
        <CardDescription>
          Scan this code to view equipment information
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* QR Code Image */}
        <div className="flex justify-center">
          <div className="p-4 bg-white rounded-lg shadow-inner border">
            <img
              src={equipmentTag.qrCodeData}
              alt={`QR Code for ${equipmentTag.tagNumber}`}
              className={`${qrSizes[size]} object-contain`}
            />
          </div>
        </div>

        {/* Equipment Details */}
        {showDetails && (
          <div className="space-y-4">
            <div className="text-center">
              <Badge variant="outline" className="text-lg px-4 py-2">
                {equipmentTag.tagNumber}
              </Badge>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <User className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Client:</span>
                  <span>{equipmentTag.clientName}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Package className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Equipment:</span>
                  <span>{equipmentTag.equipmentName}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Created:</span>
                  <span>{equipmentTag.dateOfCreation}</span>
                </div>
              </div>

              <div className="space-y-3">
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Building:</span>
                  <span>{equipmentTag.building}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Location:</span>
                  <span>{equipmentTag.location}</span>
                </div>
                
                <div className="flex items-center gap-2">
                  <TagIcon className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">Contractor:</span>
                  <span>{equipmentTag.contractor}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-wrap justify-center gap-3 pt-4 border-t">
          <Button
            onClick={handleDownload}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Download className="h-4 w-4" />
            Download PNG
          </Button>

          <Button
            onClick={handlePrint}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            <Printer className="h-4 w-4" />
            Print
          </Button>

          <Button
            onClick={handleCopyLink}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            {copied ? (
              <>
                <Check className="h-4 w-4 text-green-600" />
                Copied!
              </>
            ) : (
              <>
                <Copy className="h-4 w-4" />
                Copy Text
              </>
            )}
          </Button>
        </div>

        {/* Download Instructions */}
        <div className="text-xs text-muted-foreground text-center space-y-1">
          <p>• Download as PNG for digital use</p>
          <p>• Print for physical equipment labeling</p>
          <p>• Copy text content for sharing</p>
        </div>
      </CardContent>
    </Card>
  );
} 
"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { EquipmentTag } from "@/types/equipment-tag";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent } from "@/components/ui/card";
import { 
  ClipboardCheck, 
  Plus, 
  Building, 
  MapPin, 
  Package, 
  Tag as TagIcon,
  Calendar,
  User
} from "lucide-react";

type EquipmentTagWithChecklistInfo = EquipmentTag & {
  hasChecklists?: boolean;
  checklistCount?: number;
  lastChecklistDate?: string;
  availableAttempts?: number[];
  latestAttempt?: number;
};

interface AttemptSelectionDialogProps {
  isOpen: boolean;
  onClose: () => void;
  equipmentTag: EquipmentTagWithChecklistInfo | null;
}

export function AttemptSelectionDialog({ isOpen, onClose, equipmentTag }: AttemptSelectionDialogProps) {
  const router = useRouter();
  const [selectedAttempt, setSelectedAttempt] = useState<number | null>(null);

  const handleCreateNewChecklist = () => {
    if (!equipmentTag) return;
    
    // Calculate next attempt number
    const nextAttempt = (equipmentTag.latestAttempt || 0) + 1;
    
    // Navigate to checklist page with equipment tag and new attempt
    router.push(`/checklist?equipment=${equipmentTag.id}&attempt=${nextAttempt}`);
    onClose();
  };

  const handleSelectExistingAttempt = async () => {
    if (!equipmentTag || selectedAttempt === null) return;

    try {
      // Find the existing checklist for this equipment and attempt
      const { StorageService } = await import('@/lib/services/storage/storage-adapter');
      const allChecklists = await StorageService.getAllChecklists();
      const existingChecklist = allChecklists.find(checklist =>
        checklist.equipmentTagId === equipmentTag.id &&
        checklist.generalInfo?.ppmAttempt === selectedAttempt
      );

      if (existingChecklist) {
        // Navigate to edit the existing checklist
        router.push(`/checklist?edit=${existingChecklist.id}`);
      } else {
        // No existing checklist found, create new one with the attempt number
        router.push(`/checklist?equipment=${equipmentTag.id}&attempt=${selectedAttempt}`);
      }
    } catch (error) {
      console.error('Error finding existing checklist:', error);
      // Fallback to the original behavior
      router.push(`/checklist?equipment=${equipmentTag.id}&attempt=${selectedAttempt}`);
    }

    onClose();
  };

  const handleViewAllChecklists = () => {
    if (!equipmentTag) return;
    
    // Navigate to checklist page with equipment tag filter
    router.push(`/checklist?equipment=${equipmentTag.id}&view=all`);
    onClose();
  };

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  if (!equipmentTag) return null;

  const availableAttempts = equipmentTag.availableAttempts || [];
  const nextAttempt = (equipmentTag.latestAttempt || 0) + 1;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl sm:max-w-[95vw] max-h-[90vh] overflow-y-auto bg-gray-950 border-gray-800">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-lg sm:text-xl">
            <ClipboardCheck className="h-5 w-5" />
            <span>PPM Attempt Selection</span>
          </DialogTitle>
          <DialogDescription className="text-sm">
            Choose how you want to proceed with the checklist for this equipment.
          </DialogDescription>
        </DialogHeader>

        {/* Equipment Information */}
        <Card className="bg-gray-800 border-gray-700">
          <CardContent className="p-3 sm:p-4">
            <div className="space-y-3">
              <div className="flex flex-col sm:flex-row sm:items-center gap-2">
                <div className="flex items-center gap-2">
                  <TagIcon className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium">{equipmentTag.tagNumber}</span>
                </div>
                <Badge variant="outline" className="text-xs w-fit">
                  {equipmentTag.clientName}
                </Badge>
              </div>

              <div className="grid grid-cols-1 gap-3 text-sm">
                <div className="flex items-center gap-2">
                  <Package className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <span className="break-words">{equipmentTag.equipmentName}</span>
                </div>
                <div className="flex items-center gap-2">
                  <Building className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <span className="break-words">{equipmentTag.building}</span>
                </div>
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                  <span className="break-words">{equipmentTag.location}</span>
                </div>
                {equipmentTag.lastChecklistDate && (
                  <div className="flex items-center gap-2">
                    <Calendar className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    <span className="break-words">Last checklist: {formatDate(equipmentTag.lastChecklistDate)}</span>
                  </div>
                )}
              </div>

              {equipmentTag.hasChecklists && (
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 pt-2 border-t">
                  <ClipboardCheck className="h-4 w-4 text-green-600 flex-shrink-0" />
                  <span className="text-sm text-green-700">
                    {equipmentTag.checklistCount} existing checklist{(equipmentTag.checklistCount || 0) > 1 ? 's' : ''}
                    {availableAttempts.length > 0 && (
                      <span className="block sm:inline sm:ml-2">
                        (PPM attempts: {availableAttempts.join(', ')})
                      </span>
                    )}
                  </span>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        <div className="space-y-4">
          {/* Create New Checklist */}
          <Card className="border-2 border-green-500 bg-gray-900 hover:bg-gray-800 transition-colors">
            <CardContent className="p-3 sm:p-4">
              <div className="flex flex-col gap-3">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <Plus className="h-5 w-5 text-green-400" />
                    <h3 className="font-medium text-white">Create New Checklist</h3>
                  </div>
                  <p className="text-sm text-gray-300">
                    Start a new inspection for PPM attempt #{nextAttempt}
                  </p>
                  <Badge variant="outline" className="bg-green-900 text-green-300 border-green-500 w-fit">
                    PPM Attempt {nextAttempt}
                  </Badge>
                </div>
                <Button
                  onClick={handleCreateNewChecklist}
                  className="bg-green-600 hover:bg-green-700 text-white w-full sm:w-auto sm:self-end"
                  size="sm"
                >
                  <Plus className="h-4 w-4 mr-2 sm:hidden" />
                  Create New
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Continue Existing Attempts */}
          {availableAttempts.length > 0 && (
            <Card className="border-2 border-blue-500 bg-gray-900 hover:bg-gray-800 transition-colors">
              <CardContent className="p-3 sm:p-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-2">
                    <ClipboardCheck className="h-5 w-5 text-blue-400" />
                    <h3 className="font-medium text-white">Continue Existing Attempt</h3>
                  </div>
                  <p className="text-sm text-gray-300">
                    Continue working on a previous PPM attempt
                  </p>

                  <div className="flex flex-wrap gap-2">
                    {availableAttempts.map((attempt) => (
                      <Badge
                        key={attempt}
                        variant={selectedAttempt === attempt ? "default" : "outline"}
                        className={`cursor-pointer transition-colors text-xs px-2 py-1 ${
                          selectedAttempt === attempt
                            ? "bg-blue-600 text-white border-blue-600"
                            : "bg-blue-900 text-blue-300 border-blue-500 hover:bg-blue-800"
                        }`}
                        onClick={() => setSelectedAttempt(selectedAttempt === attempt ? null : attempt)}
                      >
                        PPM Attempt {attempt}
                      </Badge>
                    ))}
                  </div>

                  <Button
                    onClick={handleSelectExistingAttempt}
                    disabled={selectedAttempt === null}
                    variant="outline"
                    className="border-blue-500 text-blue-300 hover:bg-blue-800 hover:text-white disabled:opacity-50 w-full sm:w-auto"
                    size="sm"
                  >
                    <ClipboardCheck className="h-4 w-4 mr-2 sm:hidden" />
                    Continue Selected Attempt
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}

          {/* View All Checklists */}
          {equipmentTag.hasChecklists && (
            <Card className="border-2 border-slate-500 bg-gray-900 hover:bg-gray-800 transition-colors">
              <CardContent className="p-3 sm:p-4">
                <div className="flex flex-col gap-3">
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <User className="h-5 w-5 text-slate-400" />
                      <h3 className="font-medium text-white">View All Checklists</h3>
                    </div>
                    <p className="text-sm text-gray-300">
                      Review all completed and pending checklists for this equipment
                    </p>
                  </div>
                  <Button
                    onClick={handleViewAllChecklists}
                    variant="outline"
                    className="border-slate-500 text-slate-300 hover:bg-slate-800 hover:text-white w-full sm:w-auto sm:self-end"
                    size="sm"
                  >
                    <User className="h-4 w-4 mr-2 sm:hidden" />
                    View All
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>

        <DialogFooter className="flex-col sm:flex-row gap-2">
          <Button variant="outline" onClick={onClose} className="w-full sm:w-auto">
            Cancel
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
} 
"use client";

import { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { equipmentTagSchema, EquipmentTagFormData } from "@/lib/utils/validation";
import { EquipmentTagService } from "@/lib/services/equipment/equipment-tag-service";
import { EquipmentTag } from "@/types/equipment-tag";
import { useAuth } from "@/components/auth/auth-provider";

import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Tag, Building, MapPin, User, Package, Calendar, AlertTriangle } from "lucide-react";

interface EquipmentTagFormProps {
  onSuccess: (tag: EquipmentTag) => void;
  onCancel?: () => void;
  editingTag?: EquipmentTag | null;
}

export function EquipmentTagForm({ onSuccess, onCancel, editingTag }: EquipmentTagFormProps) {
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isCheckingUnique, setIsCheckingUnique] = useState(false);
  const [checklistReferences, setChecklistReferences] = useState<{
    hasReferences: boolean;
    checklistCount: number;
    checklistIds: string[];
  } | null>(null);
  const [showReferenceWarning, setShowReferenceWarning] = useState(false);

  const form = useForm<EquipmentTagFormData>({
    resolver: zodResolver(equipmentTagSchema),
    defaultValues: {
      clientName: editingTag?.clientName || "",
      equipmentName: editingTag?.equipmentName || "",
      tagNumber: editingTag?.tagNumber || "",
      building: editingTag?.building || "",
      location: editingTag?.location || "",
      dateOfCreation: editingTag?.dateOfCreation || new Date().toISOString().split('T')[0],
    },
  });

  // Check for checklist references when editing
  useEffect(() => {
    const checkReferences = async () => {
      if (editingTag) {
        try {
          const references = await EquipmentTagService.getChecklistReferences(editingTag.id);
          setChecklistReferences(references);
          
          // Show warning if there are references and critical fields might be changed
          const currentValues = form.getValues();
          const criticalFieldsChanged = 
            editingTag.tagNumber !== currentValues.tagNumber ||
            editingTag.clientName !== currentValues.clientName ||
            editingTag.equipmentName !== currentValues.equipmentName;
          
          setShowReferenceWarning(references.hasReferences && criticalFieldsChanged);
        } catch (error) {
          console.error('Error checking references:', error);
        }
      }
    };

    checkReferences();
  }, [editingTag, form]);

  // Watch for changes in critical fields to show/hide warning
  const watchedValues = form.watch(['tagNumber', 'clientName', 'equipmentName']);
  useEffect(() => {
    if (editingTag && checklistReferences?.hasReferences) {
      const criticalFieldsChanged = 
        editingTag.tagNumber !== watchedValues[0] ||
        editingTag.clientName !== watchedValues[1] ||
        editingTag.equipmentName !== watchedValues[2];
      
      setShowReferenceWarning(criticalFieldsChanged);
    }
  }, [watchedValues, editingTag, checklistReferences]);

  const onSubmit = async (data: EquipmentTagFormData) => {
    if (!user) {
      alert("You must be signed in to create equipment tags.");
      return;
    }

    setIsSubmitting(true);
    try {
      // Check tag number uniqueness for new tags or when tag number changed
      if (!editingTag || editingTag.tagNumber !== data.tagNumber) {
        setIsCheckingUnique(true);
        const isUnique = await EquipmentTagService.isTagNumberUniqueForClient(
          data.tagNumber, 
          data.clientName,
          editingTag?.id
        );
        setIsCheckingUnique(false);

        if (!isUnique) {
          form.setError("tagNumber", {
            type: "manual",
            message: "This tag number already exists for this client. Please choose a different tag number.",
          });
          setIsSubmitting(false);
          return;
        }
      }

      // Also check if client name changed for existing tag - need to verify uniqueness with new client
      if (editingTag && editingTag.clientName !== data.clientName) {
        setIsCheckingUnique(true);
        const isUnique = await EquipmentTagService.isTagNumberUniqueForClient(
          data.tagNumber, 
          data.clientName,
          editingTag?.id
        );
        setIsCheckingUnique(false);

        if (!isUnique) {
          form.setError("tagNumber", {
            type: "manual",
            message: "This tag number already exists for the new client. Please choose a different tag number.",
          });
          setIsSubmitting(false);
          return;
        }
      }

      let resultTag: EquipmentTag;

      if (editingTag) {
        // Update existing tag
        await EquipmentTagService.updateEquipmentTag(editingTag.id, data);
        // Fetch updated tag
        const updatedTag = await EquipmentTagService.getEquipmentTagById(editingTag.id);
        if (!updatedTag) {
          throw new Error("Failed to fetch updated tag");
        }
        resultTag = updatedTag;
      } else {
        // Create new tag
        resultTag = await EquipmentTagService.createEquipmentTag(data, user.uid);
      }

      form.reset();
      onSuccess(resultTag);
    } catch (error) {
      console.error("Error saving equipment tag:", error);
      
      // Handle referential integrity errors with better user feedback
      if (error instanceof Error && error.message.includes('referenced by')) {
        alert(
          `Cannot save changes: ${error.message}\n\n` +
          `To proceed, you can either:\n` +
          `1. Delete the associated checklists first\n` +
          `2. Only modify non-critical fields (building, location, date)\n` +
          `3. Contact an administrator for assistance`
        );
      } else {
        alert(error instanceof Error ? error.message : "Failed to save equipment tag");
      }
    } finally {
      setIsSubmitting(false);
      setIsCheckingUnique(false);
    }
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Tag className="h-5 w-5" />
          {editingTag ? "Edit Equipment Tag" : "Create New Equipment Tag"}
        </CardTitle>
        <CardDescription>
          {editingTag 
            ? "Update the equipment tag information below."
            : "Fill in the equipment details to generate a new QR code tag."
          }
        </CardDescription>
      </CardHeader>
      <CardContent>
        {/* Warning for equipment tags with checklist references */}
        {showReferenceWarning && checklistReferences && (
          <Alert variant="destructive" className="mb-6">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              <strong>Warning:</strong> This equipment tag is referenced by {checklistReferences.checklistCount} checklist(s). 
              Modifying the tag number, client name, or equipment name will be blocked to maintain data integrity. 
              You can still modify building, location, and date fields.
            </AlertDescription>
          </Alert>
        )}

        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Auto-populated contractor field */}
            <div className="p-4 bg-muted/30 rounded-lg">
              <div className="space-y-1">
                <label className="text-sm font-medium text-muted-foreground">Contractor</label>
                <p className="text-sm font-medium">Auburn Engineering WLL</p>
              </div>
            </div>

            {/* Client Name and Date of Creation */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="clientName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <User className="h-4 w-4" />
                      Client Name
                    </FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Enter client name" 
                        {...field} 
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>
                      The name of the client or organization
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="dateOfCreation"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Calendar className="h-4 w-4" />
                      Date of Creation
                    </FormLabel>
                    <FormControl>
                      <Input 
                        type="date"
                        {...field} 
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>
                      The date when this equipment tag was created
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Equipment Name */}
            <FormField
              control={form.control}
              name="equipmentName"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Package className="h-4 w-4" />
                    Equipment Name
                  </FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter equipment name" 
                      {...field} 
                      disabled={isSubmitting}
                    />
                  </FormControl>
                  <FormDescription>
                    The specific name or model of the equipment
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            {/* Tag Number */}
            <FormField
              control={form.control}
              name="tagNumber"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <Tag className="h-4 w-4" />
                    Tag Number
                  </FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter unique tag number" 
                      {...field} 
                      disabled={isSubmitting || isCheckingUnique}
                    />
                  </FormControl>
                  <FormDescription>
                    A unique identifier for this equipment tag within this client
                  </FormDescription>
                  <FormMessage />
                  {isCheckingUnique && (
                    <div className="flex items-center gap-2 text-sm text-muted-foreground">
                      <Loader2 className="h-3 w-3 animate-spin" />
                      Checking tag number availability...
                    </div>
                  )}
                </FormItem>
              )}
            />

            {/* Building and Location */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="building"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Building className="h-4 w-4" />
                      Building
                    </FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Enter building name" 
                        {...field} 
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>
                      Building or facility name
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <MapPin className="h-4 w-4" />
                      Location
                    </FormLabel>
                    <FormControl>
                      <Input 
                        placeholder="Enter specific location" 
                        {...field} 
                        disabled={isSubmitting}
                      />
                    </FormControl>
                    <FormDescription>
                      Specific location within the building
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            {/* Form Actions */}
            <div className="flex justify-end gap-3 pt-4 border-t">
              {onCancel && (
                <Button
                  type="button"
                  variant="outline"
                  onClick={onCancel}
                  disabled={isSubmitting}
                >
                  Cancel
                </Button>
              )}
              <Button
                type="submit"
                disabled={isSubmitting || isCheckingUnique}
                className="min-w-[120px]"
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    {editingTag ? "Updating..." : "Creating..."}
                  </>
                ) : (
                  <>
                    <Tag className="mr-2 h-4 w-4" />
                    {editingTag ? "Update Tag" : "Create Tag"}
                  </>
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
} 
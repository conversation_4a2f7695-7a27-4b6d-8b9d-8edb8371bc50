"use client";

import { useState, use<PERSON>emo, useEffect } from "react";
import { EquipmentTag } from "@/types/equipment-tag";
import { EquipmentTagService } from "@/lib/services/equipment/equipment-tag-service";
import { AdminChecklistService } from "@/lib/services/checklist/admin-checklist-service";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { 
  Search, 
  MoreVertical, 
  Edit, 
  Trash2, 
  QrCode, 
  Download,
  Printer,
  Calendar,
  Building,
  MapPin,
  Package,
  Tag as TagIcon,
  ClipboardCheck,
  X,
  Filter,
  FileText,
  User
} from "lucide-react";

type EquipmentTagWithChecklistInfo = EquipmentTag & {
  hasChecklists?: boolean;
  checklistCount?: number;
  lastChecklistDate?: string;
  availableAttempts?: number[];
  latestAttempt?: number;
};

interface EquipmentTagListProps {
  tags: EquipmentTagWithChecklistInfo[];
  onRefresh: () => void;
  onEdit: (tag: EquipmentTagWithChecklistInfo) => void;
  onViewQR: (tag: EquipmentTagWithChecklistInfo) => void;
  onOpenChecklist?: (tag: EquipmentTagWithChecklistInfo) => void;
}

export function EquipmentTagList({ tags, onRefresh, onEdit, onViewQR, onOpenChecklist }: EquipmentTagListProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [clientFilter, setClientFilter] = useState<string>('all');
  const [buildingFilter, setBuildingFilter] = useState<string>('all');
  const [locationFilter, setLocationFilter] = useState<string>('all');
  const [equipmentFilter, setEquipmentFilter] = useState<string>('all');
  const [checklistStatusFilter, setChecklistStatusFilter] = useState<string>('all');
  const [dateFilter, setDateFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('newest');
  
  // Filter options state
  const [availableClients, setAvailableClients] = useState<string[]>([]);
  const [availableBuildings, setAvailableBuildings] = useState<string[]>([]);
  const [availableLocations, setAvailableLocations] = useState<string[]>([]);
  const [availableEquipmentTypes, setAvailableEquipmentTypes] = useState<string[]>([]);
  
  const [deleteDialog, setDeleteDialog] = useState<{
    isOpen: boolean;
    tag: EquipmentTagWithChecklistInfo | null;
  }>({
    isOpen: false,
    tag: null,
  });
  const [isDeleting, setIsDeleting] = useState(false);

  // Load filter options
  const loadFilterOptions = async () => {
    try {
      const [clients, buildings, equipmentTypes] = await Promise.all([
        EquipmentTagService.getUniqueClients(),
        EquipmentTagService.getUniqueBuildings(),
        EquipmentTagService.getUniqueEquipmentNames()
      ]);
      setAvailableClients(clients);
      setAvailableBuildings(buildings);
      setAvailableEquipmentTypes(equipmentTypes);
      
      // Load locations for the selected building or all if no building selected
      const locations = await EquipmentTagService.getUniqueLocations(
        buildingFilter === 'all' ? undefined : buildingFilter
      );
      setAvailableLocations(locations);
    } catch (error) {
      console.error('Failed to load filter options:', error);
    }
  };

  useEffect(() => {
    loadFilterOptions();
  }, [buildingFilter]);

  // Filter and sort tags
  const filteredTags = useMemo(() => {
    let filtered = tags;

    // Search filter
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(tag => 
        tag.tagNumber.toLowerCase().includes(term) ||
        tag.equipmentName.toLowerCase().includes(term) ||
        tag.clientName.toLowerCase().includes(term) ||
        tag.building.toLowerCase().includes(term) ||
        tag.location.toLowerCase().includes(term)
      );
    }

    // Client filter
    if (clientFilter !== 'all') {
      filtered = filtered.filter(tag => tag.clientName === clientFilter);
    }

    // Building filter
    if (buildingFilter !== 'all') {
      filtered = filtered.filter(tag => tag.building === buildingFilter);
    }

    // Location filter
    if (locationFilter !== 'all') {
      filtered = filtered.filter(tag => tag.location === locationFilter);
    }

    // Equipment type filter
    if (equipmentFilter !== 'all') {
      filtered = filtered.filter(tag => tag.equipmentName === equipmentFilter);
    }

    // Checklist status filter
    if (checklistStatusFilter !== 'all') {
      filtered = filtered.filter(tag => {
        switch (checklistStatusFilter) {
          case 'has-checklists':
            return tag.hasChecklists && (tag.checklistCount || 0) > 0;
          case 'no-checklists':
            return !tag.hasChecklists || (tag.checklistCount || 0) === 0;
          case 'multiple-attempts':
            return (tag.availableAttempts || []).length > 1;
          default:
            return true;
        }
      });
    }

    // Date filter
    if (dateFilter !== 'all') {
      const now = new Date();
      filtered = filtered.filter(tag => {
        const tagDate = new Date(tag.dateOfCreation);
        switch (dateFilter) {
          case 'today':
            return tagDate.toDateString() === now.toDateString();
          case 'week':
            const weekAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            return tagDate >= weekAgo;
          case 'month':
            const monthAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            return tagDate >= monthAgo;
          case 'quarter':
            const quarterAgo = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            return tagDate >= quarterAgo;
          default:
            return true;
        }
      });
    }

    // Sort tags
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'newest':
          return new Date(b.dateOfCreation).getTime() - new Date(a.dateOfCreation).getTime();
        case 'oldest':
          return new Date(a.dateOfCreation).getTime() - new Date(b.dateOfCreation).getTime();
        case 'tagNumber':
          return a.tagNumber.localeCompare(b.tagNumber);
        case 'equipment':
          return a.equipmentName.localeCompare(b.equipmentName);
        case 'client':
          return a.clientName.localeCompare(b.clientName);
        case 'location':
          return a.location.localeCompare(b.location);
        default:
          return 0;
      }
    });

    return filtered;
  }, [tags, searchTerm, clientFilter, buildingFilter, locationFilter, equipmentFilter, checklistStatusFilter, dateFilter, sortBy]);

  const handleDelete = (tag: EquipmentTagWithChecklistInfo) => {
    setDeleteDialog({ isOpen: true, tag });
  };

  const confirmDelete = async () => {
    if (!deleteDialog.tag) return;

    setIsDeleting(true);
    try {
      await EquipmentTagService.deleteEquipmentTag(deleteDialog.tag.id);
      onRefresh(); // Refresh the list
    } catch (error) {
      console.error("Error deleting tag:", error);
      
      // Handle referential integrity errors
      if (error instanceof Error && error.message.includes('referenced by')) {
        const shouldForceDelete = confirm(
          `${error.message}\n\n` +
          `Would you like to delete all associated checklists first and then delete the equipment tag?\n\n` +
          `⚠️ WARNING: This action cannot be undone and will permanently delete all checklist data!`
        );
        
        if (shouldForceDelete) {
          try {
            // Delete associated checklists first
            const deleteResults = await EquipmentTagService.deleteAssociatedChecklists(deleteDialog.tag.id);
            
            if (deleteResults.failedCount > 0) {
              alert(
                `Failed to delete ${deleteResults.failedCount} checklist(s). ` +
                `Successfully deleted ${deleteResults.deletedCount} checklist(s). ` +
                `Please try again or contact an administrator.`
              );
              return;
            }
            
            // Now delete the equipment tag
            await EquipmentTagService.deleteEquipmentTag(deleteDialog.tag.id, { force: true });
            alert(`Successfully deleted equipment tag and ${deleteResults.deletedCount} associated checklist(s).`);
            onRefresh();
          } catch (forceDeleteError) {
            console.error("Error in force delete:", forceDeleteError);
            alert("Failed to delete equipment tag and associated checklists. Please try again.");
          }
        }
      } else {
        alert("Failed to delete equipment tag. Please try again.");
      }
    } finally {
      setIsDeleting(false);
      setDeleteDialog({ isOpen: false, tag: null });
    }
  };

  const handleDownload = (tag: EquipmentTagWithChecklistInfo) => {
    try {
      EquipmentTagService.downloadQRCode(tag.qrCodeData, tag.tagNumber);
    } catch (error) {
      console.error("Download failed:", error);
      alert("Failed to download QR code. Please try again.");
    }
  };

  const handlePrint = (tag: EquipmentTagWithChecklistInfo) => {
    try {
      EquipmentTagService.printQRCode(tag.qrCodeData, tag);
    } catch (error) {
      console.error("Print failed:", error);
      alert("Failed to print QR code. Please try again.");
    }
  };

  const clearAllFilters = () => {
    setSearchTerm("");
    setClientFilter('all');
    setBuildingFilter('all');
    setLocationFilter('all');
    setEquipmentFilter('all');
    setChecklistStatusFilter('all');
    setDateFilter('all');
  };

  const hasActiveFilters = searchTerm || clientFilter !== 'all' || buildingFilter !== 'all' || 
                         locationFilter !== 'all' || equipmentFilter !== 'all' || 
                         checklistStatusFilter !== 'all' || dateFilter !== 'all';

  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  if (tags.length === 0) {
    return (
      <Card>
        <CardContent className="flex flex-col items-center justify-center py-12">
          <TagIcon className="h-12 w-12 text-muted-foreground mb-4" />
          <h3 className="text-lg font-medium text-muted-foreground mb-2">No Equipment Tags</h3>
          <p className="text-sm text-muted-foreground text-center">
            Create your first equipment tag to get started with QR code management.
          </p>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <CardTitle className="flex items-center gap-2">
              <TagIcon className="h-5 w-5" />
              Equipment Tags ({filteredTags.length} of {tags.length})
            </CardTitle>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search and Sort Controls */}
          <div className="flex flex-col sm:flex-row gap-2">
            <div className="relative flex-1 max-w-md">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search by tag, equipment, client, building, or location..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-full sm:w-[160px]">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="newest">Newest First</SelectItem>
                <SelectItem value="oldest">Oldest First</SelectItem>
                <SelectItem value="tagNumber">Tag Number</SelectItem>
                <SelectItem value="equipment">Equipment Name</SelectItem>
                <SelectItem value="client">Client Name</SelectItem>
                <SelectItem value="location">Location</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Filter Controls */}
          <div className="flex flex-col gap-4">
            <div className="flex flex-col sm:flex-row gap-2">
              <Select value={clientFilter} onValueChange={setClientFilter}>
                <SelectTrigger className="w-full sm:w-[200px]">
                  <SelectValue placeholder="All Clients" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Clients</SelectItem>
                  {availableClients.map((client) => (
                    <SelectItem key={client} value={client}>
                      {client}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={buildingFilter} onValueChange={(value) => {
                setBuildingFilter(value);
                if (value === 'all') {
                  setLocationFilter('all');
                }
              }}>
                <SelectTrigger className="w-full sm:w-[200px]">
                  <SelectValue placeholder="All Buildings" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Buildings</SelectItem>
                  {availableBuildings.map((building) => (
                    <SelectItem key={building} value={building}>
                      {building}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              <Select value={locationFilter} onValueChange={setLocationFilter}>
                <SelectTrigger className="w-full sm:w-[200px]">
                  <SelectValue placeholder="All Locations" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Locations</SelectItem>
                  {availableLocations.map((location) => (
                    <SelectItem key={location} value={location}>
                      {location}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

                             <Select value={equipmentFilter} onValueChange={setEquipmentFilter}>
                 <SelectTrigger className="w-full sm:w-[200px]">
                   <SelectValue placeholder="All Equipment" />
                 </SelectTrigger>
                 <SelectContent>
                   <SelectItem value="all">All Equipment</SelectItem>
                   {availableEquipmentTypes.map((equipment) => (
                     <SelectItem key={equipment} value={equipment}>
                       {equipment}
                     </SelectItem>
                   ))}
                 </SelectContent>
               </Select>
             </div>

             {/* Second row of filters */}
             <div className="flex flex-col sm:flex-row gap-2">
               <Select value={checklistStatusFilter} onValueChange={setChecklistStatusFilter}>
                 <SelectTrigger className="w-full sm:w-[200px]">
                   <SelectValue placeholder="Checklist Status" />
                 </SelectTrigger>
                 <SelectContent>
                   <SelectItem value="all">All Status</SelectItem>
                   <SelectItem value="has-checklists">Has Checklists</SelectItem>
                   <SelectItem value="no-checklists">No Checklists</SelectItem>
                   <SelectItem value="multiple-attempts">Multiple PPM Attempts</SelectItem>
                 </SelectContent>
               </Select>

               <Select value={dateFilter} onValueChange={setDateFilter}>
                 <SelectTrigger className="w-full sm:w-[160px]">
                   <SelectValue placeholder="All Dates" />
                 </SelectTrigger>
                 <SelectContent>
                   <SelectItem value="all">All Dates</SelectItem>
                   <SelectItem value="today">Today</SelectItem>
                   <SelectItem value="week">Last Week</SelectItem>
                   <SelectItem value="month">Last Month</SelectItem>
                   <SelectItem value="quarter">Last Quarter</SelectItem>
                 </SelectContent>
               </Select>
             </div>
            
            {/* Active filters and clear button */}
            {hasActiveFilters && (
              <div className="flex flex-wrap items-center gap-2 text-sm text-muted-foreground">
                <span>Active filters:</span>
                {searchTerm && (
                  <Badge variant="secondary" className="gap-1">
                    Search: {searchTerm}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 w-4 h-4"
                      onClick={() => setSearchTerm("")}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                {clientFilter !== 'all' && (
                  <Badge variant="secondary" className="gap-1">
                    Client: {clientFilter}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 w-4 h-4"
                      onClick={() => setClientFilter('all')}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                {buildingFilter !== 'all' && (
                  <Badge variant="secondary" className="gap-1">
                    Building: {buildingFilter}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 w-4 h-4"
                      onClick={() => {
                        setBuildingFilter('all');
                        setLocationFilter('all');
                      }}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                                 {locationFilter !== 'all' && (
                   <Badge variant="secondary" className="gap-1">
                     Location: {locationFilter}
                     <Button
                       variant="ghost"
                       size="sm"
                       className="h-auto p-0 w-4 h-4"
                       onClick={() => setLocationFilter('all')}
                     >
                       <X className="h-3 w-3" />
                     </Button>
                   </Badge>
                 )}
                 {equipmentFilter !== 'all' && (
                   <Badge variant="secondary" className="gap-1">
                     Equipment: {equipmentFilter}
                     <Button
                       variant="ghost"
                       size="sm"
                       className="h-auto p-0 w-4 h-4"
                       onClick={() => setEquipmentFilter('all')}
                     >
                       <X className="h-3 w-3" />
                     </Button>
                   </Badge>
                 )}
                 {checklistStatusFilter !== 'all' && (
                   <Badge variant="secondary" className="gap-1">
                     Status: {checklistStatusFilter.replace('-', ' ')}
                     <Button
                       variant="ghost"
                       size="sm"
                       className="h-auto p-0 w-4 h-4"
                       onClick={() => setChecklistStatusFilter('all')}
                     >
                       <X className="h-3 w-3" />
                     </Button>
                   </Badge>
                 )}
                {dateFilter !== 'all' && (
                  <Badge variant="secondary" className="gap-1">
                    Date: {dateFilter}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-auto p-0 w-4 h-4"
                      onClick={() => setDateFilter('all')}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                )}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={clearAllFilters}
                  className="text-xs"
                >
                  Clear all filters
                </Button>
              </div>
            )}
          </div>

          {/* Table */}
          <div className="rounded-md border overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="min-w-[150px]">Tag Number</TableHead>
                  <TableHead className="hidden md:table-cell">Equipment</TableHead>
                  <TableHead className="hidden lg:table-cell">Client</TableHead>
                  <TableHead className="hidden sm:table-cell">Location</TableHead>
                  <TableHead className="hidden xl:table-cell">Created</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTags.map((tag) => (
                  <TableRow key={tag.id}>
                    <TableCell>
                      <div className="flex flex-col">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline" className="font-mono text-xs w-fit">
                            {tag.tagNumber}
                          </Badge>
                          {tag.hasChecklists && (
                            <Badge variant="secondary" className="text-xs">
                              {tag.checklistCount} checklist{(tag.checklistCount || 0) > 1 ? 's' : ''}
                            </Badge>
                          )}
                          {(tag.availableAttempts || []).length > 0 && (
                            <Badge variant="outline" className="text-xs bg-green-50 text-green-700 border-green-200">
                              PPM: {(tag.availableAttempts || []).join(', ')}
                            </Badge>
                          )}
                        </div>
                        {/* Mobile: Show equipment, client, and location info */}
                        <div className="md:hidden mt-2 space-y-1">
                          <div className="flex items-center gap-2">
                            <Package className="h-3 w-3 text-muted-foreground" />
                            <span className="text-xs font-medium">{tag.equipmentName}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Building className="h-3 w-3 text-muted-foreground" />
                            <span className="text-xs text-muted-foreground">{tag.clientName}</span>
                          </div>
                          <div className="sm:hidden flex items-center gap-2">
                            <MapPin className="h-3 w-3 text-muted-foreground" />
                            <span className="text-xs text-muted-foreground">{tag.location}</span>
                          </div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden md:table-cell">
                      <div className="flex items-center gap-2">
                        <Package className="h-4 w-4 text-muted-foreground" />
                        <span className="font-medium">{tag.equipmentName}</span>
                      </div>
                    </TableCell>
                    <TableCell className="hidden lg:table-cell">
                      <div className="flex items-center gap-2">
                        <Building className="h-4 w-4 text-muted-foreground" />
                        <div>
                          <div className="font-medium">{tag.clientName}</div>
                          <div className="text-sm text-muted-foreground">{tag.building}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell className="hidden sm:table-cell">
                      <div className="flex items-center gap-2">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{tag.location}</span>
                      </div>
                    </TableCell>
                    <TableCell className="hidden xl:table-cell">
                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-sm">{formatDate(tag.dateOfCreation)}</span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        {onOpenChecklist && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => onOpenChecklist(tag)}
                            className="h-8 w-8 p-0 text-primary hover:text-primary hover:bg-primary/10"
                            title="Open Checklist (Attempt Selection)"
                          >
                            <ClipboardCheck className="h-4 w-4" />
                          </Button>
                        )}
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                        <DropdownMenuContent align="end" className="w-48">
                          <DropdownMenuItem
                            onClick={() => onViewQR(tag)}
                            className="flex items-center gap-2"
                          >
                            <QrCode className="h-4 w-4" />
                            View QR Code
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDownload(tag)}
                            className="flex items-center gap-2"
                          >
                            <Download className="h-4 w-4" />
                            Download QR
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handlePrint(tag)}
                            className="flex items-center gap-2"
                          >
                            <Printer className="h-4 w-4" />
                            Print QR
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => onEdit(tag)}
                            className="flex items-center gap-2"
                          >
                            <Edit className="h-4 w-4" />
                            Edit Tag
                          </DropdownMenuItem>
                          <DropdownMenuItem
                            onClick={() => handleDelete(tag)}
                            className="flex items-center gap-2 text-red-600 focus:text-red-600"
                          >
                            <Trash2 className="h-4 w-4" />
                            Delete Tag
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredTags.length === 0 && hasActiveFilters && (
            <div className="text-center py-8">
              <Filter className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
              <p className="text-muted-foreground">
                No equipment tags match your current filters
              </p>
              <Button
                variant="outline"
                size="sm"
                onClick={clearAllFilters}
                className="mt-2"
              >
                Clear all filters
              </Button>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Delete Confirmation Dialog */}
      <AlertDialog 
        open={deleteDialog.isOpen} 
        onOpenChange={(open) => !open && setDeleteDialog({ isOpen: false, tag: null })}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete Equipment Tag</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete the equipment tag{" "}
              <strong>{deleteDialog.tag?.tagNumber}</strong>? This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? "Deleting..." : "Delete"}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </>
  );
} 
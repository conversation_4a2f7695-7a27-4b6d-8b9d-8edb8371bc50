"use client";

import React, { useState, useEffect } from 'react';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  List, 
  Search, 
  Trash2,
  AlertCircle,
  CheckCircle,
  Clock,
  Loader2
} from 'lucide-react';
import { ExportQueueItem } from '@/types/export-queue';
import { ExportQueueService } from '@/lib/services/export/export-queue-service';
import { QueueItemCard } from './queue-item-card';
import { useAuth } from '@/components/auth';
import { log } from '@/lib/utils/logger';
import { canManageUsers } from '@/types/user';

interface ExportQueueDialogProps {
  isOpen: boolean;
  onClose: () => void;
}

export function ExportQueueDialog({ isOpen, onClose }: ExportQueueDialogProps) {
  const { user, extendedUser } = useAuth();
  const [queueItems, setQueueItems] = useState<ExportQueueItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<ExportQueueItem[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<'all' | 'pending' | 'processing' | 'completed' | 'failed'>('all');
  const [isLoading, setIsLoading] = useState(true);
  const [stats, setStats] = useState({
    pending: 0,
    processing: 0,
    completed: 0,
    failed: 0,
    total: 0
  });

  // Subscribe to queue updates
  useEffect(() => {
    if (!user?.uid || !isOpen) return;

    setIsLoading(true);
    
    // Check if user is admin and can view all exports
    const isAdmin = extendedUser && canManageUsers(extendedUser.role);
    
    const unsubscribe = isAdmin 
      ? ExportQueueService.subscribeToAllQueue((items) => {
          setQueueItems(items);
          setIsLoading(false);
          
          // Update stats
          const newStats = {
            pending: items.filter(item => item.status === 'pending').length,
            processing: items.filter(item => item.status === 'processing').length,
            completed: items.filter(item => item.status === 'completed').length,
            failed: items.filter(item => item.status === 'failed').length,
            total: items.length
          };
          setStats(newStats);
        })
      : ExportQueueService.subscribeToUserQueue(user.uid, (items) => {
          setQueueItems(items);
          setIsLoading(false);
          
          // Update stats
          const newStats = {
            pending: items.filter(item => item.status === 'pending').length,
            processing: items.filter(item => item.status === 'processing').length,
            completed: items.filter(item => item.status === 'completed').length,
            failed: items.filter(item => item.status === 'failed').length,
            total: items.length
          };
          setStats(newStats);
        });

    return unsubscribe;
  }, [user?.uid, extendedUser?.role, isOpen]);

  // Filter items based on search and status
  useEffect(() => {
    let filtered = queueItems;

    // Filter by status
    if (statusFilter !== 'all') {
      filtered = filtered.filter(item => item.status === statusFilter);
    }

    // Filter by search term
    if (searchTerm) {
      const term = searchTerm.toLowerCase();
      filtered = filtered.filter(item => 
        item.type.toLowerCase().includes(term) ||
        item.metadata.clientName?.toLowerCase().includes(term) ||
        item.metadata.requestedBy.toLowerCase().includes(term) ||
        item.result?.fileName?.toLowerCase().includes(term)
      );
    }

    setFilteredItems(filtered);
  }, [queueItems, searchTerm, statusFilter]);

  const handleCancel = async (queueId: string) => {
    if (!user?.uid) return;

    try {
      await ExportQueueService.cancelQueueItem(queueId, user.uid);
      log.info('Export cancelled', 'EXPORT_QUEUE', { queueId });
    } catch (error) {
      console.error('Failed to cancel export:', error);
      alert('Failed to cancel export. Please try again.');
    }
  };

  const handleDownload = (downloadUrl: string, fileName: string) => {
    try {
      // Create a temporary link and trigger download
      const link = document.createElement('a');
      link.href = downloadUrl;
      link.download = fileName;
      link.target = '_blank';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      log.info('Export downloaded', 'EXPORT_QUEUE', { fileName });
    } catch (error) {
      console.error('Failed to download export:', error);
      alert('Failed to download export. Please try again.');
    }
  };

  const handleCleanup = async () => {
    if (!user?.uid) return;

    try {
      const cleaned = await ExportQueueService.cleanupOldQueueItems(user.uid, 7);
      if (cleaned > 0) {
        alert(`Cleaned up ${cleaned} old export records.`);
      } else {
        alert('No old exports to clean up.');
      }
    } catch (error) {
      console.error('Failed to cleanup exports:', error);
      alert('Failed to cleanup old exports.');
    }
  };

  const handleDelete = async (queueId: string) => {
    if (!user?.uid) return;

    try {
      await ExportQueueService.deleteQueueItem(queueId, user.uid);
      log.info('Export deleted', 'EXPORT_QUEUE', { queueId });
    } catch (error) {
      console.error('Failed to delete export:', error);
      alert('Failed to delete export. Please try again.');
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'pending': return <Clock className="h-3 w-3" />;
      case 'processing': return <Loader2 className="h-3 w-3 animate-spin" />;
      case 'completed': return <CheckCircle className="h-3 w-3" />;
      case 'failed': return <AlertCircle className="h-3 w-3" />;
      default: return null;
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-5xl sm:max-w-[95vw] max-h-[85vh] overflow-hidden flex flex-col bg-gray-950 border-gray-800">
        <DialogHeader className="pb-3">
          <DialogTitle className="flex flex-col sm:flex-row sm:items-center gap-2 text-gray-100">
            <div className="flex items-center gap-2">
              <List className="h-4 w-4" />
              <span>Export Queue</span>
            </div>
            <div className="flex items-center gap-2">
              {extendedUser && canManageUsers(extendedUser.role) && (
                <Badge variant="outline" className="border-blue-600 text-blue-300 text-xs">
                  All Users
                </Badge>
              )}
              {stats.total > 0 && (
                <Badge variant="outline" className="border-gray-600 text-gray-300 text-xs">
                  {stats.total}
                </Badge>
              )}
            </div>
          </DialogTitle>
          <DialogDescription className="text-gray-400 text-sm">
            Track export requests and download files. Available for 7 days.
            {extendedUser && canManageUsers(extendedUser.role) && (
              <span className="text-blue-400 block sm:inline"> • Viewing all user exports</span>
            )}
          </DialogDescription>
        </DialogHeader>

        {/* Compact Stats and Controls */}
        <div className="flex flex-col gap-3 pb-3 border-b border-gray-800">
          {/* Compact Stats */}
          <div className="flex gap-2 flex-wrap">
            <Badge 
              variant="outline" 
              className={`cursor-pointer transition-colors text-xs px-2 py-1 border-gray-600 ${statusFilter === 'all' ? 'bg-gray-800 text-gray-100' : 'text-gray-300 hover:bg-gray-900'}`}
              onClick={() => setStatusFilter('all')}
            >
              All ({stats.total})
            </Badge>
            {stats.pending > 0 && (
              <Badge 
                variant="outline" 
                className={`cursor-pointer transition-colors text-xs px-2 py-1 border-yellow-600 text-yellow-300 hover:bg-yellow-900/20 ${statusFilter === 'pending' ? 'ring-1 ring-yellow-600' : ''}`}
                onClick={() => setStatusFilter('pending')}
              >
                {getStatusIcon('pending')}
                <span className="ml-1">Pending ({stats.pending})</span>
              </Badge>
            )}
            {stats.processing > 0 && (
              <Badge 
                variant="outline" 
                className={`cursor-pointer transition-colors text-xs px-2 py-1 border-blue-600 text-blue-300 hover:bg-blue-900/20 ${statusFilter === 'processing' ? 'ring-1 ring-blue-600' : ''}`}
                onClick={() => setStatusFilter('processing')}
              >
                {getStatusIcon('processing')}
                <span className="ml-1">Processing ({stats.processing})</span>
              </Badge>
            )}
            {stats.completed > 0 && (
              <Badge 
                variant="outline" 
                className={`cursor-pointer transition-colors text-xs px-2 py-1 border-green-600 text-green-300 hover:bg-green-900/20 ${statusFilter === 'completed' ? 'ring-1 ring-green-600' : ''}`}
                onClick={() => setStatusFilter('completed')}
              >
                {getStatusIcon('completed')}
                <span className="ml-1">Ready ({stats.completed})</span>
              </Badge>
            )}
            {stats.failed > 0 && (
              <Badge 
                variant="outline" 
                className={`cursor-pointer transition-colors text-xs px-2 py-1 border-red-600 text-red-300 hover:bg-red-900/20 ${statusFilter === 'failed' ? 'ring-1 ring-red-600' : ''}`}
                onClick={() => setStatusFilter('failed')}
              >
                {getStatusIcon('failed')}
                <span className="ml-1">Failed ({stats.failed})</span>
              </Badge>
            )}
          </div>

          {/* Compact Search and Actions */}
          <div className="flex flex-col sm:flex-row gap-2">
            <div className="relative flex-1">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-3 w-3 text-gray-400" />
              <Input
                placeholder="Search exports..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-7 h-8 text-sm bg-gray-900 border-gray-700 text-gray-100 placeholder:text-gray-400"
              />
            </div>
            <Button
              variant="outline"
              size="sm"
              onClick={handleCleanup}
              className="h-8 px-3 text-xs border-gray-600 text-gray-300 hover:bg-gray-800 w-full sm:w-auto"
            >
              <Trash2 className="h-3 w-3 mr-1" />
              <span className="sm:hidden">Cleanup Old Exports</span>
              <span className="hidden sm:inline">Cleanup</span>
            </Button>
          </div>
        </div>

        {/* Compact Queue Items */}
        <div className="flex-1 overflow-y-auto">
          {isLoading ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-5 w-5 animate-spin mr-2 text-gray-400" />
              <span className="text-gray-300 text-sm">Loading...</span>
            </div>
          ) : filteredItems.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-8 text-center">
              <List className="h-10 w-10 text-gray-600 mb-3" />
              <h3 className="text-base font-medium mb-2 text-gray-200">
                {queueItems.length === 0 ? 'No exports yet' : 'No matching exports'}
              </h3>
              <p className="text-gray-400 max-w-md text-sm">
                {queueItems.length === 0 
                  ? 'Export requests will appear here. Select checklists and click "Queue Export".'
                  : 'Try adjusting your search terms or filters.'
                }
              </p>
            </div>
          ) : (
            <div className="space-y-2 p-1">
              {filteredItems.map((item) => (
                <QueueItemCard
                  key={item.id}
                  item={item}
                  onCancel={handleCancel}
                  onDownload={handleDownload}
                  onDelete={handleDelete}
                />
              ))}
            </div>
          )}
        </div>

        {/* Compact Footer */}
        <div className="flex justify-between items-center pt-3 border-t border-gray-800">
          <div className="text-xs text-gray-400">
            {filteredItems.length > 0 && (
              <span>Showing {filteredItems.length} of {queueItems.length}</span>
            )}
          </div>
          <Button onClick={onClose} size="sm" className="h-8 px-4 text-sm bg-gray-800 hover:bg-gray-700 text-gray-100">
            Close
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
} 
"use client";

import React from 'react';
import { Progress } from '@/components/ui/progress';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Loader2, 
  CheckCircle, 
  AlertCircle, 
  Clock,
  X,
  ExternalLink,
  Download
} from 'lucide-react';
import { ExportQueueItem } from '@/types/export-queue';
import { formatDistanceToNow, format } from 'date-fns';

interface ExportProgressToastProps {
  item: ExportQueueItem;
  onCancel?: () => void;
  onViewQueue?: () => void;
  onDownload?: () => void;
  onDismiss?: () => void;
  compact?: boolean;
}

export function ExportProgressToast({ 
  item, 
  onCancel, 
  onViewQueue, 
  onDownload, 
  onDismiss,
  compact = false 
}: ExportProgressToastProps) {
  const getStatusIcon = () => {
    switch (item.status) {
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'processing':
        return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
      case 'completed':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'failed':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = () => {
    switch (item.status) {
      case 'pending': return 'border-yellow-500/20 bg-yellow-500/5';
      case 'processing': return 'border-blue-500/20 bg-blue-500/5';
      case 'completed': return 'border-green-500/20 bg-green-500/5';
      case 'failed': return 'border-red-500/20 bg-red-500/5';
      default: return 'border-gray-500/20 bg-gray-500/5';
    }
  };

  const getProgressMessage = () => {
    if (!item.progress) return item.status;
    
    switch (item.progress.stage) {
      case 'queued':
        return 'Waiting in queue...';
      case 'generating':
        return `Processing ${item.progress.currentIndex} of ${item.progress.totalCount}`;
      case 'merging':
        return 'Combining documents...';
      case 'uploading':
        return 'Uploading to storage...';
      case 'complete':
        return 'Processing complete!';
      default:
        return `${item.progress.percentage}% complete`;
    }
  };

  const getTimeInfo = () => {
    const now = new Date();
    
    if (item.status === 'completed' && item.completedAt) {
      const completedTime = item.completedAt.toDate();
      const startTime = item.startedAt?.toDate() || item.createdAt.toDate();
      const duration = Math.round((completedTime.getTime() - startTime.getTime()) / 1000);
      
      return {
        label: 'Completed',
        value: `${duration}s ago`,
        detail: format(completedTime, 'HH:mm:ss')
      };
    }
    
    if (item.status === 'processing' && item.startedAt) {
      const startTime = item.startedAt.toDate();
      const elapsed = Math.round((now.getTime() - startTime.getTime()) / 1000);
      
      // Estimate remaining time based on progress
      let remaining = '';
      if (item.progress?.percentage && item.progress.percentage > 5) {
        const totalEstimated = (elapsed / item.progress.percentage) * 100;
        const remainingSeconds = Math.round(totalEstimated - elapsed);
        remaining = remainingSeconds > 0 ? ` (~${remainingSeconds}s remaining)` : '';
      }
      
      return {
        label: 'Processing',
        value: `${elapsed}s${remaining}`,
        detail: format(startTime, 'HH:mm:ss')
      };
    }
    
    if (item.status === 'pending') {
      const queuedTime = item.createdAt.toDate();
      return {
        label: 'Queued',
        value: formatDistanceToNow(queuedTime, { addSuffix: true }),
        detail: format(queuedTime, 'HH:mm:ss')
      };
    }
    
    return null;
  };

  const timeInfo = getTimeInfo();

  if (compact) {
    return (
      <div className={`flex items-center gap-3 p-3 rounded-lg border ${getStatusColor()}`}>
        {getStatusIcon()}
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2">
            <span className="text-sm font-medium truncate">
              {item.type.toUpperCase()} Export
            </span>
            <Badge variant="outline" className="text-xs">
              {item.checklistCount}
            </Badge>
          </div>
          <p className="text-xs text-muted-foreground truncate">
            {getProgressMessage()}
          </p>
        </div>
        
        {item.status === 'processing' && item.progress && (
          <div className="flex items-center gap-2">
            <Progress value={item.progress.percentage} className="w-16 h-2" />
            <span className="text-xs font-mono text-muted-foreground min-w-[3rem]">
              {item.progress.percentage}%
            </span>
          </div>
        )}
        
        <div className="flex gap-1">
          {item.status === 'completed' && onDownload && (
            <Button size="sm" variant="outline" onClick={onDownload} className="h-7 px-2">
              <Download className="h-3 w-3" />
            </Button>
          )}
          {onViewQueue && (
            <Button size="sm" variant="ghost" onClick={onViewQueue} className="h-7 px-2">
              <ExternalLink className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className={`p-4 rounded-lg border ${getStatusColor()}`}>
      {/* Header */}
      <div className="flex items-start justify-between mb-3">
        <div className="flex items-center gap-2">
          {getStatusIcon()}
          <div>
            <h4 className="text-sm font-semibold">
              {item.type.toUpperCase()} Export
            </h4>
            <p className="text-xs text-muted-foreground">
              {item.checklistCount} checklists • {item.metadata.clientName}
            </p>
          </div>
        </div>
        
        <div className="flex items-center gap-1">
          <Badge variant="outline" className="text-xs">
            {item.status}
          </Badge>
          {onDismiss && (
            <Button size="sm" variant="ghost" onClick={onDismiss} className="h-6 w-6 p-0">
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>
      </div>

      {/* Progress */}
      {item.status === 'processing' && item.progress && (
        <div className="mb-3">
          <div className="flex items-center justify-between mb-1">
            <span className="text-xs text-muted-foreground">
              {getProgressMessage()}
            </span>
            <span className="text-xs font-mono">
              {item.progress.percentage}%
            </span>
          </div>
          <Progress value={item.progress.percentage} className="h-2" />
          
          {item.progress.currentChecklist && (
            <p className="text-xs text-muted-foreground mt-1 truncate">
              Current: {item.progress.currentChecklist}
            </p>
          )}
        </div>
      )}

      {/* Status Message */}
      <div className="mb-3">
        <p className="text-sm">
          {getProgressMessage()}
        </p>
        
        {timeInfo && (
          <div className="flex items-center gap-4 mt-1">
            <span className="text-xs text-muted-foreground">
              {timeInfo.label}: {timeInfo.value}
            </span>
            <span className="text-xs text-muted-foreground">
              {timeInfo.detail}
            </span>
          </div>
        )}
      </div>

      {/* Error Message */}
      {item.status === 'failed' && item.error && (
        <div className="mb-3 p-2 bg-red-500/10 border border-red-500/20 rounded text-xs">
          <p className="text-red-600 dark:text-red-400">{item.error}</p>
        </div>
      )}

      {/* Success Info */}
      {item.status === 'completed' && item.result && (
        <div className="mb-3 p-2 bg-green-500/10 border border-green-500/20 rounded text-xs">
          <div className="flex items-center justify-between">
            <span>File ready for download</span>
            <span className="text-muted-foreground">
              {(item.result.fileSize / 1024 / 1024).toFixed(1)} MB
            </span>
          </div>
          {item.result.processedCount !== item.checklistCount && (
            <p className="text-amber-600 dark:text-amber-400 mt-1">
              {item.result.processedCount} of {item.checklistCount} processed successfully
            </p>
          )}
        </div>
      )}

      {/* Actions */}
      <div className="flex items-center justify-between">
        <div className="flex gap-2">
          {item.status === 'completed' && onDownload && (
            <Button size="sm" onClick={onDownload} className="h-7">
              <Download className="h-3 w-3 mr-1" />
              Download
            </Button>
          )}
          
          {item.status === 'pending' && onCancel && (
            <Button size="sm" variant="outline" onClick={onCancel} className="h-7">
              <X className="h-3 w-3 mr-1" />
              Cancel
            </Button>
          )}
        </div>
        
        {onViewQueue && (
          <Button size="sm" variant="ghost" onClick={onViewQueue} className="h-7">
            View Queue
            <ExternalLink className="h-3 w-3 ml-1" />
          </Button>
        )}
      </div>
    </div>
  );
} 
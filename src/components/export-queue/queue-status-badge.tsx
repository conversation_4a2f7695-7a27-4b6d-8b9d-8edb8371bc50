"use client";

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { List, Loader2, CheckCircle, Clock } from 'lucide-react';
import { ExportQueueService } from '@/lib/services/export/export-queue-service';
import { useAuth } from '@/components/auth';

interface QueueStatusBadgeProps {
  onClick: () => void;
  className?: string;
}

export function QueueStatusBadge({ onClick, className = '' }: QueueStatusBadgeProps) {
  const { user } = useAuth();
  const [stats, setStats] = useState({
    pending: 0,
    processing: 0,
    completed: 0,
    failed: 0,
    total: 0
  });
  const [isLoading, setIsLoading] = useState(false);

  // Subscribe to queue updates for stats
  useEffect(() => {
    if (!user?.uid) return;

    setIsLoading(true);
    const unsubscribe = ExportQueueService.subscribeToUserQueue(
      user.uid,
      (items) => {
        const newStats = {
          pending: items.filter(item => item.status === 'pending').length,
          processing: items.filter(item => item.status === 'processing').length,
          completed: items.filter(item => item.status === 'completed').length,
          failed: items.filter(item => item.status === 'failed').length,
          total: items.length
        };
        setStats(newStats);
        setIsLoading(false);
      }
    );

    return unsubscribe;
  }, [user?.uid]);

  const getActiveCount = () => {
    return stats.pending + stats.processing;
  };

  const getCompletedCount = () => {
    return stats.completed;
  };

  const getStatusIcon = () => {
    if (isLoading) return <Loader2 className="h-4 w-4 animate-spin" />;
    if (stats.processing > 0) return <Loader2 className="h-4 w-4 animate-spin text-blue-500" />;
    if (stats.pending > 0) return <Clock className="h-4 w-4 text-yellow-500" />;
    if (stats.completed > 0) return <CheckCircle className="h-4 w-4 text-green-500" />;
    return <List className="h-4 w-4" />;
  };

  const getButtonVariant = () => {
    if (stats.processing > 0) return 'default';
    if (stats.pending > 0 || stats.completed > 0) return 'outline';
    return 'ghost';
  };

  const getButtonText = () => {
    const activeCount = getActiveCount();
    const completedCount = getCompletedCount();
    
    if (activeCount > 0 && completedCount > 0) {
      return `Export Queue (${activeCount} active, ${completedCount} ready)`;
    } else if (activeCount > 0) {
      return `Export Queue (${activeCount} processing)`;
    } else if (completedCount > 0) {
      return `Export Queue (${completedCount} ready)`;
    } else if (stats.total > 0) {
      return 'Export Queue (history)';
    } else {
      return 'Export Queue';
    }
  };

  // Don't show if user is not authenticated
  if (!user?.uid) return null;

  return (
    <Button
      variant={getButtonVariant()}
      size="sm"
      onClick={onClick}
      className={`relative transition-all duration-200 ${className}`}
    >
      {getStatusIcon()}
      <span className="ml-2 hidden sm:inline">{getButtonText()}</span>
      <span className="ml-2 sm:hidden">Queue</span>
      
      {/* Active badge */}
      {getActiveCount() > 0 && (
        <Badge 
          className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs bg-blue-500 text-white border-white"
        >
          {getActiveCount()}
        </Badge>
      )}
      
      {/* Completed badge */}
      {getActiveCount() === 0 && getCompletedCount() > 0 && (
        <Badge 
          className="absolute -top-2 -right-2 h-5 w-5 p-0 text-xs bg-green-500 text-white border-white"
        >
          {getCompletedCount()}
        </Badge>
      )}
    </Button>
  );
} 
'use client'

import { motion } from 'framer-motion'
import { cn } from '@/lib/utils/utils'

interface GradientTextProps {
  children: React.ReactNode
  className?: string
  gradient?: string
  animate?: boolean
}

export function GradientText({ 
  children, 
  className, 
  gradient = "from-blue-600 via-blue-500 to-indigo-600",
  animate = true 
}: GradientTextProps) {
  return (
    <motion.span
      className={cn(
        `bg-gradient-to-r ${gradient} bg-clip-text text-transparent font-bold`,
        animate && "animate-gradient-x bg-[length:200%_200%]",
        className
      )}
      initial={animate ? { backgroundPosition: "0% 50%" } : undefined}
      animate={animate ? { 
        backgroundPosition: ["0% 50%", "100% 50%", "0% 50%"] 
      } : undefined}
      transition={animate ? {
        duration: 5,
        ease: "linear",
        repeat: Infinity,
      } : undefined}
    >
      {children}
    </motion.span>
  )
}

// Predefined gradient options for easy use
export const GRADIENT_PRESETS = {
  primary: "from-red-600 via-red-500 to-red-400", // Red for primary brand
  secondary: "from-blue-600 via-blue-500 to-indigo-600", // Blue for secondary
  success: "from-green-600 via-green-500 to-emerald-600", // Green for success
  warning: "from-amber-600 via-amber-500 to-orange-600", // Amber for warnings
  purple: "from-purple-600 via-purple-500 to-indigo-600", // Purple for variety
  neutral: "from-slate-600 via-slate-500 to-slate-400", // Neutral gray
};

// Keep default export for backward compatibility
export default GradientText; 
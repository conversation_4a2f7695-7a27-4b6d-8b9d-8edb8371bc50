'use client'

import { useState, useEffect, useRef } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '@/components/ui/button';
import { Menu, X, Phone, Mail, MapPin, User, LogOut, Settings, BarChart3, LogIn, ChevronDown, Shield, Database, CheckSquare } from 'lucide-react';
import GradientText from '@/components/gradient-text';
import { useOptionalAuth } from '@/components/auth/safe-auth-hook';
import { AuthModal } from '@/components/auth';
import { canManageUsers } from '@/types/user';
import { AuburnIcon } from '@/components/auburn-icon';

const navigationItems = [
  { name: 'Home', href: '/' },
  { name: 'About', href: '/about' },
  { name: 'Services', href: '/services' },
  { name: 'Projects', href: '/projects' },
  { name: 'Suite', href: '/tools' },
  { name: 'Contact', href: '/contact' },
];

export default function Navigation() {
  const [isOpen, setIsOpen] = useState(false);
  const [showUserMenu, setShowUserMenu] = useState(false);
  const [showAuthModal, setShowAuthModal] = useState(false);
  const pathname = usePathname();
  const { user, extendedUser, signOut } = useOptionalAuth();
  const userMenuRef = useRef<HTMLDivElement>(null);

  // Close user menu when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (userMenuRef.current && !userMenuRef.current.contains(event.target as Node)) {
        setShowUserMenu(false);
      }
    }

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <nav className="fixed top-0 left-0 right-0 z-50 glass border-b border-red-500/20 shadow-2xl">
      <div className="container mx-auto header-spacing">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link href="/" className="flex-shrink-0">
            <motion.div
              className="flex items-center space-x-3"
              whileHover={{ scale: 1.02 }}
              transition={{ duration: 0.2 }}
            >
              <motion.div
                whileHover={{ rotate: 5 }}
                transition={{ duration: 0.3 }}
              >
                <AuburnIcon size={40} className="drop-shadow-xl" />
              </motion.div>
              <div className="hidden sm:block">
                <div className="flex flex-col">
                  <span className="text-xl font-bold leading-tight text-red-gradient">
                    Auburn Engineering
                  </span>
                  <span className="text-xs text-slate-400 font-medium">
                    Leading ACMV Solutions
                  </span>
                </div>
              </div>
            </motion.div>
          </Link>

          {/* Desktop Navigation */}
          <div className="hidden md:block">
            <div className="flex items-center space-x-1">
              {navigationItems.map((item, index) => (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, y: -20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ scale: 1.05 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <Link
                    href={item.href}
                    className={`px-3 py-2 rounded-lg text-sm font-medium transition-all duration-300 ${
                      pathname === item.href
                        ? 'text-white bg-gradient-to-r from-red-700 to-red-600 shadow-lg shadow-red-500/25'
                        : 'text-slate-300 hover:text-white hover:bg-red-900/20'
                    }`}
                  >
                    {item.name}
                  </Link>
                </motion.div>
              ))}
            </div>
          </div>

          {/* Contact Info & Auth */}
          <div className="hidden lg:flex items-center space-x-4">
            <motion.div 
              className="flex items-center space-x-2 text-slate-300 text-sm glass-light px-3 py-2 rounded-lg border border-red-500/20"
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.2 }}
            >
              <Phone className="h-4 w-4 text-red-400" />
              <span className="font-medium">70300401</span>
            </motion.div>
            
            {user ? (
              <div className="relative" ref={userMenuRef}>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowUserMenu(!showUserMenu)}
                  className="flex items-center space-x-3 text-slate-200 hover:bg-red-600/20 px-3 py-2 h-auto rounded-lg border border-red-500/20"
                >
                  <div className="w-8 h-8 bg-gradient-to-r from-red-700 to-red-600 rounded-full flex items-center justify-center shadow-lg">
                    <User className="h-4 w-4 text-white" />
                  </div>
                  <div className="hidden xl:block text-left">
                    <div className="text-sm font-medium">{user.email?.split('@')[0] || 'User'}</div>
                    <div className="text-xs text-slate-400">{user.email}</div>
                  </div>
                  <ChevronDown className="h-4 w-4" />
                </Button>

                <AnimatePresence>
                  {showUserMenu && (
                    <motion.div
                      initial={{ opacity: 0, y: -10, scale: 0.95 }}
                      animate={{ opacity: 1, y: 0, scale: 1 }}
                      exit={{ opacity: 0, y: -10, scale: 0.95 }}
                      transition={{ duration: 0.2 }}
                      className="absolute right-0 mt-2 w-64 glass rounded-xl border border-red-500/20 shadow-2xl z-50"
                    >
                      <div className="p-4 border-b border-red-500/20">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="text-white font-medium">{user.email?.split('@')[0] || 'User'}</p>
                            <p className="text-slate-400 text-sm">{user.email}</p>
                          </div>
                          {extendedUser && (
                            <div className={`px-2 py-1 rounded-full text-xs font-medium ${
                              extendedUser.role === 'admin' 
                                ? 'bg-amber-600/20 text-amber-200 border border-amber-500/30' 
                                : extendedUser.role === 'technician'
                                ? 'bg-green-600/20 text-green-200 border border-green-500/30'
                                : 'bg-red-600/20 text-red-200 border border-red-500/30'
                            }`}>
                              {extendedUser.role === 'admin' ? 'Admin' : extendedUser.role === 'technician' ? 'Tech' : 'User'}
                            </div>
                          )}
                        </div>
                      </div>
                      
                      <div className="p-2">
                        <Link
                          href="/dashboard"
                          className="flex items-center space-x-3 px-3 py-2 text-slate-300 hover:text-white hover:bg-red-600/20 rounded-lg transition-all duration-200"
                          onClick={() => setShowUserMenu(false)}
                        >
                          <BarChart3 className="h-4 w-4" />
                          <span className="font-medium">Dashboard</span>
                        </Link>
                        
                        <Link
                          href="/saved"
                          className="flex items-center space-x-3 px-3 py-2 text-slate-300 hover:text-white hover:bg-red-600/20 rounded-lg transition-all duration-200"
                          onClick={() => setShowUserMenu(false)}
                        >
                          <Database className="h-4 w-4" />
                          <span className="font-medium">Saved Inspections</span>
                        </Link>
                        
                        <Link
                          href="/checklist"
                          className="flex items-center space-x-3 px-3 py-2 text-slate-300 hover:text-white hover:bg-red-600/20 rounded-lg transition-all duration-200"
                          onClick={() => setShowUserMenu(false)}
                        >
                          <CheckSquare className="h-4 w-4" />
                          <span className="font-medium">New Checklist</span>
                        </Link>
                        
                        {extendedUser && canManageUsers(extendedUser.role) && (
                          <Link
                            href="/admin"
                            className="flex items-center space-x-3 px-3 py-2 text-slate-300 hover:text-white hover:bg-amber-600/20 rounded-lg transition-all duration-200"
                            onClick={() => setShowUserMenu(false)}
                          >
                            <Shield className="h-4 w-4" />
                            <span className="font-medium">Admin Panel</span>
                          </Link>
                        )}
                        
                        <div className="border-t border-red-500/20 mt-2 pt-2">
                          <button
                            onClick={() => {
                              signOut();
                              setShowUserMenu(false);
                            }}
                            className="flex items-center space-x-3 px-3 py-2 text-red-400 hover:text-red-300 hover:bg-red-600/20 rounded-lg transition-all duration-200 w-full text-left"
                          >
                            <LogOut className="h-4 w-4" />
                            <span className="font-medium">Sign Out</span>
                          </button>
                        </div>
                      </div>
                    </motion.div>
                  )}
                </AnimatePresence>
              </div>
            ) : (
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <Button
                  onClick={() => setShowAuthModal(true)}
                  className="btn-primary px-4 py-2 rounded-lg font-medium"
                >
                  <LogIn className="h-4 w-4 mr-2" />
                  <span>Sign In</span>
                </Button>
              </motion.div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="flex md:hidden items-center space-x-3">
            {user ? (
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                <div className="w-8 h-8 bg-gradient-to-r from-red-700 to-red-600 rounded-full flex items-center justify-center shadow-lg">
                  <User className="h-4 w-4 text-white" />
                </div>
              </motion.div>
            ) : (
              <Button
                onClick={() => setShowAuthModal(true)}
                size="sm"
                className="btn-primary rounded-lg"
              >
                <LogIn className="h-4 w-4" />
              </Button>
            )}
            
            <motion.button
              onClick={() => setIsOpen(!isOpen)}
              className="p-2 rounded-lg glass-light border border-red-500/20 text-slate-300 hover:text-white hover:bg-red-900/20 transition-all duration-300"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              {isOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
            </motion.button>
          </div>
        </div>
      </div>

      {/* Mobile Menu */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="md:hidden glass border-t border-red-500/20"
          >
            <div className="container mx-auto px-4 pt-4 pb-6 space-y-2">
              {/* Main Navigation Items */}
              {navigationItems.map((item, index) => (
                <motion.div
                  key={item.name}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Link
                    href={item.href}
                    className={`block px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ${
                      pathname === item.href
                        ? 'text-white bg-gradient-to-r from-red-700 to-red-600 shadow-lg'
                        : 'text-slate-300 hover:text-white hover:bg-red-900/20'
                    }`}
                    onClick={() => setIsOpen(false)}
                  >
                    {item.name}
                  </Link>
                </motion.div>
              ))}

              {/* PPM Suite Navigation Items for Authenticated Users */}
              {user && (
                <>
                  <div className="border-t border-red-500/20 my-4"></div>
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.1 }}
                  >
                    <Link
                      href="/dashboard"
                      className={`block px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ${
                        pathname === '/dashboard'
                          ? 'text-white bg-gradient-to-r from-red-700 to-red-600 shadow-lg'
                          : 'text-slate-300 hover:text-white hover:bg-red-900/20'
                      }`}
                      onClick={() => setIsOpen(false)}
                    >
                      Dashboard
                    </Link>
                  </motion.div>
                  
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.2 }}
                  >
                    <Link
                      href="/saved"
                      className={`block px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ${
                        pathname === '/saved'
                          ? 'text-white bg-gradient-to-r from-red-700 to-red-600 shadow-lg'
                          : 'text-slate-300 hover:text-white hover:bg-red-900/20'
                      }`}
                      onClick={() => setIsOpen(false)}
                    >
                      Saved Inspections
                    </Link>
                  </motion.div>
                  
                  <motion.div
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.3, delay: 0.3 }}
                  >
                    <Link
                      href="/checklist"
                      className={`block px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ${
                        pathname === '/checklist'
                          ? 'text-white bg-gradient-to-r from-red-700 to-red-600 shadow-lg'
                          : 'text-slate-300 hover:text-white hover:bg-red-900/20'
                      }`}
                      onClick={() => setIsOpen(false)}
                    >
                      New Checklist
                    </Link>
                  </motion.div>

                  {extendedUser && canManageUsers(extendedUser.role) && (
                    <motion.div
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: 0.4 }}
                    >
                      <Link
                        href="/admin"
                        className={`block px-4 py-3 rounded-lg text-base font-medium transition-all duration-200 ${
                          pathname === '/admin'
                            ? 'text-white bg-gradient-to-r from-amber-700 to-amber-600 shadow-lg'
                            : 'text-slate-300 hover:text-white hover:bg-amber-900/20'
                        }`}
                        onClick={() => setIsOpen(false)}
                      >
                        Admin Panel
                      </Link>
                    </motion.div>
                  )}
                </>
              )}
              
              {/* Mobile Contact Info */}
              <div className="px-4 py-4 border-t border-red-500/20 mt-4">
                <div className="space-y-3">
                  <div className="flex items-center space-x-3 text-slate-300 text-sm">
                    <Phone className="h-4 w-4 text-red-400" />
                    <span>70300401</span>
                  </div>
                  <div className="flex items-center space-x-3 text-slate-300 text-sm">
                    <MapPin className="h-4 w-4 text-red-400" />
                    <span>Doha, Qatar</span>
                  </div>
                </div>
              </div>
              
              {/* User Actions Section */}
              {user && (
                <div className="px-4 py-4 border-t border-red-500/20 mt-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-gradient-to-r from-red-700 to-red-600 rounded-full flex items-center justify-center shadow-lg">
                        <User className="h-4 w-4 text-white" />
                      </div>
                      <div>
                        <p className="text-white text-sm font-medium">{user.email?.split('@')[0] || 'User'}</p>
                        {extendedUser && (
                          <div className={`inline-block px-2 py-1 rounded-full text-xs font-medium mt-1 ${
                            extendedUser.role === 'admin' 
                              ? 'bg-amber-600/20 text-amber-200 border border-amber-500/30' 
                              : extendedUser.role === 'technician'
                              ? 'bg-green-600/20 text-green-200 border border-green-500/30'
                              : 'bg-red-600/20 text-red-200 border border-red-500/30'
                          }`}>
                            {extendedUser.role === 'admin' ? 'Admin' : extendedUser.role === 'technician' ? 'Tech' : 'User'}
                          </div>
                        )}
                      </div>
                    </div>
                    
                    <Button
                      onClick={() => {
                        signOut();
                        setIsOpen(false);
                      }}
                      size="sm"
                      variant="ghost"
                      className="text-red-400 hover:text-red-300 hover:bg-red-600/20 p-2"
                    >
                      <LogOut className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>

      <AuthModal
        isOpen={showAuthModal}
        onClose={() => setShowAuthModal(false)}
      />
    </nav>
  );
} 
'use client';

import React, { useState, useEffect } from 'react';
import { getFunctions, httpsCallable } from 'firebase/functions';
import { getAppCheckToken, isAppCheckInitialized, validateAppCheckConfig } from '@/config/app-check';
import { useAuth } from '@/components/auth';

interface AppCheckStatusData {
  hasToken: boolean;
  isValid: boolean;
  appId?: string;
  error?: string;
  timestamp: string;
  environment: string;
}

export default function AppCheckStatus() {
  const { user, loading: authLoading } = useAuth();
  const [status, setStatus] = useState<AppCheckStatusData | null>(null);
  const [clientStatus, setClientStatus] = useState<{
    initialized: boolean;
    configValid: boolean;
    configErrors: string[];
    token: string | null;
  }>({
    initialized: false,
    configValid: false,
    configErrors: [],
    token: null
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Check client-side App Check status
  useEffect(() => {
    const checkClientStatus = async () => {
      const initialized = isAppCheckInitialized();
      const configValidation = validateAppCheckConfig();
      
      let token: string | null = null;
      if (initialized) {
        try {
          token = await getAppCheckToken();
        } catch (err) {
          console.warn('Failed to get App Check token:', err);
        }
      }

      setClientStatus({
        initialized,
        configValid: configValidation.isValid,
        configErrors: configValidation.errors,
        token
      });
    };

    checkClientStatus();
  }, []);

  // Check server-side App Check status
  const checkServerStatus = async () => {
    if (!user || authLoading) return;

    setLoading(true);
    setError(null);

    try {
      const functions = getFunctions();
      const getAppCheckStatus = httpsCallable(functions, 'getAppCheckTokenStatus');
      
      const result = await getAppCheckStatus();
      setStatus(result.data as AppCheckStatusData);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Unknown error';
      setError(errorMessage);
      console.error('Failed to get App Check status:', err);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (user && !authLoading) {
      checkServerStatus();
    }
  }, [user, authLoading]);

  if (authLoading) {
    return <div className="p-4">Loading authentication...</div>;
  }

  if (!user) {
    return (
      <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-yellow-800">Please sign in to check App Check status.</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="bg-white border border-gray-200 rounded-lg p-4">
        <h3 className="text-lg font-semibold mb-3">Firebase App Check Status</h3>
        
        {/* Client-side status */}
        <div className="mb-4">
          <h4 className="font-medium text-gray-900 mb-2">Client-side Status</h4>
          <div className="space-y-2 text-sm">
            <div className="flex items-center space-x-2">
              <span className={`w-3 h-3 rounded-full ${clientStatus.initialized ? 'bg-green-500' : 'bg-red-500'}`}></span>
              <span>App Check Initialized: {clientStatus.initialized ? 'Yes' : 'No'}</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className={`w-3 h-3 rounded-full ${clientStatus.configValid ? 'bg-green-500' : 'bg-red-500'}`}></span>
              <span>Configuration Valid: {clientStatus.configValid ? 'Yes' : 'No'}</span>
            </div>
            {clientStatus.configErrors.length > 0 && (
              <div className="ml-5 text-red-600">
                <p>Configuration errors:</p>
                <ul className="list-disc list-inside ml-2">
                  {clientStatus.configErrors.map((error, index) => (
                    <li key={index}>{error}</li>
                  ))}
                </ul>
              </div>
            )}
            <div className="flex items-center space-x-2">
              <span className={`w-3 h-3 rounded-full ${clientStatus.token ? 'bg-green-500' : 'bg-red-500'}`}></span>
              <span>Token Available: {clientStatus.token ? 'Yes' : 'No'}</span>
            </div>
            {clientStatus.token && (
              <div className="ml-5 text-xs text-gray-600 font-mono break-all">
                Token: {clientStatus.token.substring(0, 50)}...
              </div>
            )}
          </div>
        </div>

        {/* Server-side status */}
        <div>
          <div className="flex items-center justify-between mb-2">
            <h4 className="font-medium text-gray-900">Server-side Status</h4>
            <button
              onClick={checkServerStatus}
              disabled={loading}
              className="px-3 py-1 text-sm bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
            >
              {loading ? 'Checking...' : 'Refresh'}
            </button>
          </div>
          
          {error && (
            <div className="bg-red-50 border border-red-200 rounded p-3 mb-3">
              <p className="text-red-800 text-sm">Error: {error}</p>
            </div>
          )}
          
          {status && (
            <div className="space-y-2 text-sm">
              <div className="flex items-center space-x-2">
                <span className={`w-3 h-3 rounded-full ${status.hasToken ? 'bg-green-500' : 'bg-red-500'}`}></span>
                <span>Server Received Token: {status.hasToken ? 'Yes' : 'No'}</span>
              </div>
              <div className="flex items-center space-x-2">
                <span className={`w-3 h-3 rounded-full ${status.isValid ? 'bg-green-500' : 'bg-red-500'}`}></span>
                <span>Token Valid: {status.isValid ? 'Yes' : 'No'}</span>
              </div>
              {status.appId && (
                <div className="ml-5 text-gray-600">
                  App ID: {status.appId}
                </div>
              )}
              {status.error && (
                <div className="ml-5 text-red-600">
                  Error: {status.error}
                </div>
              )}
              <div className="ml-5 text-gray-500 text-xs">
                Environment: {status.environment} | Last checked: {new Date(status.timestamp).toLocaleString()}
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Environment information */}
      <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
        <h4 className="font-medium text-gray-900 mb-2">Environment Information</h4>
        <div className="text-sm text-gray-600 space-y-1">
          <p>Node Environment: {process.env.NODE_ENV}</p>
          <p>App Check Provider: {process.env.NODE_ENV === 'production' ? 'reCAPTCHA v3' : 'Debug Token'}</p>
          <p>Debug Token Available: {process.env.NEXT_PUBLIC_FIREBASE_APPCHECK_DEBUG_TOKEN ? 'Yes' : 'No'}</p>
          <p>reCAPTCHA Site Key Available: {process.env.NEXT_PUBLIC_FIREBASE_APPCHECK_RECAPTCHA_SITE_KEY ? 'Yes' : 'No'}</p>
        </div>
      </div>

      {/* Quick actions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-medium text-blue-900 mb-2">Quick Actions</h4>
        <div className="space-y-2 text-sm">
          <button
            onClick={() => window.location.reload()}
            className="block w-full text-left px-3 py-2 bg-blue-100 hover:bg-blue-200 rounded"
          >
            Reload Page (Re-initialize App Check)
          </button>
          <button
            onClick={checkServerStatus}
            disabled={loading}
            className="block w-full text-left px-3 py-2 bg-blue-100 hover:bg-blue-200 rounded disabled:opacity-50"
          >
            Test Function Call
          </button>
        </div>
      </div>
    </div>
  );
} 
'use client'

import { motion } from 'framer-motion'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { cn } from '@/lib/utils/utils'

interface AnimatedCardProps {
  children: React.ReactNode
  className?: string
  delay?: number
  hoverScale?: number
}

export default function AnimatedCard({ 
  children, 
  className, 
  delay = 0,
  hoverScale = 1.02
}: AnimatedCardProps) {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ 
        duration: 0.5, 
        delay,
        ease: "easeOut"
      }}
      whileHover={{ 
        scale: hoverScale,
        transition: { duration: 0.2 }
      }}
      className="group"
    >
      <Card className={cn(
        "relative overflow-hidden",
        "hover:shadow-2xl hover:shadow-blue-500/20 transition-all duration-300",
        "border-0 bg-gradient-to-br from-slate-900/20 to-slate-800/20 backdrop-blur-sm",
        "before:absolute before:inset-0 before:bg-gradient-to-r before:from-blue-500/10 before:via-blue-600/10 before:to-blue-700/10 before:opacity-0 hover:before:opacity-100 before:transition-opacity before:duration-300",
        className
      )}>
        <div className="relative z-10">
          {children}
        </div>
      </Card>
    </motion.div>
  )
}

interface AnimatedFeatureCardProps {
  icon: React.ReactNode
  title: string
  description: string
  delay?: number
}

export function AnimatedFeatureCard({ 
  icon, 
  title, 
  description, 
  delay = 0 
}: AnimatedFeatureCardProps) {
  return (
    <AnimatedCard delay={delay}>
      <CardHeader>
        <CardTitle className="flex items-center gap-3">
          <motion.div
            className="text-red-400"
            whileHover={{ rotate: 360 }}
            transition={{ duration: 0.5 }}
          >
            {icon}
          </motion.div>
          <span className="text-red-gradient">
            {title}
          </span>
        </CardTitle>
      </CardHeader>
      <CardContent>
        <CardDescription className="text-base text-slate-300">
          {description}
        </CardDescription>
      </CardContent>
    </AnimatedCard>
  )
} 
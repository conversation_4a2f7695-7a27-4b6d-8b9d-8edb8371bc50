'use client';

import { usePathname } from 'next/navigation';
import { AuthProvider } from './auth';
import { AppInitialization } from './app-initialization';

interface ConditionalAuthWrapperProps {
  children: React.ReactNode;
}

// Define routes that need authentication and storage initialization
const AUTHENTICATED_ROUTES = [
  '/dashboard',
  '/checklist',
  '/saved',
  '/admin',
  '/tools' // Some tools may require auth
];

// Define routes that need auth provider but not storage initialization
const AUTH_ONLY_ROUTES = [
  '/tools' // Tools page shows different content based on auth state
];

// Check if current route needs authentication
function needsAuth(pathname: string): boolean {
  return AUTHENTICATED_ROUTES.some(route => pathname.startsWith(route));
}

// Check if current route needs auth provider only (no storage init)
function needsAuthProvider(pathname: string): boolean {
  return AUTH_ONLY_ROUTES.some(route => pathname.startsWith(route)) || needsAuth(pathname);
}

export function ConditionalAuthWrapper({ children }: ConditionalAuthWrapperProps) {
  const pathname = usePathname();
  
  const requiresAuth = needsAuth(pathname);
  
  // Always provide AuthProvider so authentication works on all pages
  // Only add AppInitialization (storage) for pages that need it
  if (requiresAuth) {
    return (
      <AuthProvider>
        <AppInitialization>
          {children}
        </AppInitialization>
      </AuthProvider>
    );
  }
  
  // For public pages, provide AuthProvider without storage initialization
  return (
    <AuthProvider>
      {children}
    </AuthProvider>
  );
} 
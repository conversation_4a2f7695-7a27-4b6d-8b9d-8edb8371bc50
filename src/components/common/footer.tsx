"use client";

import { motion } from "framer-motion";
import Link from "next/link";
import { 
  ExternalLink, 
  Mail, 
  Phone, 
  MapPin, 
  CheckSquare, 
  Database,
  Shield,
  Clock,
  Users,
  Award,
  Brain,
  BarChart3,
  Settings,
  Cloud
} from "lucide-react";
import { GradientText } from "../gradient-text";
import { AuburnIcon } from "../auburn-icon";

const quickLinks = [
  { name: "New Checklist", href: "/checklist", icon: CheckSquare },
  { name: "Dashboard", href: "/dashboard", icon: BarChart3 },
  { name: "Saved Inspections", href: "/saved", icon: Database },
  { name: "Admin Panel", href: "/admin", icon: Settings },
];

const features = [
  { name: "AI-Powered Insights", icon: Brain },
  { name: "QCD Compliance", icon: Shield },
  { name: "Cloud Sync", icon: Cloud },
  { name: "User Management", icon: Users },
  { name: "Real-time Analytics", icon: Bar<PERSON>hart3 },
  { name: "Professional Reports", icon: Award },
];

const contactInfo = [
  { 
    label: "Email", 
    value: "<EMAIL>", 
    href: "mailto:<EMAIL>",
    icon: Mail 
  },
  { 
    label: "Phone", 
    value: "+974 70300401", 
    href: "tel:+97470300401",
    icon: Phone 
  },
  { 
    label: "Address", 
    value: "Doha, Qatar (Est. 2013)",
    href: "https://www.auburnengineering.com",
    icon: MapPin 
  },
];

export function Footer() {
  return (
    <motion.footer 
      className="w-full border-t border-blue-500/20 glass backdrop-blur-xl mt-auto"
      initial={{ opacity: 0, y: 50 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.6, ease: "easeOut" }}
    >
      <div className="container mx-auto px-4 py-12">
        {/* Main Footer Content */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
          
          {/* Brand Section */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.1 }}
            className="space-y-4"
          >
            <div className="flex items-center space-x-4">
              <AuburnIcon size={40} className="drop-shadow-lg" />
              <div>
                <h3 className="font-bold text-xl">
                  <GradientText 
                    gradient="from-blue-200 to-cyan-200" 
                    animate={false}
                  >
                    Auburn Engineering
                  </GradientText>
                </h3>
                <p className="text-sm text-slate-400 font-medium">Advanced PPM Suite</p>
              </div>
            </div>
            <p className="text-sm text-slate-300 leading-relaxed">
              AI-enhanced digital inspection platform for ACMV systems with comprehensive 
              QCD compliance, cloud synchronization, and professional user management solutions.
            </p>
            <div className="text-xs text-slate-400">
              <p>🏢 Trusted ACMV Leader in Qatar</p>
              <p>🔧 QCD & Kahramaa Certified</p>
              <p>⚡ 15+ Years of Excellence</p>
            </div>
          </motion.div>

          {/* Quick Links */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="space-y-4"
          >
            <h4 className="font-semibold text-white">Quick Access</h4>
            <div className="space-y-3">
              {quickLinks.map((link) => {
                const Icon = link.icon;
                return (
                  <motion.div
                    key={link.name}
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Link
                      href={link.href}
                      className="flex items-center space-x-2 text-sm text-slate-300 hover:text-white transition-colors duration-300 group"
                    >
                      <Icon className="h-4 w-4 group-hover:text-blue-400 transition-colors duration-300" />
                      <span>{link.name}</span>
                    </Link>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Features */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="space-y-4"
          >
            <h4 className="font-semibold text-white">Platform Features</h4>
            <div className="space-y-3">
              {features.map((feature) => {
                const Icon = feature.icon;
                return (
                  <motion.div
                    key={feature.name}
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                    className="flex items-center space-x-2 text-sm text-slate-300"
                  >
                    <Icon className="h-4 w-4 text-blue-400" />
                    <span>{feature.name}</span>
                  </motion.div>
                );
              })}
            </div>
          </motion.div>

          {/* Contact Information */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="space-y-4"
          >
            <h4 className="font-semibold text-white">Contact Auburn</h4>
            <div className="space-y-3">
              {contactInfo.map((contact) => {
                const Icon = contact.icon;
                return (
                  <motion.div
                    key={contact.label}
                    whileHover={{ scale: 1.02 }}
                    transition={{ duration: 0.2 }}
                  >
                    <Link
                      href={contact.href}
                      className="flex items-start space-x-2 text-sm text-slate-300 hover:text-white transition-colors duration-300 group"
                    >
                      <Icon className="h-4 w-4 mt-0.5 group-hover:text-blue-400 transition-colors duration-300 flex-shrink-0" />
                      <span className="leading-relaxed">{contact.value}</span>
                    </Link>
                  </motion.div>
                );
              })}
            </div>
            <div className="mt-4 pt-4 border-t border-slate-700">
              <p className="text-xs text-slate-400 leading-relaxed">
                Specializing in ACMV Systems, Generators, 
                Electrical Systems & Energy Solutions
              </p>
            </div>
          </motion.div>
        </div>

        {/* Technology Section */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
          className="border-t border-blue-500/20 pt-8 mb-8"
        >
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <h5 className="font-semibold text-white text-sm">Technology Stack</h5>
              <div className="flex flex-wrap gap-2">
                {["React Framework", "TypeScript", "Cloud Backend", "Modern CSS"].map((tech) => (
                  <span
                    key={tech}
                    className="px-2 py-1 bg-blue-900/20 text-blue-200 text-xs rounded-md border border-blue-500/30"
                  >
                    {tech}
                  </span>
                ))}
              </div>
            </div>
            
            <div className="space-y-2">
              <h5 className="font-semibold text-white text-sm">Compliance</h5>
              <div className="flex flex-wrap gap-2">
                {["QCD Certified", "Kahramaa Approved", "ISO Standards"].map((cert) => (
                  <span
                    key={cert}
                    className="px-2 py-1 bg-green-900/20 text-green-200 text-xs rounded-md border border-green-500/30"
                  >
                    {cert}
                  </span>
                ))}
              </div>
            </div>
            
            <div className="space-y-2">
              <h5 className="font-semibold text-white text-sm">Security</h5>
              <div className="flex flex-wrap gap-2">
                {["End-to-End Encryption", "Role-Based Access", "Audit Trails"].map((security) => (
                  <span
                    key={security}
                    className="px-2 py-1 bg-amber-900/20 text-amber-200 text-xs rounded-md border border-amber-500/30"
                  >
                    {security}
                  </span>
                ))}
              </div>
            </div>
          </div>
        </motion.div>

        {/* Bottom Bar */}
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.6, delay: 0.6 }}
          className="border-t border-blue-500/20 pt-6"
        >
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex items-center space-x-4">
              <Link
                href="https://www.auburnengineering.com"
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center space-x-2 text-slate-300 hover:text-white transition-colors duration-300 group"
              >
                <span className="text-sm">Visit Auburn Engineering</span>
                <ExternalLink className="h-4 w-4 group-hover:text-blue-400 transition-colors duration-300" />
              </Link>
            </div>
            
            <div className="flex items-center space-x-2 text-xs text-slate-500">
              <Clock className="h-3 w-3" />
              <span>Last updated: {new Date().toLocaleDateString()}</span>
            </div>
          </div>
          
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.6, delay: 0.8 }}
            className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4 text-xs text-slate-500"
          >
            <div className="flex items-center space-x-2">
              <span>© 2024 Auburn Engineering WLL</span>
              <span>•</span>
              <span>All rights reserved</span>
            </div>
            <div className="flex items-center space-x-2">
              <span>•</span>
              <span>QCD Certified</span>
              <span>•</span>
              <span>AgneX Studio</span>
            </div>
          </motion.div>
        </motion.div>
      </div>
    </motion.footer>
  );
} 
import { ChecklistWithUser } from '@/lib/services/checklist/admin-checklist-service';
import { BulkExportResult } from '@/lib/services/export/bulk-export-service';
import { format } from 'date-fns';

interface BulkPDFCoverProps {
  checklists: ChecklistWithUser[];
  result: BulkExportResult;
  logoDataUrl?: string;
}

export function BulkPDFCover({ checklists, result, logoDataUrl }: BulkPDFCoverProps) {
  // Get summary statistics
  
  // Get unique clients and buildings
  const uniqueClients = Array.from(new Set(checklists.map(c => c.generalInfo.clientName)));
  const uniqueBuildings = Array.from(new Set(checklists.map(c => c.generalInfo.building)));
  
  // Get date range
  const dates = checklists.map(c => new Date(c.generalInfo.date)).sort((a, b) => a.getTime() - b.getTime());
  const dateRange = dates.length > 1 
    ? `${format(dates[0], 'MMM dd, yyyy')} - ${format(dates[dates.length - 1], 'MMM dd, yyyy')}`
    : format(dates[0] || new Date(), 'MMM dd, yyyy');

  return (
    <div style={{
      fontFamily: 'Arial, "Helvetica Neue", Helvetica, sans-serif',
      width: '210mm',
      height: '297mm',
      margin: '0 auto',
      padding: '0',
      boxSizing: 'border-box',
      backgroundColor: '#ffffff',
      display: 'flex',
      flexDirection: 'column',
      position: 'relative'
    }}>
      {/* Header */}
      <div style={{
        backgroundColor: '#CC0000',
        color: 'white',
        padding: '30px 40px',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        minHeight: '120px'
      }}>
        <div style={{ flex: 1 }}>
          <h1 style={{
            fontSize: '32px',
            fontWeight: '700',
            margin: '0 0 8px 0',
            letterSpacing: '-0.5px',
            lineHeight: '1.2'
          }}>
            PPM Ventilation System
          </h1>
          <h2 style={{
            fontSize: '24px',
            fontWeight: '600',
            margin: '0 0 4px 0',
            opacity: '0.95'
          }}>
            Bulk Inspection Report
          </h2>
          <p style={{
            fontSize: '14px',
            margin: '0',
            opacity: '0.9',
            fontWeight: '500'
          }}>
            Preventive Maintenance Checklist Summary
          </p>
        </div>
        <div style={{
          width: '180px',
          height: '90px',
          backgroundColor: 'white',
          borderRadius: '8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          boxShadow: '0 4px 12px rgba(0,0,0,0.2)',
          border: '2px solid rgba(255,255,255,0.3)'
        }}>
          <img 
            src={logoDataUrl || "/images/logo.jpeg"} 
            alt="Auburn Engineering"
            style={{
              maxWidth: '90%',
              maxHeight: '90%',
              objectFit: 'contain'
            }}
          />
        </div>
      </div>

      {/* Main content */}
      <div style={{
        flex: 1,
        padding: '40px',
        display: 'flex',
        flexDirection: 'column',
        gap: '20px'
      }}>
        {/* Top Section - Report Summary and Project Overview */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(2, 1fr)',
          gap: '20px'
        }}>
          <div style={{
            background: 'linear-gradient(135deg, #ffffff 0%, #ffffff 100%)',
            border: '1px solid #e0e0e0',
            borderRadius: '8px',
            padding: '20px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
          }}>
            <h3 style={{
              fontSize: '16px',
              fontWeight: '700',
              color: '#CC0000',
              margin: '0 0 12px 0',
              borderBottom: '2px solid #CC0000',
              paddingBottom: '6px'
            }}>
              Report Summary
            </h3>
            <div style={{ display: 'flex', flexDirection: 'column', gap: '8px', fontSize: '14px' }}>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span style={{ fontWeight: '600', color: '#495057' }}>Generated:</span>
                <span style={{ fontWeight: '600', color: '#212529' }}>{format(new Date(), 'MMM dd, yyyy HH:mm')}</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span style={{ fontWeight: '600', color: '#495057' }}>Total Checklists:</span>
                <span style={{ fontWeight: '700', color: '#CC0000', fontSize: '16px' }}>{checklists.length}</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span style={{ fontWeight: '600', color: '#495057' }}>Successfully Processed:</span>
                <span style={{ fontWeight: '700', color: '#28a745', fontSize: '16px' }}>{result.processedCount}</span>
              </div>
              <div style={{ display: 'flex', justifyContent: 'space-between' }}>
                <span style={{ fontWeight: '600', color: '#495057' }}>Failed:</span>
                <span style={{ fontWeight: '700', color: result.failedCount > 0 ? '#dc3545' : '#6c757d', fontSize: '16px' }}>{result.failedCount}</span>
              </div>
            </div>
          </div>

          <div style={{
            background: 'linear-gradient(135deg, #ffffff 0%, #ffffff 100%)',
            border: '1px solid #e0e0e0',
            borderRadius: '8px',
            padding: '20px',
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)'
          }}>
            <h3 style={{
              fontSize: '16px',
              fontWeight: '700',
              color: '#CC0000',
              margin: '0 0 12px 0',
              borderBottom: '2px solid #CC0000',
              paddingBottom: '6px'
            }}>
              Project Overview
            </h3>
            <div style={{
              display: 'flex',
              flexDirection: 'column',
              gap: '12px',
              fontSize: '14px'
            }}>
              <div>
                <span style={{ fontWeight: '700', color: '#495057', display: 'block', marginBottom: '4px' }}>Client(s):</span>
                <span style={{ color: '#212529', fontWeight: '600' }}>{uniqueClients.join(', ')}</span>
              </div>
              <div>
                <span style={{ fontWeight: '700', color: '#495057', display: 'block', marginBottom: '4px' }}>Building(s):</span>
                <span style={{ color: '#212529', fontWeight: '600' }}>{uniqueBuildings.join(', ')}</span>
              </div>
              <div>
                <span style={{ fontWeight: '700', color: '#495057', display: 'block', marginBottom: '4px' }}>Date Range:</span>
                <span style={{ color: '#212529', fontWeight: '600' }}>{dateRange}</span>
              </div>
            </div>
          </div>
        </div>

        {/* Checklist Summary */}
        <div style={{
          background: 'linear-gradient(135deg, #ffffff 0%, #ffffff 100%)',
          border: '1px solid #e0e0e0',
          borderRadius: '8px',
          padding: '20px',
          boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
          flex: 1
        }}>
          <h3 style={{
            fontSize: '16px',
            fontWeight: '700',
            color: '#CC0000',
            margin: '0 0 12px 0',
            borderBottom: '2px solid #CC0000',
            paddingBottom: '6px'
          }}>
            Checklist Summary
          </h3>
          <div style={{ fontSize: '12px' }}>
            {checklists.slice(0, 25).map((checklist, index) => (
              <div key={checklist.id} style={{
                display: 'flex',
                justifyContent: 'space-between',
                padding: '4px 8px',
                borderBottom: '1px solid #e0e0e0',
                backgroundColor: index % 2 === 0 ? '#ffffff' : '#ffffff'
              }}>
                <span style={{ fontWeight: '600', color: '#212529' }}>
                  {index + 1}. {checklist.generalInfo.tagNo} - {checklist.generalInfo.equipmentName}
                </span>
                <span style={{
                  padding: '2px 6px',
                  borderRadius: '8px',
                  fontSize: '10px',
                  fontWeight: '600',
                  backgroundColor: checklist.isCompleted ? '#e8f5e8' : '#fff3cd',
                  color: checklist.isCompleted ? '#155724' : '#856404'
                }}>
                  {checklist.isCompleted ? 'Complete' : 'Pending'}
                </span>
              </div>
            ))}
            {checklists.length > 25 && (
              <div style={{
                padding: '8px',
                textAlign: 'center',
                fontStyle: 'italic',
                color: '#6c757d'
              }}>
                ... and {checklists.length - 25} more checklists
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Footer */}
      <div style={{
        backgroundColor: '#ffffff',
        borderTop: '2px solid #CC0000',
        padding: '20px 40px',
        textAlign: 'center'
      }}>
        <p style={{
          fontSize: '12px',
          color: '#6c757d',
          margin: '0',
          fontWeight: '500'
        }}>
          This report was generated automatically by Auburn Engineering PPM System
          <br />
          Generated on {format(new Date(), 'EEEE, MMMM dd, yyyy \'at\' HH:mm:ss')}
        </p>
      </div>
    </div>
  );
} 
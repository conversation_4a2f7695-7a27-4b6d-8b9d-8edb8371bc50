'use client';

import React, { useState } from 'react';
import { LineChart, Line, Area, AreaChart, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer } from 'recharts';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { TrendData, TIME_RANGES } from '@/lib/utils/data/chart-data-processor';
import { TrendingUp, TrendingDown, Minus } from 'lucide-react';

interface InspectionTrendsChartProps {
  data: TrendData[];
  timeRange: string;
  onTimeRangeChange: (value: string) => void;
  className?: string;
}

interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{
    value: number;
    dataKey: string;
    color: string;
  }>;
  label?: string;
}

const CustomTooltip: React.FC<CustomTooltipProps> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    const data = payload[0];
    const completed = payload.find(p => p.dataKey === 'completed')?.value || 0;
    const total = payload.find(p => p.dataKey === 'total')?.value || 0;
    const successRate = payload.find(p => p.dataKey === 'successRate')?.value || 0;

    return (
      <div className="bg-background/95 backdrop-blur-sm border border-border rounded-lg p-4 shadow-lg">
        <p className="font-medium text-foreground mb-2">{label}</p>
        <div className="space-y-1">
          <p className="text-sm text-muted-foreground">
            Completed: <span className="font-medium text-green-500">{completed}</span>
          </p>
          <p className="text-sm text-muted-foreground">
            Total: <span className="font-medium text-foreground">{total}</span>
          </p>
          <p className="text-sm text-muted-foreground">
            Success Rate: <span className="font-medium text-blue-500">{successRate}%</span>
          </p>
        </div>
      </div>
    );
  }
  return null;
};

export function InspectionTrendsChart({ 
  data, 
  timeRange, 
  onTimeRangeChange, 
  className = '' 
}: InspectionTrendsChartProps) {
  const [chartType, setChartType] = useState<'line' | 'area'>('area');
  
  const hasData = data.length > 0 && data.some(d => d.total > 0);
  
  // Calculate trend
  const calculateTrend = () => {
    if (data.length < 2) return { direction: 'stable', value: 0 };
    
    const recentData = data.slice(-7); // Last 7 data points
    const olderData = data.slice(0, 7); // First 7 data points
    
    const recentAvg = recentData.reduce((sum, d) => sum + d.successRate, 0) / recentData.length;
    const olderAvg = olderData.reduce((sum, d) => sum + d.successRate, 0) / olderData.length;
    
    const difference = recentAvg - olderAvg;
    
    if (difference > 5) return { direction: 'up', value: Math.round(difference) };
    if (difference < -5) return { direction: 'down', value: Math.round(Math.abs(difference)) };
    return { direction: 'stable', value: Math.round(Math.abs(difference)) };
  };

  const trend = calculateTrend();
  const totalInspections = data.reduce((sum, d) => sum + d.total, 0);
  const averageSuccessRate = data.length > 0 
    ? Math.round(data.reduce((sum, d) => sum + d.successRate, 0) / data.length)
    : 0;

  const TrendIcon = trend.direction === 'up' ? TrendingUp : trend.direction === 'down' ? TrendingDown : Minus;
  const trendColor = trend.direction === 'up' ? 'text-green-500' : trend.direction === 'down' ? 'text-red-500' : 'text-gray-500';

  return (
    <Card className={`glass border-border/50 ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold">Inspection Trends</CardTitle>
            <CardDescription>
              Performance over time • {totalInspections} total inspections
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Select value={timeRange} onValueChange={onTimeRangeChange}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {TIME_RANGES.map(range => (
                  <SelectItem key={range.value} value={range.value}>
                    {range.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
        
        {/* Trend Indicator */}
        <div className="flex items-center gap-4 mt-2">
          <div className={`flex items-center gap-1 ${trendColor}`}>
            <TrendIcon className="h-4 w-4" />
            <span className="text-sm font-medium">
              {trend.direction === 'stable' ? 'Stable' : `${trend.value}% ${trend.direction === 'up' ? 'improvement' : 'decline'}`}
            </span>
          </div>
          <div className="text-sm text-muted-foreground">
            Avg Success Rate: <span className="font-medium text-foreground">{averageSuccessRate}%</span>
          </div>
        </div>
      </CardHeader>
      
      <CardContent>
        {hasData ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <ResponsiveContainer width="100%" height={350}>
              {chartType === 'area' ? (
                <AreaChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                  <defs>
                    <linearGradient id="completedGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#22c55e" stopOpacity={0.3}/>
                      <stop offset="95%" stopColor="#22c55e" stopOpacity={0}/>
                    </linearGradient>
                    <linearGradient id="totalGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor="#3b82f6" stopOpacity={0.2}/>
                      <stop offset="95%" stopColor="#3b82f6" stopOpacity={0}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid 
                    strokeDasharray="3 3" 
                    stroke="rgba(156, 163, 175, 0.2)"
                    horizontal={true}
                    vertical={false}
                  />
                  <XAxis 
                    dataKey="date" 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: 'rgba(156, 163, 175, 0.8)' }}
                  />
                  <YAxis 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: 'rgba(156, 163, 175, 0.8)' }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Area
                    type="monotone"
                    dataKey="total"
                    stroke="#3b82f6"
                    strokeWidth={2}
                    fill="url(#totalGradient)"
                    animationDuration={1000}
                  />
                  <Area
                    type="monotone"
                    dataKey="completed"
                    stroke="#22c55e"
                    strokeWidth={2}
                    fill="url(#completedGradient)"
                    animationDuration={1200}
                  />
                </AreaChart>
              ) : (
                <LineChart data={data} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid 
                    strokeDasharray="3 3" 
                    stroke="rgba(156, 163, 175, 0.2)"
                    horizontal={true}
                    vertical={false}
                  />
                  <XAxis 
                    dataKey="date" 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: 'rgba(156, 163, 175, 0.8)' }}
                  />
                  <YAxis 
                    axisLine={false}
                    tickLine={false}
                    tick={{ fontSize: 12, fill: 'rgba(156, 163, 175, 0.8)' }}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <Line
                    type="monotone"
                    dataKey="total"
                    stroke="#3b82f6"
                    strokeWidth={3}
                    dot={{ fill: '#3b82f6', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, fill: '#3b82f6' }}
                    animationDuration={1000}
                  />
                  <Line
                    type="monotone"
                    dataKey="completed"
                    stroke="#22c55e"
                    strokeWidth={3}
                    dot={{ fill: '#22c55e', strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, fill: '#22c55e' }}
                    animationDuration={1200}
                  />
                </LineChart>
              )}
            </ResponsiveContainer>
            
            {/* Chart Type Toggle */}
            <div className="flex justify-center mt-4">
              <div className="flex bg-muted rounded-lg p-1">
                <button
                  onClick={() => setChartType('area')}
                  className={`px-3 py-1 text-sm rounded-md transition-colors ${
                    chartType === 'area' 
                      ? 'bg-background shadow-sm text-foreground' 
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                >
                  Area
                </button>
                <button
                  onClick={() => setChartType('line')}
                  className={`px-3 py-1 text-sm rounded-md transition-colors ${
                    chartType === 'line' 
                      ? 'bg-background shadow-sm text-foreground' 
                      : 'text-muted-foreground hover:text-foreground'
                  }`}
                >
                  Line
                </button>
              </div>
            </div>
          </motion.div>
        ) : (
          <div className="flex items-center justify-center h-[350px]">
            <div className="text-center space-y-2">
              <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto">
                <span className="text-2xl">📈</span>
              </div>
              <p className="text-muted-foreground">No trend data available</p>
              <p className="text-sm text-muted-foreground">Complete some inspections to see trends</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 
'use client';

import React, { useState } from 'react';
import { BarChart, Bar, XAxis, <PERSON><PERSON>xis, CartesianGrid, Tooltip, Responsive<PERSON><PERSON><PERSON>, <PERSON> } from 'recharts';
import { motion } from 'framer-motion';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { CategoryPerformanceData, STATUS_COLORS } from '@/lib/utils/data/chart-data-processor';
import { Badge } from '@/components/ui/badge';

interface CategoryPerformanceChartProps {
  data: CategoryPerformanceData[];
  className?: string;
}

interface CustomTooltipProps {
  active?: boolean;
  payload?: Array<{
    value: number;
    dataKey: string;
    color: string;
    payload?: any;
  }>;
  label?: string;
}

const CustomTooltip: React.FC<CustomTooltipProps> = ({ active, payload, label }) => {
  if (active && payload && payload.length) {
    const categoryData = payload[0]?.payload as CategoryPerformanceData;
    
    return (
      <div className="bg-background/95 backdrop-blur-sm border border-border rounded-lg p-4 shadow-lg">
        <p className="font-medium text-foreground mb-2">{label}</p>
        <div className="space-y-1">
          <div className="flex items-center justify-between gap-4">
            <span className="text-sm text-muted-foreground">OK:</span>
            <span className="font-medium text-green-500">{categoryData.ok}</span>
          </div>
          <div className="flex items-center justify-between gap-4">
            <span className="text-sm text-muted-foreground">Faulty:</span>
            <span className="font-medium text-red-500">{categoryData.faulty}</span>
          </div>
          <div className="flex items-center justify-between gap-4">
            <span className="text-sm text-muted-foreground">N/A:</span>
            <span className="font-medium text-yellow-600">{categoryData.na}</span>
          </div>
          <div className="flex items-center justify-between gap-4">
            <span className="text-sm text-muted-foreground">Missing:</span>
            <span className="font-medium text-gray-500">{categoryData.missing}</span>
          </div>
          <div className="border-t border-border pt-2 mt-2">
            <div className="flex items-center justify-between gap-4">
              <span className="text-sm text-muted-foreground">Success Rate:</span>
              <span className="font-medium text-blue-500">{categoryData.successRate}%</span>
            </div>
            <div className="flex items-center justify-between gap-4">
              <span className="text-sm text-muted-foreground">Total Checks:</span>
              <span className="font-medium text-foreground">{categoryData.total}</span>
            </div>
          </div>
        </div>
      </div>
    );
  }
  return null;
};

const CustomLegend = ({ payload }: any) => {
  const legendItems = [
    { value: 'OK', color: STATUS_COLORS.OK },
    { value: 'Faulty', color: STATUS_COLORS.Faulty },
    { value: 'N/A', color: STATUS_COLORS['N/A'] },
    { value: 'Missing', color: STATUS_COLORS.Missing }
  ];

  return (
    <div className="flex flex-wrap justify-center gap-4 mt-4">
      {legendItems.map((item, index) => (
        <div key={index} className="flex items-center gap-2">
          <div 
            className="w-3 h-3 rounded-sm"
            style={{ backgroundColor: item.color }}
          />
          <span className="text-sm text-muted-foreground">{item.value}</span>
        </div>
      ))}
    </div>
  );
};

export function CategoryPerformanceChart({ data, className = '' }: CategoryPerformanceChartProps) {
  const [viewMode, setViewMode] = useState<'stacked' | 'grouped'>('stacked');
  
  const hasData = data.length > 0 && data.some(d => d.total > 0);
  
  // Calculate overall stats
  const totalChecks = data.reduce((sum, d) => sum + d.total, 0);
  const totalOk = data.reduce((sum, d) => sum + d.ok, 0);
  const overallSuccessRate = totalChecks > 0 ? Math.round((totalOk / totalChecks) * 100) : 0;
  
  // Find best and worst performing categories
  const bestCategory = data.reduce((best, current) => 
    current.successRate > best.successRate ? current : best, data[0]);
  const worstCategory = data.reduce((worst, current) => 
    current.successRate < worst.successRate ? current : worst, data[0]);

  return (
    <Card className={`glass border-border/50 ${className}`}>
      <CardHeader className="pb-2">
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-lg font-semibold">Category Performance</CardTitle>
            <CardDescription>
              Performance breakdown by inspection type • {totalChecks} total checks
            </CardDescription>
          </div>
          <div className="flex bg-muted rounded-lg p-1">
            <button
              onClick={() => setViewMode('stacked')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                viewMode === 'stacked' 
                  ? 'bg-background shadow-sm text-foreground' 
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              Stacked
            </button>
            <button
              onClick={() => setViewMode('grouped')}
              className={`px-3 py-1 text-sm rounded-md transition-colors ${
                viewMode === 'grouped' 
                  ? 'bg-background shadow-sm text-foreground' 
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              Grouped
            </button>
          </div>
        </div>
        
        {/* Performance Summary */}
        {hasData && (
          <div className="flex items-center gap-4 mt-2 flex-wrap">
            <div className="text-sm text-muted-foreground">
              Overall Success Rate: <span className="font-medium text-foreground">{overallSuccessRate}%</span>
            </div>
            {bestCategory && (
              <Badge variant="outline" className="text-green-500 border-green-500/30 dark:text-green-400 dark:border-green-400/30">
                Best: {bestCategory.category} ({bestCategory.successRate}%)
              </Badge>
            )}
            {worstCategory && bestCategory !== worstCategory && (
              <Badge variant="outline" className="text-red-500 border-red-500/30 dark:text-red-400 dark:border-red-400/30">
                Needs Focus: {worstCategory.category} ({worstCategory.successRate}%)
              </Badge>
            )}
          </div>
        )}
      </CardHeader>
      
      <CardContent>
        {hasData ? (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <ResponsiveContainer width="100%" height={350}>
              <BarChart 
                data={data} 
                margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                barCategoryGap="20%"
              >
                <CartesianGrid 
                  strokeDasharray="3 3" 
                  stroke="rgba(156, 163, 175, 0.2)"
                  horizontal={true}
                  vertical={false}
                />
                <XAxis 
                  dataKey="category" 
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: 'rgba(156, 163, 175, 0.8)' }}
                  angle={-45}
                  textAnchor="end"
                  height={80}
                />
                <YAxis 
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 12, fill: 'rgba(156, 163, 175, 0.8)' }}
                />
                <Tooltip content={<CustomTooltip />} />
                <Legend content={<CustomLegend />} />
                
                <Bar 
                  dataKey="ok" 
                  stackId={viewMode === 'stacked' ? 'stack' : 'ok'}
                  fill={STATUS_COLORS.OK}
                  radius={viewMode === 'stacked' ? [0, 0, 0, 0] : [2, 2, 0, 0]}
                  animationDuration={800}
                />
                <Bar 
                  dataKey="faulty" 
                  stackId={viewMode === 'stacked' ? 'stack' : 'faulty'}
                  fill={STATUS_COLORS.Faulty}
                  radius={viewMode === 'stacked' ? [0, 0, 0, 0] : [2, 2, 0, 0]}
                  animationDuration={1000}
                />
                <Bar 
                  dataKey="na" 
                  stackId={viewMode === 'stacked' ? 'stack' : 'na'}
                  fill={STATUS_COLORS['N/A']}
                  radius={viewMode === 'stacked' ? [0, 0, 0, 0] : [2, 2, 0, 0]}
                  animationDuration={1200}
                />
                <Bar 
                  dataKey="missing" 
                  stackId={viewMode === 'stacked' ? 'stack' : 'missing'}
                  fill={STATUS_COLORS.Missing}
                  radius={viewMode === 'stacked' ? [2, 2, 0, 0] : [2, 2, 0, 0]}
                  animationDuration={1400}
                />
              </BarChart>
            </ResponsiveContainer>
            
            {/* Category Insights */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
              {data.map((category, index) => (
                <motion.div
                  key={category.category}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-muted/50 rounded-lg p-3"
                >
                  <h4 className="font-medium text-sm mb-2">{category.category}</h4>
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs">
                      <span className="text-muted-foreground">Success Rate:</span>
                      <span className={`font-medium ${
                        category.successRate >= 90 ? 'text-green-600' :
                        category.successRate >= 70 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {category.successRate}%
                      </span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span className="text-muted-foreground">Total Checks:</span>
                      <span className="font-medium">{category.total}</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span className="text-muted-foreground">Issues:</span>
                      <span className="font-medium text-red-600">{category.faulty}</span>
                    </div>
                  </div>
                  
                  {/* Progress Bar */}
                  <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                    <div 
                      className="bg-green-500 h-2 rounded-full transition-all duration-500"
                      style={{ width: `${category.successRate}%` }}
                    />
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        ) : (
          <div className="flex items-center justify-center h-[350px]">
            <div className="text-center space-y-2">
              <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto">
                <span className="text-2xl">📊</span>
              </div>
              <p className="text-muted-foreground">No category data available</p>
              <p className="text-sm text-muted-foreground">Complete some inspections to see category performance</p>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
} 
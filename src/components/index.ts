// Main layout components
export { Navigation } from './common/navigation';
export { Footer } from './common/footer';
export { ThemeProvider } from './common/theme-provider';

// Background and visual components
export { default as ThreeBackground } from './three-background';
export { default as AnimatedCard, AnimatedFeatureCard } from './animated-card';
export { GradientText } from './gradient-text';

// App components
export { ConditionalAuthWrapper } from './conditional-auth-wrapper';
export { AppInitialization } from './app-initialization';

// PDF Templates - Individual template deprecated, use cloud function instead
// export { PDFTemplate } from './pdf-template'; // DEPRECATED: Use cloud function
export { PDFTemplateSSR } from './pdf-template-ssr';

// Re-export grouped components
export * from './ui';
export * from './checklist';
export * from './auth'; 
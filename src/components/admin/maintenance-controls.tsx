'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  Settings, 
  Power, 
  PowerOff, 
  Clock, 
  Users, 
  MessageSquare, 
  Save,
  AlertTriangle,
  CheckCircle,
  Calendar
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog';
import { SystemService, MaintenanceConfig } from '@/lib/services/system/system-service';
import { useAuth } from '@/components/auth/auth-provider';
import { UserRole, getRoleDisplayName } from '@/types/user';

export function MaintenanceControls() {
  const { user, extendedUser } = useAuth();
  const [maintenanceConfig, setMaintenanceConfig] = useState<MaintenanceConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [showConfirmDialog, setShowConfirmDialog] = useState(false);
  const [pendingToggle, setPendingToggle] = useState(false);
  
  // Form state
  const [formData, setFormData] = useState({
    enabled: false,
    message: '',
    scheduledStart: '',
    scheduledEnd: '',
    allowedRoles: [] as string[],
    bypassUsers: [] as string[]
  });

  useEffect(() => {
    loadMaintenanceConfig();
    
    // Subscribe to maintenance mode changes
    const unsubscribe = SystemService.subscribeToMaintenanceMode((config) => {
      if (config) {
        setMaintenanceConfig(config);
        setFormData({
          enabled: config.enabled,
          message: config.message || '',
          scheduledStart: config.scheduledStart || '',
          scheduledEnd: config.scheduledEnd || '',
          allowedRoles: config.allowedRoles || [],
          bypassUsers: config.bypassUsers || []
        });
      }
      setIsLoading(false);
    });

    return unsubscribe;
  }, []);

  const loadMaintenanceConfig = async () => {
    try {
      const config = await SystemService.getMaintenanceStatus();
      if (config) {
        setMaintenanceConfig(config);
        setFormData({
          enabled: config.enabled,
          message: config.message || '',
          scheduledStart: config.scheduledStart || '',
          scheduledEnd: config.scheduledEnd || '',
          allowedRoles: config.allowedRoles || [],
          bypassUsers: config.bypassUsers || []
        });
      }
    } catch (error) {
      console.error('Failed to load maintenance configuration:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleToggleMaintenance = () => {
    setPendingToggle(!formData.enabled);
    setShowConfirmDialog(true);
  };

  const confirmToggle = async () => {
    if (!user?.email) return;

    setIsSaving(true);
    try {
      await SystemService.updateMaintenanceMode(
        { enabled: pendingToggle },
        user.email
      );
      setShowConfirmDialog(false);
    } catch (error) {
      console.error('Failed to toggle maintenance mode:', error);
      alert('Failed to update maintenance mode. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleSaveSettings = async () => {
    if (!user?.email) return;

    setIsSaving(true);
    try {
      // Prepare update data - always include all fields to handle clearing
      const updateData: Partial<MaintenanceConfig> = {
        message: formData.message,
        allowedRoles: formData.allowedRoles,
        bypassUsers: formData.bypassUsers,
        scheduledStart: formData.scheduledStart, // Can be empty string to clear
        scheduledEnd: formData.scheduledEnd // Can be empty string to clear
      };

      await SystemService.updateMaintenanceMode(updateData, user.email);
      alert('Maintenance settings saved successfully!');
    } catch (error) {
      console.error('Failed to save maintenance settings:', error);
      alert('Failed to save settings. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  const handleRoleToggle = (role: string) => {
    setFormData(prev => ({
      ...prev,
      allowedRoles: prev.allowedRoles.includes(role)
        ? prev.allowedRoles.filter(r => r !== role)
        : [...prev.allowedRoles, role]
    }));
  };

  const formatDateTime = (dateString: string) => {
    if (!dateString || dateString.trim() === '') return '';
    try {
      return new Date(dateString).toISOString().slice(0, 16);
    } catch (error) {
      console.warn('Invalid date string for formatting:', dateString);
      return '';
    }
  };

  const parseDateTime = (dateString: string) => {
    if (!dateString || dateString.trim() === '') return '';
    try {
      return new Date(dateString).toISOString();
    } catch (error) {
      console.warn('Invalid date string for parsing:', dateString);
      return '';
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Status Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Maintenance Mode Status</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                {formData.enabled ? (
                  <PowerOff className="h-5 w-5 text-amber-500" />
                ) : (
                  <Power className="h-5 w-5 text-green-500" />
                )}
                <span className="font-medium">
                  {formData.enabled ? 'Maintenance Mode Active' : 'System Online'}
                </span>
              </div>
              <Badge 
                variant={formData.enabled ? 'destructive' : 'default'}
                className={formData.enabled ? 'bg-amber-500 hover:bg-amber-600' : 'bg-green-600 hover:bg-green-700'}
              >
                {formData.enabled ? 'MAINTENANCE' : 'ACTIVE'}
              </Badge>
            </div>
            
            <AlertDialog open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>
              <AlertDialogTrigger asChild>
                <Button
                  onClick={handleToggleMaintenance}
                  variant={formData.enabled ? 'default' : 'destructive'}
                  className={formData.enabled ? 'bg-green-600 hover:bg-green-700' : 'bg-amber-600 hover:bg-amber-700'}
                >
                  {formData.enabled ? (
                    <>
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Enable System
                    </>
                  ) : (
                    <>
                      <AlertTriangle className="h-4 w-4 mr-2" />
                      Enable Maintenance
                    </>
                  )}
                </Button>
              </AlertDialogTrigger>
              <AlertDialogContent>
                <AlertDialogHeader>
                  <AlertDialogTitle className="flex items-center space-x-2">
                    <AlertTriangle className="h-5 w-5 text-amber-500" />
                    <span>Confirm Maintenance Mode Change</span>
                  </AlertDialogTitle>
                  <AlertDialogDescription>
                    {pendingToggle ? (
                      <>
                        You are about to <strong>enable maintenance mode</strong>. 
                        This will make the website unavailable to regular users. 
                        Only users with admin privileges will be able to access the system.
                      </>
                    ) : (
                      <>
                        You are about to <strong>disable maintenance mode</strong>. 
                        This will make the website available to all users again.
                      </>
                    )}
                  </AlertDialogDescription>
                </AlertDialogHeader>
                <AlertDialogFooter>
                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                  <AlertDialogAction 
                    onClick={confirmToggle}
                    disabled={isSaving}
                    className={pendingToggle ? 'bg-amber-600 hover:bg-amber-700' : 'bg-green-600 hover:bg-green-700'}
                  >
                    {isSaving ? 'Updating...' : 'Confirm'}
                  </AlertDialogAction>
                </AlertDialogFooter>
              </AlertDialogContent>
            </AlertDialog>
          </div>

          {maintenanceConfig && (
            <div className="mt-4 text-sm text-slate-400">
              Last updated by {maintenanceConfig.lastUpdatedBy} on{' '}
              {new Date(maintenanceConfig.lastUpdatedAt).toLocaleString()}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Configuration Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <MessageSquare className="h-5 w-5" />
            <span>Maintenance Configuration</span>
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Maintenance Message */}
          <div className="space-y-2">
            <Label htmlFor="message">Maintenance Message</Label>
            <Textarea
              id="message"
              placeholder="Enter the message to display to users during maintenance..."
              value={formData.message}
              onChange={(e) => setFormData(prev => ({ ...prev, message: e.target.value }))}
              rows={3}
            />
          </div>

          {/* Scheduled Maintenance */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="scheduledStart" className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Calendar className="h-4 w-4" />
                  <span>Scheduled Start (Optional)</span>
                </div>
                {formData.scheduledStart && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => setFormData(prev => ({ ...prev, scheduledStart: '' }))}
                    className="h-6 px-2 text-xs text-slate-500 hover:text-red-600"
                  >
                    Clear
                  </Button>
                )}
              </Label>
              <Input
                id="scheduledStart"
                type="datetime-local"
                value={formatDateTime(formData.scheduledStart)}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  scheduledStart: parseDateTime(e.target.value) 
                }))}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="scheduledEnd" className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Clock className="h-4 w-4" />
                  <span>Scheduled End (Optional)</span>
                </div>
                {formData.scheduledEnd && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => setFormData(prev => ({ ...prev, scheduledEnd: '' }))}
                    className="h-6 px-2 text-xs text-slate-500 hover:text-red-600"
                  >
                    Clear
                  </Button>
                )}
              </Label>
              <Input
                id="scheduledEnd"
                type="datetime-local"
                value={formatDateTime(formData.scheduledEnd)}
                onChange={(e) => setFormData(prev => ({ 
                  ...prev, 
                  scheduledEnd: parseDateTime(e.target.value) 
                }))}
              />
            </div>
          </div>

          {/* Allowed Roles */}
          <div className="space-y-3">
            <Label className="flex items-center space-x-2">
              <Users className="h-4 w-4" />
              <span>Roles Allowed During Maintenance</span>
            </Label>
            <div className="flex flex-wrap gap-2">
              {Object.values(UserRole).map((role) => (
                <div key={role} className="flex items-center space-x-2">
                  <Switch
                    id={`role-${role}`}
                    checked={formData.allowedRoles.includes(role)}
                    onCheckedChange={() => handleRoleToggle(role)}
                  />
                  <Label htmlFor={`role-${role}`} className="capitalize">
                    {getRoleDisplayName(role)}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Save Button */}
          <div className="flex justify-end">
            <Button
              onClick={handleSaveSettings}
              disabled={isSaving}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <Save className="h-4 w-4 mr-2" />
              {isSaving ? 'Saving...' : 'Save Settings'}
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 
"use client";

import { useState, useEffect } from "react";
import { DataIntegrityService, IntegrityReport, IntegrityIssue } from "@/lib/services/admin/data-integrity-service";

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { 
  Shield, 
  AlertTriangle, 
  Info, 
  CheckCircle, 
  XCircle, 
  RefreshCw, 
  MoreVertical,
  Database,
  Link,
  Trash2,
  Edit,
  Merge,
  Plus,
  Eye,
  Loader2,
  TrendingUp,
  TrendingDown,
  Minus
} from "lucide-react";

interface DataIntegrityDashboardProps {
  onClose?: () => void;
}

export function DataIntegrityDashboard({ onClose }: DataIntegrityDashboardProps) {
  const [report, setReport] = useState<IntegrityReport | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedIssue, setSelectedIssue] = useState<IntegrityIssue | null>(null);
  const [isResolving, setIsResolving] = useState<string | null>(null);
  const [resolveDialog, setResolveDialog] = useState<{
    isOpen: boolean;
    issue: IntegrityIssue | null;
    action: any;
  }>({
    isOpen: false,
    issue: null,
    action: null
  });

  useEffect(() => {
    loadIntegrityReport();
  }, []);

  const loadIntegrityReport = async () => {
    setIsLoading(true);
    try {
      const newReport = await DataIntegrityService.analyzeDataIntegrity();
      setReport(newReport);
    } catch (error) {
      console.error('Error loading integrity report:', error);
      alert('Failed to load data integrity report. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleResolveIssue = async (issue: IntegrityIssue, action: any) => {
    setResolveDialog({ isOpen: true, issue, action });
  };

  const confirmResolveIssue = async () => {
    if (!resolveDialog.issue || !resolveDialog.action) return;

    setIsResolving(resolveDialog.issue.id);
    try {
      const result = await DataIntegrityService.autoResolveIssue(
        resolveDialog.issue.id,
        resolveDialog.action.action
      );

      if (result.success) {
        alert(`✅ ${result.message}`);
        await loadIntegrityReport();
      } else {
        alert(`❌ ${result.message}`);
      }
    } catch (error) {
      console.error('Error resolving issue:', error);
      alert('Failed to resolve issue. Please try again.');
    } finally {
      setIsResolving(null);
      setResolveDialog({ isOpen: false, issue: null, action: null });
    }
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'warning':
        return <AlertTriangle className="h-4 w-4 text-yellow-500" />;
      case 'info':
        return <Info className="h-4 w-4 text-blue-500" />;
      default:
        return <Info className="h-4 w-4 text-gray-500" />;
    }
  };

  const getSeverityBadge = (severity: string) => {
    switch (severity) {
      case 'critical':
        return <Badge variant="destructive">Critical</Badge>;
      case 'warning':
        return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Warning</Badge>;
      case 'info':
        return <Badge variant="outline">Info</Badge>;
      default:
        return <Badge variant="outline">Unknown</Badge>;
    }
  };

  const getActionIcon = (action: string) => {
    switch (action) {
      case 'delete_checklist':
        return <Trash2 className="h-4 w-4" />;
      case 'update_reference':
        return <Link className="h-4 w-4" />;
      case 'create_equipment_tag':
        return <Plus className="h-4 w-4" />;
      case 'merge_tags':
        return <Merge className="h-4 w-4" />;
      case 'update_data':
        return <Edit className="h-4 w-4" />;
      default:
        return <Edit className="h-4 w-4" />;
    }
  };

  const getRiskBadge = (risk: string) => {
    switch (risk) {
      case 'high':
        return <Badge variant="destructive" className="text-xs">High Risk</Badge>;
      case 'medium':
        return <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-800">Medium Risk</Badge>;
      case 'low':
        return <Badge variant="outline" className="text-xs">Low Risk</Badge>;
      default:
        return <Badge variant="outline" className="text-xs">Unknown</Badge>;
    }
  };

  if (isLoading && !report) {
    return (
      <Card className="w-full max-w-6xl mx-auto">
        <CardContent className="flex items-center justify-center py-12">
          <div className="flex items-center gap-2">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span>Analyzing data integrity...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!report) {
    return (
      <Card className="w-full max-w-6xl mx-auto">
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <AlertTriangle className="h-12 w-12 text-yellow-500 mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Failed to Load Report</h3>
            <p className="text-muted-foreground mb-4">Unable to analyze data integrity</p>
            <Button onClick={loadIntegrityReport}>Try Again</Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="w-full max-w-7xl mx-auto space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Shield className="h-8 w-8 text-blue-600" />
            Data Integrity Dashboard
          </h1>
          <p className="text-muted-foreground">
            Monitor and resolve data consistency issues between equipment tags and checklists
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Button
            onClick={loadIntegrityReport}
            disabled={isLoading}
            variant="outline"
            size="sm"
          >
            {isLoading ? (
              <Loader2 className="h-4 w-4 animate-spin mr-2" />
            ) : (
              <RefreshCw className="h-4 w-4 mr-2" />
            )}
            Refresh
          </Button>
          {onClose && (
            <Button onClick={onClose} variant="outline" size="sm">
              Close
            </Button>
          )}
        </div>
      </div>

      {/* Summary Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Issues</CardTitle>
            <Database className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{report.summary.totalIssues}</div>
            <p className="text-xs text-muted-foreground">
              {report.summary.autoResolvableIssues} auto-resolvable
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Critical Issues</CardTitle>
            <XCircle className="h-4 w-4 text-red-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-red-600">{report.summary.criticalIssues}</div>
            <p className="text-xs text-muted-foreground">
              Require immediate attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Warning Issues</CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600">{report.summary.warningIssues}</div>
            <p className="text-xs text-muted-foreground">
              Should be addressed
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Data Health</CardTitle>
            <CheckCircle className="h-4 w-4 text-green-500" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {Math.round((report.dataStats.checklistsWithValidReferences / report.dataStats.totalChecklists) * 100)}%
            </div>
            <p className="text-xs text-muted-foreground">
              Valid references
            </p>
          </CardContent>
        </Card>
      </div>

      {/* Data Statistics */}
      <Card>
        <CardHeader>
          <CardTitle>Data Statistics</CardTitle>
          <CardDescription>
            Overview of your equipment tags and checklists data
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold">{report.dataStats.totalEquipmentTags}</div>
              <div className="text-sm text-muted-foreground">Equipment Tags</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold">{report.dataStats.totalChecklists}</div>
              <div className="text-sm text-muted-foreground">Checklists</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{report.dataStats.checklistsWithValidReferences}</div>
              <div className="text-sm text-muted-foreground">Valid References</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-red-600">{report.dataStats.checklistsWithInvalidReferences}</div>
              <div className="text-sm text-muted-foreground">Invalid References</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-yellow-600">{report.dataStats.orphanedChecklists}</div>
              <div className="text-sm text-muted-foreground">Orphaned Checklists</div>
            </div>
            <div className="text-center">
              <div className="text-2xl font-bold text-blue-600">{report.dataStats.unusedEquipmentTags}</div>
              <div className="text-sm text-muted-foreground">Unused Tags</div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Issues Table */}
      <Card>
        <CardHeader>
          <CardTitle>Integrity Issues</CardTitle>
          <CardDescription>
            Detailed list of data integrity issues found in your system
          </CardDescription>
        </CardHeader>
        <CardContent>
          {report.issues.length === 0 ? (
            <div className="text-center py-8">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No Issues Found</h3>
              <p className="text-muted-foreground">
                Your data integrity is excellent! All equipment tags and checklists are properly linked.
              </p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Issue</TableHead>
                    <TableHead>Severity</TableHead>
                    <TableHead>Affected Items</TableHead>
                    <TableHead>Auto-Resolvable</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {report.issues.map((issue) => (
                    <TableRow key={issue.id}>
                      <TableCell>
                        <div className="space-y-1">
                          <div className="flex items-center gap-2">
                            {getSeverityIcon(issue.severity)}
                            <span className="font-medium">{issue.title}</span>
                          </div>
                          <p className="text-sm text-muted-foreground">{issue.description}</p>
                        </div>
                      </TableCell>
                      <TableCell>
                        {getSeverityBadge(issue.severity)}
                      </TableCell>
                      <TableCell>
                        <div className="space-y-1 text-sm">
                          {issue.affectedItems.checklistId && (
                            <div>Checklist: {issue.affectedItems.checklistId}</div>
                          )}
                          {issue.affectedItems.equipmentTagId && (
                            <div>Equipment: {issue.affectedItems.equipmentTagId}</div>
                          )}
                          {issue.affectedItems.tagNumber && (
                            <div>Tag: {issue.affectedItems.tagNumber}</div>
                          )}
                          {issue.affectedItems.clientName && (
                            <div>Client: {issue.affectedItems.clientName}</div>
                          )}
                          {issue.affectedItems.count && (
                            <div>Count: {issue.affectedItems.count}</div>
                          )}
                        </div>
                      </TableCell>
                      <TableCell>
                        {issue.autoResolvable ? (
                          <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Yes
                          </Badge>
                        ) : (
                          <Badge variant="outline" className="bg-gray-50 text-gray-700">
                            <Minus className="h-3 w-3 mr-1" />
                            Manual
                          </Badge>
                        )}
                      </TableCell>
                      <TableCell className="text-right">
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button 
                              variant="ghost" 
                              size="sm" 
                              className="h-8 w-8 p-0"
                              disabled={isResolving === issue.id}
                            >
                              {isResolving === issue.id ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <MoreVertical className="h-4 w-4" />
                              )}
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end" className="w-56">
                            {issue.suggestedActions.map((action, index) => (
                              <DropdownMenuItem
                                key={index}
                                onClick={() => handleResolveIssue(issue, action)}
                                className="flex items-center gap-2"
                                disabled={!issue.autoResolvable && action.risk === 'high'}
                              >
                                {getActionIcon(action.action)}
                                <div className="flex-1">
                                  <div className="font-medium">{action.description}</div>
                                  <div className="flex items-center gap-2 mt-1">
                                    {getRiskBadge(action.risk)}
                                  </div>
                                </div>
                              </DropdownMenuItem>
                            ))}
                            <DropdownMenuItem
                              onClick={() => setSelectedIssue(issue)}
                              className="flex items-center gap-2"
                            >
                              <Eye className="h-4 w-4" />
                              View Details
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Last Updated */}
      <div className="text-center text-sm text-muted-foreground">
        Last checked: {new Date(report.lastChecked).toLocaleString()}
      </div>

      {/* Resolve Issue Dialog */}
      <AlertDialog 
        open={resolveDialog.isOpen} 
        onOpenChange={(open) => !open && setResolveDialog({ isOpen: false, issue: null, action: null })}
      >
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Resolve Data Integrity Issue</AlertDialogTitle>
            <AlertDialogDescription>
              {resolveDialog.issue && resolveDialog.action && (
                <div className="space-y-3">
                  <div>
                    <strong>Issue:</strong> {resolveDialog.issue.title}
                  </div>
                  <div>
                    <strong>Action:</strong> {resolveDialog.action.description}
                  </div>
                  <div className="flex items-center gap-2">
                    <strong>Risk Level:</strong> {getRiskBadge(resolveDialog.action.risk)}
                  </div>
                  {resolveDialog.action.risk === 'high' && (
                    <Alert variant="destructive">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription>
                        <strong>Warning:</strong> This action may result in data loss. Please ensure you have backups before proceeding.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
              )}
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel disabled={isResolving !== null}>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmResolveIssue}
              disabled={isResolving !== null}
              className={resolveDialog.action?.risk === 'high' ? 'bg-red-600 hover:bg-red-700' : ''}
            >
              {isResolving !== null ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Resolving...
                </>
              ) : (
                'Resolve Issue'
              )}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  );
} 
'use client';

import React, { useEffect, useState, useRef } from 'react';
import { useAuth } from './auth';
import { StorageService } from '@/lib/services/storage/storage-adapter';
import { log } from '@/lib/utils/logger';

interface AppInitializationProps {
  children: React.ReactNode;
}



export function AppInitialization({ children }: AppInitializationProps) {
  const { user, loading: authLoading } = useAuth();
  const [storageInitialized, setStorageInitialized] = useState(false);
  const [initializationError, setInitializationError] = useState<string | null>(null);
  const initializationRef = useRef<Promise<void> | null>(null);

  // Debug logging
  console.log('AppInitialization render:', {
    user: user?.uid,
    authLoading,
    storageInitialized,
    initializationError,
    serviceInitialized: StorageService.isInitialized()
  });

  // Simplified initialization logic
  useEffect(() => {
    console.log('AppInitialization useEffect:', { 
      authLoading, 
      user: user?.uid,
      serviceInitialized: StorageService.isInitialized(),
      currentInit: !!initializationRef.current
    });
    
    // Don't do anything if auth is still loading
    if (authLoading) {
      console.log('Auth still loading, waiting...');
      return;
    }

    // If no user, reset state
    if (!user) {
      console.log('No user, resetting state');
      setStorageInitialized(false);
      setInitializationError(null);
      initializationRef.current = null;
      return;
    }

    // If already initialized for this user, we're done
    if (StorageService.isInitialized()) {
      console.log('Storage service already initialized');
      setStorageInitialized(true);
      setInitializationError(null);
      return;
    }

    // If initialization is already in progress, don't start another
    if (initializationRef.current) {
      console.log('Initialization already in progress');
      return;
    }

    // Start initialization
    console.log('Starting storage initialization for user:', user.uid);
    const initPromise = initializeStorageService(user.uid);
    initializationRef.current = initPromise;

    initPromise
      .then(() => {
        console.log('Storage initialization completed successfully');
        setStorageInitialized(true);
        setInitializationError(null);
      })
      .catch((error) => {
        console.error('Storage initialization failed:', error);
        setInitializationError(error instanceof Error ? error.message : 'Unknown error');
        setStorageInitialized(false);
      })
      .finally(() => {
        initializationRef.current = null;
      });
  }, [user, authLoading]);

  const initializeStorageService = async (userId: string): Promise<void> => {
    try {
      log.info('Initializing storage service', 'APP_INIT', { userId });
      await StorageService.initialize(userId);
      log.info('Storage service initialized successfully', 'APP_INIT', { userId });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown initialization error';
      log.error('Failed to initialize storage service', 'APP_INIT', {
        userId,
        error: errorMessage
      });
      throw error;
    }
  };

  // For unauthenticated users, don't show any loading - just render content
  if (!user && !authLoading) {
    return <>{children}</>;
  }

  // Show loading during auth check or storage initialization
  const isInitializing = !storageInitialized && !initializationError && user && !authLoading;
  
  if (authLoading || isInitializing) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
          <p className="text-sm text-muted-foreground">
            {authLoading ? 'Authenticating...' : 'Initializing storage...'}
          </p>
        </div>
      </div>
    );
  }

  // Show error if initialization failed
  if (initializationError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4 max-w-md mx-auto p-6">
          <div className="text-red-500 text-lg font-semibold">Initialization Failed</div>
          <p className="text-sm text-muted-foreground">{initializationError}</p>
          <button 
            onClick={() => {
              setInitializationError(null);
              setStorageInitialized(false);
              // Trigger re-initialization by setting user dependency
              if (user) {
                window.location.reload();
              }
            }}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            Retry
          </button>
        </div>
      </div>
    );
  }

  return <>{children}</>;
} 
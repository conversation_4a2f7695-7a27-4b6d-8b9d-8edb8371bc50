"use client";

import { useState, useRef, useCallback, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Camera, RotateCcw, Check, AlertTriangle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Image from "next/image";

interface CameraCaptureProps {
  isOpen: boolean;
  onClose: () => void;
  onCapture: (file: File) => void;
  title?: string;
  description?: string;
}

export function CameraCapture({ 
  isOpen, 
  onClose, 
  onCapture, 
  title = "Take Photo",
  description = "Position your camera and tap capture to take a photo"
}: CameraCaptureProps) {
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [facingMode, setFacingMode] = useState<'user' | 'environment'>('environment');
  const [debugInfo, setDebugInfo] = useState<{
    videoWidth?: number;
    videoHeight?: number;
    streamActive?: boolean;
    userAgent?: string;
  }>({});
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Start camera stream
  const startCamera = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      // Stop existing stream if any
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }

      let mediaStream: MediaStream | null = null;

      // Try with ideal constraints first
      try {
        const constraints = {
          video: {
            facingMode: facingMode,
            // Use more conservative resolution constraints for mobile compatibility
            width: { ideal: 1280, max: 1920 },
            height: { ideal: 720, max: 1080 },
            // Add frame rate constraint for better performance on mobile
            frameRate: { ideal: 30, max: 30 }
          },
          audio: false
        };

        mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
      } catch (constraintError) {
        console.warn('Initial constraints failed, trying fallback:', constraintError);
        
        // Fallback to basic constraints for problematic Android devices
        try {
          const fallbackConstraints = {
            video: {
              facingMode: facingMode
            },
            audio: false
          };
          
          mediaStream = await navigator.mediaDevices.getUserMedia(fallbackConstraints);
        } catch (fallbackError) {
          console.warn('Fallback constraints failed, trying minimal:', fallbackError);
          
          // Last resort: minimal constraints
          const minimalConstraints = {
            video: true,
            audio: false
          };
          
          mediaStream = await navigator.mediaDevices.getUserMedia(minimalConstraints);
        }
      }

      if (!mediaStream) {
        throw new Error('Failed to get media stream');
      }

      setStream(mediaStream);
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;
        
        // Add event listener for when video metadata is loaded (important for Android)
        const video = videoRef.current;
        
        const handleLoadedMetadata = () => {
          console.log('Video metadata loaded, dimensions:', video.videoWidth, 'x', video.videoHeight);
          setDebugInfo(prev => ({
            ...prev,
            videoWidth: video.videoWidth,
            videoHeight: video.videoHeight,
            streamActive: true,
            userAgent: navigator.userAgent
          }));
        };
        
        video.addEventListener('loadedmetadata', handleLoadedMetadata);
        
        // Explicitly call play() for Android compatibility
        try {
          await videoRef.current.play();
        } catch (playError) {
          console.warn('Video play() failed, but stream may still work:', playError);
          // Don't throw here as the stream might still work
        }
      }
    } catch (err) {
      console.error('Camera access error:', err);
      setError(
        err instanceof Error 
          ? err.message.includes('Permission denied') || err.message.includes('NotAllowedError')
            ? 'Camera permission denied. Please allow camera access and try again.'
            : err.message.includes('NotFoundError') || err.message.includes('DeviceNotFoundError')
            ? 'No camera found on this device.'
            : err.message.includes('OverconstrainedError')
            ? 'Camera constraints not supported. Try switching cameras.'
            : `Camera error: ${err.message}`
          : 'Failed to access camera. Please check your device permissions.'
      );
    } finally {
      setIsLoading(false);
    }
  }, [facingMode, stream]);

  // Stop camera stream
  const stopCamera = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
  }, [stream]);

  // Capture photo
  const capturePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');
    
    if (!context) return;

    // Check if video has valid dimensions (important for Android)
    const videoWidth = video.videoWidth || video.clientWidth || 640;
    const videoHeight = video.videoHeight || video.clientHeight || 480;
    
    if (videoWidth === 0 || videoHeight === 0) {
      console.warn('Video dimensions are 0, using fallback dimensions');
      setError('Camera not ready. Please wait a moment and try again.');
      return;
    }

    console.log('Capturing photo with dimensions:', videoWidth, 'x', videoHeight);

    // Set canvas dimensions to match video
    canvas.width = videoWidth;
    canvas.height = videoHeight;

    // Draw the video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Convert canvas to blob and create file
    canvas.toBlob((blob) => {
      if (blob) {
        const imageUrl = canvas.toDataURL('image/jpeg', 0.8);
        setCapturedImage(imageUrl);
        
        // Stop the camera stream after capture
        stopCamera();
      }
    }, 'image/jpeg', 0.8);
  }, [stopCamera]);

  // Handle dialog close
  const handleClose = useCallback(() => {
    stopCamera();
    setCapturedImage(null);
    setError(null);
    onClose();
  }, [stopCamera, onClose]);

  // Confirm and use captured image
  const confirmCapture = useCallback(() => {
    if (!capturedImage || !canvasRef.current) return;

    canvasRef.current.toBlob((blob) => {
      if (blob) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const file = new File([blob], `camera-capture-${timestamp}.jpg`, {
          type: 'image/jpeg',
          lastModified: Date.now()
        });
        
        onCapture(file);
        handleClose();
      }
    }, 'image/jpeg', 0.8);
  }, [capturedImage, onCapture, handleClose]);

  // Retake photo
  const retakePhoto = useCallback(() => {
    setCapturedImage(null);
    startCamera();
  }, [startCamera]);

  // Switch camera facing mode
  const switchCamera = useCallback(() => {
    setFacingMode(current => current === 'user' ? 'environment' : 'user');
  }, []);

  // Force refresh camera (for Android troubleshooting)
  const forceRefreshCamera = useCallback(async () => {
    console.log('Force refreshing camera...');
    setDebugInfo({});
    stopCamera();
    // Wait a bit before restarting
    setTimeout(() => {
      startCamera();
    }, 500);
  }, [stopCamera, startCamera]);

  // Start camera when dialog opens
  useEffect(() => {
    if (isOpen && !capturedImage) {
      startCamera();
    }
  }, [isOpen, capturedImage, startCamera]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, [stopCamera]);

  // Check if camera is supported
  const isCameraSupported = typeof navigator !== 'undefined' && 
    navigator.mediaDevices && 
    navigator.mediaDevices.getUserMedia;

  if (!isCameraSupported) {
    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Camera className="h-5 w-5" />
              {title}
            </DialogTitle>
            <DialogDescription>
              Camera functionality is not available on this device or browser. Please use a different device or try uploading an image file instead.
            </DialogDescription>
          </DialogHeader>
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              Camera is not supported on this device or browser.
            </AlertDescription>
          </Alert>
          <div className="flex justify-end">
            <Button variant="outline" onClick={handleClose}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Camera className="h-5 w-5" />
            {title}
          </DialogTitle>
          <DialogDescription>
            {description}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Camera Preview or Captured Image */}
          <Card>
            <CardContent className="p-0">
              <div className="relative aspect-[4/3] w-full overflow-hidden rounded-lg bg-black">
                {capturedImage ? (
                  // Show captured image
                  <Image 
                    src={capturedImage} 
                    alt="Captured photo"
                    fill
                    className="object-cover"
                  />
                ) : (
                  // Show camera preview
                  <div className="relative w-full h-full">
                    <video
                      ref={videoRef}
                      className="w-full h-full object-cover"
                      playsInline
                      autoPlay
                      muted
                    />
                    {isLoading && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <div className="text-center text-white">
                          <Camera className="h-8 w-8 animate-pulse mx-auto mb-2" />
                          <p className="text-sm">Starting camera...</p>
                        </div>
                      </div>
                    )}
                  </div>
                )}
                
                {/* Hidden canvas for capturing */}
                <canvas ref={canvasRef} className="hidden" />
              </div>
            </CardContent>
          </Card>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                {error}
              </AlertDescription>
            </Alert>
          )}

          {/* Debug Information (for troubleshooting) */}
          {(debugInfo.videoWidth || debugInfo.streamActive !== undefined) && (
            <Alert>
              <AlertDescription className="text-xs">
                <strong>Debug Info:</strong><br/>
                Video: {debugInfo.videoWidth || 0}x{debugInfo.videoHeight || 0}<br/>
                Stream: {debugInfo.streamActive ? 'Active' : 'Inactive'}<br/>
                Device: {debugInfo.userAgent?.includes('Android') ? 'Android' : 
                        debugInfo.userAgent?.includes('iPhone') ? 'iOS' : 'Other'}
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 justify-between">
            {capturedImage ? (
              // Captured image actions
              <>
                <Button variant="outline" onClick={retakePhoto}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Retake
                </Button>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={handleClose}>
                    Cancel
                  </Button>
                  <Button onClick={confirmCapture}>
                    <Check className="h-4 w-4 mr-2" />
                    Use Photo
                  </Button>
                </div>
              </>
            ) : (
              // Camera preview actions
              <>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={switchCamera}
                    disabled={isLoading || !!error}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                  {error && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={startCamera}
                    >
                      Retry
                    </Button>
                  )}
                  {/* Force refresh button for Android troubleshooting */}
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={forceRefreshCamera}
                    disabled={isLoading}
                    title="Force refresh camera (for troubleshooting)"
                  >
                    🔄
                  </Button>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={handleClose}>
                    Cancel
                  </Button>
                  <Button 
                    onClick={capturePhoto}
                    disabled={isLoading || !!error || !stream}
                  >
                    <Camera className="h-4 w-4 mr-2" />
                    Capture
                  </Button>
                </div>
              </>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 
"use client";

import { useState, useRef, useCallback, useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogDescription } from "@/components/ui/dialog";
import { Camera, RotateCcw, Check, AlertTriangle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Image from "next/image";

interface CameraCaptureProps {
  isOpen: boolean;
  onClose: () => void;
  onCapture: (file: File) => void;
  title?: string;
  description?: string;
}

export function CameraCapture({ 
  isOpen, 
  onClose, 
  onCapture, 
  title = "Take Photo",
  description = "Position your camera and tap capture to take a photo"
}: CameraCaptureProps) {
  const [stream, setStream] = useState<MediaStream | null>(null);
  const [capturedImage, setCapturedImage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [facingMode, setFacingMode] = useState<'user' | 'environment'>('environment');
  const [debugInfo, setDebugInfo] = useState<{
    videoWidth?: number;
    videoHeight?: number;
    streamActive?: boolean;
    userAgent?: string;
  }>({});
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);

  // Start camera stream
  const startCamera = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    try {
      // Check if camera is available
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera API not supported in this browser');
      }

      // Stop existing stream if any
      if (stream) {
        stream.getTracks().forEach(track => track.stop());
      }

      let mediaStream: MediaStream | null = null;

      // Check available devices first
      try {
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(device => device.kind === 'videoinput');

        if (videoDevices.length === 0) {
          throw new Error('No camera devices found');
        }

        console.log('Available video devices:', videoDevices.length);
      } catch (deviceError) {
        console.warn('Could not enumerate devices:', deviceError);
        // Continue anyway, as some browsers restrict device enumeration
      }

      // Try with ideal constraints first
      try {
        const constraints = {
          video: {
            facingMode: facingMode,
            // Use more conservative resolution constraints for mobile compatibility
            width: { ideal: 1280, max: 1920 },
            height: { ideal: 720, max: 1080 },
            // Add frame rate constraint for better performance on mobile
            frameRate: { ideal: 30, max: 30 }
          },
          audio: false
        };

        mediaStream = await navigator.mediaDevices.getUserMedia(constraints);
      } catch (constraintError) {
        console.warn('Initial constraints failed, trying fallback:', constraintError);

        // Fallback to basic constraints for problematic Android devices
        try {
          const fallbackConstraints = {
            video: {
              facingMode: facingMode
            },
            audio: false
          };

          mediaStream = await navigator.mediaDevices.getUserMedia(fallbackConstraints);
        } catch (fallbackError) {
          console.warn('Fallback constraints failed, trying minimal:', fallbackError);

          // Last resort: minimal constraints
          const minimalConstraints = {
            video: true,
            audio: false
          };

          mediaStream = await navigator.mediaDevices.getUserMedia(minimalConstraints);
        }
      }

      if (!mediaStream) {
        throw new Error('Failed to get media stream');
      }

      setStream(mediaStream);
      
      if (videoRef.current) {
        videoRef.current.srcObject = mediaStream;

        // Add event listener for when video metadata is loaded (important for Android)
        const video = videoRef.current;

        const handleLoadedMetadata = () => {
          console.log('Video metadata loaded, dimensions:', video.videoWidth, 'x', video.videoHeight);
          setDebugInfo(prev => ({
            ...prev,
            videoWidth: video.videoWidth,
            videoHeight: video.videoHeight,
            streamActive: true,
            userAgent: navigator.userAgent
          }));
        };

        const handleCanPlay = () => {
          console.log('Video can play');
          setIsLoading(false);
        };

        video.addEventListener('loadedmetadata', handleLoadedMetadata);
        video.addEventListener('canplay', handleCanPlay);

        // Set video attributes for better mobile compatibility
        video.setAttribute('playsinline', 'true');
        video.setAttribute('webkit-playsinline', 'true');
        video.muted = true;
        video.autoplay = true;

        // Explicitly call play() for Android compatibility
        try {
          const playPromise = video.play();
          if (playPromise !== undefined) {
            await playPromise;
          }
        } catch (playError) {
          console.warn('Video play() failed, but stream may still work:', playError);
          // Don't throw here as the stream might still work
        }

        // Cleanup event listeners
        return () => {
          video.removeEventListener('loadedmetadata', handleLoadedMetadata);
          video.removeEventListener('canplay', handleCanPlay);
        };
      }
    } catch (err) {
      console.error('Camera access error:', err);

      // Enhanced error handling with specific mobile browser guidance
      let errorMessage = 'Failed to access camera. Please check your device permissions.';

      if (err instanceof Error) {
        const errorName = (err as any).name || '';
        const errorMessage_base = err.message.toLowerCase();

        if (errorMessage_base.includes('permission denied') || errorName === 'NotAllowedError') {
          errorMessage = 'Camera permission denied. Please allow camera access in your browser settings and refresh the page.';
        } else if (errorMessage_base.includes('not found') || errorName === 'NotFoundError' || errorName === 'DeviceNotFoundError') {
          errorMessage = 'No camera found on this device. Please ensure your device has a camera and try again.';
        } else if (errorMessage_base.includes('overconstrained') || errorName === 'OverconstrainedError') {
          errorMessage = 'Camera constraints not supported. Try switching cameras or use a different device.';
        } else if (errorMessage_base.includes('not supported') || errorMessage_base.includes('not available')) {
          errorMessage = 'Camera API not supported in this browser. Please use Chrome, Safari, or Firefox.';
        } else if (errorMessage_base.includes('secure context') || errorMessage_base.includes('https')) {
          errorMessage = 'Camera access requires a secure connection (HTTPS). Please access the site via HTTPS.';
        } else {
          errorMessage = `Camera error: ${err.message}. Please try refreshing the page or using a different browser.`;
        }
      }

      // Add mobile-specific guidance
      const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
      if (isMobile) {
        errorMessage += ' On mobile devices, ensure you\'re using the latest browser version and have granted camera permissions.';
      }

      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [facingMode, stream]);

  // Stop camera stream
  const stopCamera = useCallback(() => {
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
      setStream(null);
    }
    if (videoRef.current) {
      videoRef.current.srcObject = null;
    }
  }, [stream]);

  // Capture photo
  const capturePhoto = useCallback(() => {
    if (!videoRef.current || !canvasRef.current) return;

    const video = videoRef.current;
    const canvas = canvasRef.current;
    const context = canvas.getContext('2d');
    
    if (!context) return;

    // Check if video has valid dimensions (important for Android)
    const videoWidth = video.videoWidth || video.clientWidth || 640;
    const videoHeight = video.videoHeight || video.clientHeight || 480;
    
    if (videoWidth === 0 || videoHeight === 0) {
      console.warn('Video dimensions are 0, using fallback dimensions');
      setError('Camera not ready. Please wait a moment and try again.');
      return;
    }

    console.log('Capturing photo with dimensions:', videoWidth, 'x', videoHeight);

    // Set canvas dimensions to match video
    canvas.width = videoWidth;
    canvas.height = videoHeight;

    // Draw the video frame to canvas
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // Convert canvas to blob and create file
    canvas.toBlob((blob) => {
      if (blob) {
        const imageUrl = canvas.toDataURL('image/jpeg', 0.8);
        setCapturedImage(imageUrl);
        
        // Stop the camera stream after capture
        stopCamera();
      }
    }, 'image/jpeg', 0.8);
  }, [stopCamera]);

  // Handle dialog close
  const handleClose = useCallback(() => {
    stopCamera();
    setCapturedImage(null);
    setError(null);
    onClose();
  }, [stopCamera, onClose]);

  // Confirm and use captured image
  const confirmCapture = useCallback(() => {
    if (!capturedImage || !canvasRef.current) return;

    canvasRef.current.toBlob((blob) => {
      if (blob) {
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const file = new File([blob], `camera-capture-${timestamp}.jpg`, {
          type: 'image/jpeg',
          lastModified: Date.now()
        });
        
        onCapture(file);
        handleClose();
      }
    }, 'image/jpeg', 0.8);
  }, [capturedImage, onCapture, handleClose]);

  // Retake photo
  const retakePhoto = useCallback(() => {
    setCapturedImage(null);
    startCamera();
  }, [startCamera]);

  // Switch camera facing mode
  const switchCamera = useCallback(() => {
    setFacingMode(current => current === 'user' ? 'environment' : 'user');
  }, []);

  // Force refresh camera (for Android troubleshooting)
  const forceRefreshCamera = useCallback(async () => {
    console.log('Force refreshing camera...');
    setDebugInfo({});
    stopCamera();
    // Wait a bit before restarting
    setTimeout(() => {
      startCamera();
    }, 500);
  }, [stopCamera, startCamera]);

  // Start camera when dialog opens
  useEffect(() => {
    if (isOpen && !capturedImage) {
      startCamera();
    }
  }, [isOpen, capturedImage, startCamera]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopCamera();
    };
  }, [stopCamera]);

  // Enhanced camera support detection
  const isCameraSupported = typeof navigator !== 'undefined' &&
    navigator.mediaDevices &&
    navigator.mediaDevices.getUserMedia &&
    window.isSecureContext; // Camera requires secure context (HTTPS)

  // Check camera permission status
  const checkCameraPermission = useCallback(async () => {
    if (!navigator.permissions) return 'unknown';

    try {
      const permission = await navigator.permissions.query({ name: 'camera' as PermissionName });
      return permission.state;
    } catch (error) {
      console.warn('Could not check camera permission:', error);
      return 'unknown';
    }
  }, []);

  // Request camera permission explicitly
  const requestCameraPermission = useCallback(async () => {
    try {
      const stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });
      stream.getTracks().forEach(track => track.stop());
      return true;
    } catch (error) {
      console.error('Camera permission request failed:', error);
      return false;
    }
  }, []);

  if (!isCameraSupported) {
    const isHttps = window.location.protocol === 'https:';
    const isLocalhost = window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1';

    return (
      <Dialog open={isOpen} onOpenChange={handleClose}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Camera className="h-5 w-5" />
              {title}
            </DialogTitle>
            <DialogDescription>
              Camera functionality is not available. Please check the requirements below.
            </DialogDescription>
          </DialogHeader>
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="space-y-2">
              <div>Camera access requires:</div>
              <ul className="list-disc list-inside text-sm space-y-1">
                <li>A device with a camera</li>
                <li>A modern browser (Chrome, Safari, Firefox, Edge)</li>
                <li>HTTPS connection {!isHttps && !isLocalhost && '(currently missing)'}</li>
                <li>Camera permissions granted</li>
              </ul>
              {!isHttps && !isLocalhost && (
                <div className="mt-2 p-2 bg-yellow-50 rounded text-sm">
                  <strong>Security Issue:</strong> Camera access requires HTTPS. Please access this site via a secure connection.
                </div>
              )}
            </AlertDescription>
          </Alert>
          <div className="flex justify-end">
            <Button variant="outline" onClick={handleClose}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-lg">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Camera className="h-5 w-5" />
            {title}
          </DialogTitle>
          <DialogDescription>
            {description}
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4">
          {/* Camera Preview or Captured Image */}
          <Card>
            <CardContent className="p-0">
              <div className="relative aspect-[4/3] w-full overflow-hidden rounded-lg bg-black">
                {capturedImage ? (
                  // Show captured image
                  <Image 
                    src={capturedImage} 
                    alt="Captured photo"
                    fill
                    className="object-cover"
                  />
                ) : (
                  // Show camera preview
                  <div className="relative w-full h-full">
                    <video
                      ref={videoRef}
                      className="w-full h-full object-cover"
                      playsInline
                      autoPlay
                      muted
                      controls={false}
                      webkit-playsinline="true"
                      x-webkit-airplay="deny"
                      disablePictureInPicture
                      style={{
                        transform: facingMode === 'user' ? 'scaleX(-1)' : 'none',
                        backgroundColor: '#000'
                      }}
                    />
                    {isLoading && (
                      <div className="absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center">
                        <div className="text-center text-white">
                          <Camera className="h-8 w-8 animate-pulse mx-auto mb-2" />
                          <p className="text-sm">Starting camera...</p>
                        </div>
                      </div>
                    )}
                  </div>
                )}
                
                {/* Hidden canvas for capturing */}
                <canvas ref={canvasRef} className="hidden" />
              </div>
            </CardContent>
          </Card>

          {/* Error Display */}
          {error && (
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="space-y-2">
                <div>{error}</div>
                {error.includes('permission') && (
                  <div className="flex gap-2 mt-2">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={async () => {
                        const granted = await requestCameraPermission();
                        if (granted) {
                          setError(null);
                          startCamera();
                        }
                      }}
                    >
                      Request Permission
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => {
                        setError(null);
                        startCamera();
                      }}
                    >
                      Try Again
                    </Button>
                  </div>
                )}
                {error.includes('https') && (
                  <div className="text-sm text-yellow-600 bg-yellow-50 p-2 rounded">
                    <strong>Tip:</strong> Make sure you're accessing this site via HTTPS (secure connection) for camera access to work.
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}

          {/* Debug Information (for troubleshooting) */}
          {(debugInfo.videoWidth || debugInfo.streamActive !== undefined) && (
            <Alert>
              <AlertDescription className="text-xs">
                <strong>Debug Info:</strong><br/>
                Video: {debugInfo.videoWidth || 0}x{debugInfo.videoHeight || 0}<br/>
                Stream: {debugInfo.streamActive ? 'Active' : 'Inactive'}<br/>
                Device: {debugInfo.userAgent?.includes('Android') ? 'Android' : 
                        debugInfo.userAgent?.includes('iPhone') ? 'iOS' : 'Other'}
              </AlertDescription>
            </Alert>
          )}

          {/* Action Buttons */}
          <div className="flex gap-2 justify-between">
            {capturedImage ? (
              // Captured image actions
              <>
                <Button variant="outline" onClick={retakePhoto}>
                  <RotateCcw className="h-4 w-4 mr-2" />
                  Retake
                </Button>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={handleClose}>
                    Cancel
                  </Button>
                  <Button onClick={confirmCapture}>
                    <Check className="h-4 w-4 mr-2" />
                    Use Photo
                  </Button>
                </div>
              </>
            ) : (
              // Camera preview actions
              <>
                <div className="flex gap-2">
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={switchCamera}
                    disabled={isLoading || !!error}
                  >
                    <RotateCcw className="h-4 w-4" />
                  </Button>
                  {error && (
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={startCamera}
                    >
                      Retry
                    </Button>
                  )}
                  {/* Force refresh button for Android troubleshooting */}
                  <Button 
                    variant="outline" 
                    size="sm"
                    onClick={forceRefreshCamera}
                    disabled={isLoading}
                    title="Force refresh camera (for troubleshooting)"
                  >
                    🔄
                  </Button>
                </div>
                <div className="flex gap-2">
                  <Button variant="outline" onClick={handleClose}>
                    Cancel
                  </Button>
                  <Button 
                    onClick={capturePhoto}
                    disabled={isLoading || !!error || !stream}
                  >
                    <Camera className="h-4 w-4 mr-2" />
                    Capture
                  </Button>
                </div>
              </>
            )}
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
} 
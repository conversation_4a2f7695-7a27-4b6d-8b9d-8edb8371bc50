"use client";

import { ChecklistData } from "@/types/checklist";
import { calculateChecklistSummary } from "@/lib/services/storage/firestore-storage";
import { exportToExcel, exportToPDF } from "@/lib/services/export/export";
import { FirebaseStorageService } from "@/lib/services/storage/firebase-storage-service";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableRow } from "@/components/ui/table";
import {
  CheckCircle,
  XCircle,
  MinusCircle,
  AlertCircle,
  FileSpreadsheet,
  FileText,
  Building,
  MapPin,
  Calendar,
  User,
  Tag,
  UserCheck,
  Hash,
  Settings,
  Loader2,
  ImageIcon,
  RefreshCw,
} from "lucide-react";
import { useState } from "react";
import Image from "next/image";
import { getImageProps } from "@/lib/utils/image-utils";

interface SummaryViewProps {
  checklist: ChecklistData;
}

export function SummaryView({ checklist }: SummaryViewProps) {
  const summary = calculateChecklistSummary(checklist);
  const [exportingStates, setExportingStates] = useState({
    pdf: false,
    excel: false,
  });

  const handleExport = async (type: 'excel' | 'pdf') => {
    setExportingStates(prev => ({ ...prev, [type]: true }));
    try {
      if (type === 'excel') {
        await exportToExcel(checklist);
      } else {
        await exportToPDF(checklist);
      }
    } catch (error) {
      console.error(`Export ${type} failed:`, error);
      alert(`Failed to export ${type.toUpperCase()} file. Please try again.`);
    } finally {
      setExportingStates(prev => ({ ...prev, [type]: false }));
    }
  };

  const isAnyExporting = Object.values(exportingStates).some(Boolean);

  return (
    <div className="space-y-6">
      {/* Summary Cards */}
      <div className="grid grid-cols-2 lg:grid-cols-4 gap-4 lg:gap-6">
        <Card className="group hover:shadow-lg hover:shadow-primary/5 transition-all duration-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Total OK</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <CheckCircle className="h-5 w-5 text-green-500" />
              <span className="text-2xl font-bold">{summary.totalOk}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg hover:shadow-primary/5 transition-all duration-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Faulty</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <XCircle className="h-5 w-5 text-red-500" />
              <span className="text-2xl font-bold">{summary.totalFaulty}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg hover:shadow-primary/5 transition-all duration-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Not Applicable</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <MinusCircle className="h-5 w-5 text-yellow-500" />
              <span className="text-2xl font-bold">{summary.totalNA}</span>
            </div>
          </CardContent>
        </Card>

        <Card className="group hover:shadow-lg hover:shadow-primary/5 transition-all duration-200">
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">Missing</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <AlertCircle className="h-5 w-5 text-orange-500" />
              <span className="text-2xl font-bold">{summary.totalMissing}</span>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* General Information */}
      <Card className="group hover:shadow-lg hover:shadow-primary/5 transition-all duration-200">
        <CardHeader>
          <CardTitle className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
            General Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          {/* Mobile-friendly layout - Hidden on desktop */}
          <div className="block lg:hidden space-y-4">
            <div className="grid grid-cols-1 gap-4">
              <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30">
                <Building className="h-5 w-5 text-primary" />
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">Client Name</div>
                  <div className="font-medium">{checklist.generalInfo.clientName}</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30">
                <Building className="h-5 w-5 text-primary" />
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">Building</div>
                  <div className="font-medium">{checklist.generalInfo.building}</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30">
                <User className="h-5 w-5 text-primary" />
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">Inspected By</div>
                  <div className="font-medium">{checklist.generalInfo.inspectedBy}</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30">
                <UserCheck className="h-5 w-5 text-primary" />
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">Approved By</div>
                  <div className="font-medium">{checklist.generalInfo.approvedBy}</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30">
                <Calendar className="h-5 w-5 text-primary" />
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">Date</div>
                  <div className="font-medium">{checklist.generalInfo.date}</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30">
                <Hash className="h-5 w-5 text-primary" />
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">PPM Attempt</div>
                  <div className="font-medium">{checklist.generalInfo.ppmAttempt}</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30">
                <Settings className="h-5 w-5 text-primary" />
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">Equipment Name</div>
                  <div className="font-medium">{checklist.generalInfo.equipmentName}</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30">
                <MapPin className="h-5 w-5 text-primary" />
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">Location</div>
                  <div className="font-medium">{checklist.generalInfo.location}</div>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 rounded-lg bg-muted/30">
                <Tag className="h-5 w-5 text-primary" />
                <div className="space-y-1">
                  <div className="text-sm text-muted-foreground">Tag No</div>
                  <div className="font-medium">{checklist.generalInfo.tagNo}</div>
                </div>
              </div>
            </div>
          </div>

          {/* Desktop table layout - Hidden on mobile */}
          <div className="hidden lg:block">
            <Table>
              <TableBody>
                <TableRow>
                  <TableCell className="font-medium">Client Name</TableCell>
                  <TableCell>{checklist.generalInfo.clientName}</TableCell>
                  <TableCell className="font-medium">Building</TableCell>
                  <TableCell>{checklist.generalInfo.building}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Inspected By</TableCell>
                  <TableCell>{checklist.generalInfo.inspectedBy}</TableCell>
                  <TableCell className="font-medium">Approved By</TableCell>
                  <TableCell>{checklist.generalInfo.approvedBy}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Date</TableCell>
                  <TableCell>{checklist.generalInfo.date}</TableCell>
                  <TableCell className="font-medium">PPM Attempt</TableCell>
                  <TableCell>{checklist.generalInfo.ppmAttempt}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Equipment Name</TableCell>
                  <TableCell>{checklist.generalInfo.equipmentName}</TableCell>
                  <TableCell className="font-medium">Location</TableCell>
                  <TableCell>{checklist.generalInfo.location}</TableCell>
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Tag No</TableCell>
                  <TableCell colSpan={3}>{checklist.generalInfo.tagNo}</TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Export Options */}
      <Card className="group hover:shadow-lg hover:shadow-primary/5 transition-all duration-200">
        <CardHeader>
          <CardTitle className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
            Export Report
          </CardTitle>
          <CardDescription className="text-base">
            Download your inspection report in your preferred format
            {isAnyExporting && (
              <div className="flex items-center gap-2 text-blue-500 mt-2">
                <Loader2 className="h-4 w-4 animate-spin" />
                <span>Processing export...</span>
              </div>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex flex-col gap-4">
            <Button
              onClick={() => handleExport('pdf')}
              disabled={isAnyExporting}
              size="lg"
              title="Export as PDF Document - Single page formatted report"
              className="w-full rounded-xl shadow-lg shadow-primary/20 hover:shadow-primary/30 transition-all duration-200 relative"
            >
              {exportingStates.pdf ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  Generating PDF Document...
                </>
              ) : (
                <>
                  <FileText className="mr-2 h-5 w-5" />
                  Export as PDF Document
                </>
              )}
            </Button>
            <Button
              onClick={() => handleExport('excel')}
              disabled={isAnyExporting}
              size="lg"
              variant="outline"
              title="Export as Excel Spreadsheet - Structured data with multiple sheets"
              className="w-full rounded-xl transition-all duration-200 relative"
            >
              {exportingStates.excel ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  Generating Excel Spreadsheet...
                </>
              ) : (
                <>
                  <FileSpreadsheet className="mr-2 h-5 w-5 text-green-600" />
                  <span className="text-green-700 font-medium">Export as Excel Spreadsheet</span>
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Images */}
      {(checklist.beforeImage || checklist.afterImage) && (
        <Card className="group hover:shadow-lg hover:shadow-primary/5 transition-all duration-200">
          <CardHeader>
            <CardTitle className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
              Inspection Images
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {checklist.beforeImage && (
                <div className="space-y-2">
                  <h3 className="font-medium">Before</h3>
                  <ChecklistImage
                    src={checklist.beforeImage}
                    alt="Before inspection"
                    imagePath={checklist.imageMetadata?.beforeImage?.path}
                  />
                </div>
              )}
              {checklist.afterImage && (
                <div className="space-y-2">
                  <h3 className="font-medium">After</h3>
                  <ChecklistImage
                    src={checklist.afterImage}
                    alt="After inspection"
                    imagePath={checklist.imageMetadata?.afterImage?.path}
                  />
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

// Image component with error handling and refresh capability
function ChecklistImage({ 
  src, 
  alt, 
  imagePath,
  className = "",
  aspectRatio = "video" 
}: { 
  src: string; 
  alt: string; 
  imagePath?: string;
  className?: string;
  aspectRatio?: "video" | "square";
}) {
  const [isError, setIsError] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(src);

  const handleImageError = () => {
    setIsError(true);
  };

  const handleRefresh = async () => {
    if (!imagePath) return;
    
    setIsRefreshing(true);
    try {
      const refreshedUrl = await FirebaseStorageService.refreshImageUrl(imagePath);
      if (refreshedUrl) {
        setCurrentSrc(refreshedUrl);
        setIsError(false);
      }
    } catch (error) {
      console.error('Failed to refresh image URL:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const aspectClass = aspectRatio === "video" ? "aspect-video" : "aspect-square";

  if (isError) {
    return (
      <div className={`${aspectClass} rounded-lg border-2 border-dashed border-muted-foreground/25 bg-muted/10 flex flex-col items-center justify-center gap-2 ${className}`}>
        <ImageIcon className="h-8 w-8 text-muted-foreground" />
        <p className="text-xs text-muted-foreground text-center">Image unavailable</p>
        {imagePath && (
          <Button
            size="sm"
            variant="ghost"
            onClick={handleRefresh}
            disabled={isRefreshing}
            className="text-xs"
          >
            {isRefreshing ? (
              <RefreshCw className="h-3 w-3 animate-spin" />
            ) : (
              <RefreshCw className="h-3 w-3" />
            )}
            Retry
          </Button>
        )}
      </div>
    );
  }

  return (
    <div className={`relative ${aspectClass} rounded-lg overflow-hidden ${className}`}>
      <Image
        {...getImageProps(currentSrc, {
          alt,
          sizes: "(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",
          className: "object-cover"
        })}
        fill
        onError={handleImageError}
      />
    </div>
  );
} 
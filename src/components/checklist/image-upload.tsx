"use client";

import { useState, useRef, useEffect } from "react";
import { UseFormReturn } from "react-hook-form";
import { ChecklistFormData } from "@/lib/utils/validation";
import { 
  compressImage, 
  quickCompress, 
  validateImageFile, 
  formatFileSize,
  getRecommendedProfile,
  CompressionResult 
  } from "@/lib/utils/compression/image-compression";
import { FirebaseStorageService, ImageUploadResult } from "@/lib/services/storage/firebase-storage-service";
import { useAuth } from "@/components/auth";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import { Upload, X, Camera, PenTool, Zap, Info, ImageIcon, RefreshCw, Image as ImageLucide } from "lucide-react";
import Image from "next/image";
import { useSearchParams } from "next/navigation";
import { getImageProps } from "@/lib/utils/image-utils";
import { CameraCapture } from "./camera-capture";

interface ImageUploadProps {
  form: UseFormReturn<ChecklistFormData>;
  checklistId?: string; // Optional prop to pass the actual checklist ID
}

export function ImageUpload({ form, checklistId }: ImageUploadProps) {
  const { user } = useAuth();
  const searchParams = useSearchParams();
  const editId = searchParams.get('edit');
  
  const [isUploading, setIsUploading] = useState(false);
  const [compressionProgress, setCompressionProgress] = useState(0);
  const [compressionInfo, setCompressionInfo] = useState<{
    originalSize?: number;
    compressedSize?: number;
    compressionRatio?: number;
    fieldName?: string;
    uploadResult?: ImageUploadResult;
  }>({});
  const [showCompressionDetails, setShowCompressionDetails] = useState(false);
  const [activeCameraCapture, setActiveCameraCapture] = useState<"beforeImage" | "afterImage" | "inspectorSignature" | null>(null);
  
  const beforeImageRef = useRef<HTMLInputElement>(null);
  const afterImageRef = useRef<HTMLInputElement>(null);
  const signatureRef = useRef<HTMLInputElement>(null);

  const beforeImage = form.watch("beforeImage");
  const afterImage = form.watch("afterImage");
  const inspectorSignature = form.watch("inspectorSignature");

  // Generate a consistent checklist ID for the session
  const getChecklistId = () => {
    if (checklistId) return checklistId;
    if (editId) return editId;
    
    // For new checklists, generate a consistent session ID
    const sessionId = sessionStorage.getItem('current_checklist_id');
    if (sessionId) return sessionId;
    
    // Use the same ID generation method as StorageService.generateId()
    const newId = crypto.randomUUID?.() || 
                  `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
    sessionStorage.setItem('current_checklist_id', newId);
    return newId;
  };

  const handleImageUpload = async (
    file: File,
    fieldName: "beforeImage" | "afterImage" | "inspectorSignature"
  ) => {
    if (!file || !user) {
      if (!user) {
        alert("Please sign in to upload images.");
      }
      return;
    }

    // Validate file
    const validation = validateImageFile(file);
    if (!validation.isValid) {
      alert(validation.error);
      return;
    }

    setIsUploading(true);
    setCompressionProgress(0);
    setCompressionInfo({ fieldName });

    try {
      // Simulate progress for better UX
      const progressInterval = setInterval(() => {
        setCompressionProgress(prev => Math.min(prev + 10, 80));
      }, 100);

      // Use consistent checklist ID
      const currentChecklistId = getChecklistId();
      
      // Map field names to storage types
      const imageTypeMap = {
        beforeImage: 'before' as const,
        afterImage: 'after' as const,
        inspectorSignature: 'signature' as const
      };

      // Upload to Firebase Storage
      const uploadResult = await FirebaseStorageService.uploadImage(
        file,
        user.uid,
        currentChecklistId,
        imageTypeMap[fieldName]
      );

      clearInterval(progressInterval);
      setCompressionProgress(100);

      // Update compression info for display
      setCompressionInfo({
        fieldName,
        originalSize: uploadResult.size,
        compressedSize: uploadResult.compressedSize,
        compressionRatio: uploadResult.compressionRatio,
        uploadResult
      });

      // Set the image URL
      form.setValue(fieldName, uploadResult.url);

      // Update metadata for cleanup tracking
      const currentMetadata = form.getValues('imageMetadata') || {};
      form.setValue('imageMetadata', {
        ...currentMetadata,
        [fieldName]: {
          path: uploadResult.path,
          originalName: uploadResult.originalName
        }
      });

      // Show compression details briefly
      setShowCompressionDetails(true);
      setTimeout(() => setShowCompressionDetails(false), 5000);

    } catch (error) {
      console.error("Error uploading image:", error);
      alert("Failed to upload image. Please check your connection and try again.");
    } finally {
      setIsUploading(false);
      setTimeout(() => {
        setCompressionProgress(0);
        setCompressionInfo({});
      }, 2000);
    }
  };

  const removeImage = (fieldName: "beforeImage" | "afterImage" | "inspectorSignature") => {
    form.setValue(fieldName, undefined);
    if (fieldName === "beforeImage" && beforeImageRef.current) {
      beforeImageRef.current.value = "";
    }
    if (fieldName === "afterImage" && afterImageRef.current) {
      afterImageRef.current.value = "";
    }
    if (fieldName === "inspectorSignature" && signatureRef.current) {
      signatureRef.current.value = "";
    }
  };

  // Handle camera capture
  const handleCameraCapture = (file: File) => {
    if (activeCameraCapture) {
      handleImageUpload(file, activeCameraCapture);
      setActiveCameraCapture(null);
    }
  };

  // Clean up session storage when component unmounts
  useEffect(() => {
    return () => {
      // Only clean up if we're not in edit mode and no images are present
      if (!editId && !beforeImage && !afterImage && !inspectorSignature) {
        sessionStorage.removeItem('current_checklist_id');
      }
    };
  }, [editId, beforeImage, afterImage, inspectorSignature]);

  return (
    <div className="space-y-6">
      {/* Compression Status */}
      {(isUploading || showCompressionDetails) && (
        <Card className="border-primary/20 bg-primary/5">
          <CardContent className="pt-6">
            <div className="space-y-3">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-primary" />
                <span className="text-sm font-medium">
                  {isUploading ? "Uploading to cloud storage..." : "Upload Complete"}
                </span>
              </div>
              
              {isUploading && (
                <div className="space-y-2">
                  <Progress value={compressionProgress} className="h-2" />
                  <p className="text-xs text-muted-foreground">
                    Compressing and uploading to cloud storage
                  </p>
                </div>
              )}

              {showCompressionDetails && compressionInfo.originalSize && (
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <div className="flex items-center gap-1">
                    <Info className="h-3 w-3" />
                    <span>
                      Reduced from {formatFileSize(compressionInfo.originalSize)} to{" "}
                      {formatFileSize(compressionInfo.compressedSize || 0)}
                    </span>
                  </div>
                  <div className="text-green-600 font-medium">
                    {compressionInfo.compressionRatio?.toFixed(1)}% smaller
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Camera className="h-5 w-5" />
            Image Upload
          </CardTitle>
          <CardDescription>
            Upload before and after images of the inspection. Images are automatically compressed for optimal performance.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Before Image */}
          <div className="space-y-3">
            <Label htmlFor="beforeImage" className="text-sm font-medium">
              Before Image
            </Label>
            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6">
              {beforeImage ? (
                <div className="space-y-3">
                  <ImagePreview
                    src={beforeImage}
                    alt="Before inspection"
                    onRemove={() => removeImage("beforeImage")}
                    imagePath={form.getValues('imageMetadata')?.beforeImage?.path}
                  />
                  <p className="text-sm text-muted-foreground text-center">
                    Before image uploaded successfully
                  </p>
                </div>
              ) : (
                <div className="text-center space-y-3">
                  <Upload className="h-12 w-12 mx-auto text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Click to upload before image</p>
                    <p className="text-xs text-muted-foreground">
                      PNG, JPG, WebP up to 20MB (auto-compressed to ~500KB)
                    </p>
                  </div>
                  <Input
                    ref={beforeImageRef}
                    id="beforeImage"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleImageUpload(file, "beforeImage");
                    }}
                    disabled={isUploading}
                  />
                  <div className="flex gap-2 justify-center">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => beforeImageRef.current?.click()}
                      disabled={isUploading}
                    >
                      <ImageLucide className="h-4 w-4 mr-2" />
                      {isUploading ? "Uploading..." : "Gallery"}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setActiveCameraCapture("beforeImage")}
                      disabled={isUploading}
                    >
                      <Camera className="h-4 w-4 mr-2" />
                      Camera
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* After Image */}
          <div className="space-y-3">
            <Label htmlFor="afterImage" className="text-sm font-medium">
              After Image
            </Label>
            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6">
              {afterImage ? (
                <div className="space-y-3">
                  <ImagePreview
                    src={afterImage}
                    alt="After inspection"
                    onRemove={() => removeImage("afterImage")}
                    imagePath={form.getValues('imageMetadata')?.afterImage?.path}
                  />
                  <p className="text-sm text-muted-foreground text-center">
                    After image uploaded successfully
                  </p>
                </div>
              ) : (
                <div className="text-center space-y-3">
                  <Upload className="h-12 w-12 mx-auto text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Click to upload after image</p>
                    <p className="text-xs text-muted-foreground">
                      PNG, JPG, WebP up to 20MB (auto-compressed to ~500KB)
                    </p>
                  </div>
                  <Input
                    ref={afterImageRef}
                    id="afterImage"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleImageUpload(file, "afterImage");
                    }}
                    disabled={isUploading}
                  />
                  <div className="flex gap-2 justify-center">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => afterImageRef.current?.click()}
                      disabled={isUploading}
                    >
                      <ImageLucide className="h-4 w-4 mr-2" />
                      {isUploading ? "Uploading..." : "Gallery"}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setActiveCameraCapture("afterImage")}
                      disabled={isUploading}
                    >
                      <Camera className="h-4 w-4 mr-2" />
                      Camera
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Inspector Signature */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <PenTool className="h-5 w-5" />
            Inspector Signature
          </CardTitle>
          <CardDescription>
            Upload inspector signature (optional)
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <Label htmlFor="inspectorSignature" className="text-sm font-medium">
              Signature Image
            </Label>
            <div className="border-2 border-dashed border-muted-foreground/25 rounded-lg p-6">
              {inspectorSignature ? (
                <div className="space-y-3">
                  <ImagePreview
                    src={inspectorSignature}
                    alt="Inspector signature"
                    onRemove={() => removeImage("inspectorSignature")}
                    imagePath={form.getValues('imageMetadata')?.inspectorSignature?.path}
                  />
                  <p className="text-sm text-muted-foreground text-center">
                    Signature uploaded successfully
                  </p>
                </div>
              ) : (
                <div className="text-center space-y-3">
                  <PenTool className="h-12 w-12 mx-auto text-muted-foreground" />
                  <div>
                    <p className="text-sm font-medium">Click to upload signature</p>
                    <p className="text-xs text-muted-foreground">
                      PNG, JPG up to 20MB (auto-compressed to ~200KB, Optional)
                    </p>
                  </div>
                  <Input
                    ref={signatureRef}
                    id="inspectorSignature"
                    type="file"
                    accept="image/*"
                    className="hidden"
                    onChange={(e) => {
                      const file = e.target.files?.[0];
                      if (file) handleImageUpload(file, "inspectorSignature");
                    }}
                    disabled={isUploading}
                  />
                  <div className="flex gap-2 justify-center">
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => signatureRef.current?.click()}
                      disabled={isUploading}
                    >
                      <ImageLucide className="h-4 w-4 mr-2" />
                      {isUploading ? "Uploading..." : "Gallery"}
                    </Button>
                    <Button
                      type="button"
                      variant="outline"
                      onClick={() => setActiveCameraCapture("inspectorSignature")}
                      disabled={isUploading}
                    >
                      <Camera className="h-4 w-4 mr-2" />
                      Camera
                    </Button>
                  </div>
                </div>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Camera Capture Modal */}
      <CameraCapture
        isOpen={activeCameraCapture !== null}
        onClose={() => setActiveCameraCapture(null)}
        onCapture={handleCameraCapture}
        title={activeCameraCapture === "beforeImage" ? "Take Before Photo" : 
               activeCameraCapture === "afterImage" ? "Take After Photo" : 
               "Take Signature Photo"}
        description={activeCameraCapture === "beforeImage" ? "Capture the equipment state before inspection" : 
                    activeCameraCapture === "afterImage" ? "Capture the equipment state after inspection" : 
                    "Capture your signature for the inspection"}
      />
    </div>
  );
}

// Image preview component with error handling
function ImagePreview({ 
  src, 
  alt, 
  onRemove,
  imagePath 
}: { 
  src: string; 
  alt: string; 
  onRemove: () => void;
  imagePath?: string;
}) {
  const [isError, setIsError] = useState(false);
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [currentSrc, setCurrentSrc] = useState(src);

  const handleImageError = () => {
    setIsError(true);
  };

  const handleRefresh = async () => {
    if (!imagePath) return;
    
    setIsRefreshing(true);
    try {
      const refreshedUrl = await FirebaseStorageService.refreshImageUrl(imagePath);
      if (refreshedUrl) {
        setCurrentSrc(refreshedUrl);
        setIsError(false);
      }
    } catch (error) {
      console.error('Failed to refresh image URL:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  if (isError) {
    return (
      <div className="relative max-w-xs mx-auto">
        <div className="h-48 w-full rounded-lg border-2 border-dashed border-muted-foreground/25 bg-muted/10 flex flex-col items-center justify-center gap-2">
          <ImageIcon className="h-8 w-8 text-muted-foreground" />
          <p className="text-xs text-muted-foreground text-center">Image unavailable</p>
          {imagePath && (
            <Button
              size="sm"
              variant="ghost"
              onClick={handleRefresh}
              disabled={isRefreshing}
              className="text-xs"
            >
              {isRefreshing ? (
                <RefreshCw className="h-3 w-3 animate-spin" />
              ) : (
                <RefreshCw className="h-3 w-3" />
              )}
              Retry
            </Button>
          )}
        </div>
        <Button
          type="button"
          variant="destructive"
          size="icon"
          className="absolute top-2 right-2"
          onClick={onRemove}
        >
          <X className="h-4 w-4" />
        </Button>
      </div>
    );
  }

  return (
    <div className="relative max-w-xs mx-auto">
      <Image
        {...getImageProps(currentSrc, {
          alt,
          width: 300,
          height: 200,
          sizes: "300px",
          className: "rounded-lg object-cover w-full h-48"
        })}
        onError={handleImageError}
      />
      <Button
        type="button"
        variant="destructive"
        size="icon"
        className="absolute top-2 right-2"
        onClick={onRemove}
      >
        <X className="h-4 w-4" />
      </Button>
    </div>
  );
} 
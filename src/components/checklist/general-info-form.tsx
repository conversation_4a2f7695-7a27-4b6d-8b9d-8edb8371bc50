"use client";

import { UseFormReturn } from "react-hook-form";
import { ChecklistFormData } from "@/lib/utils/validation";
import { EquipmentTag } from "@/types/equipment-tag";
import { TagSelector } from "./tag-selector";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Lock, AlertTriangle } from "lucide-react";

interface GeneralInfoFormProps {
  form: UseFormReturn<ChecklistFormData>;
  selectedEquipmentTag?: EquipmentTag | null;
  onEquipmentTagSelect?: (tag: EquipmentTag) => void;
  onEquipmentTagClear?: () => void;
}

export function GeneralInfoForm({ 
  form, 
  selectedEquipmentTag = null,
  onEquipmentTagSelect,
  onEquipmentTagClear
}: GeneralInfoFormProps) {
  
  const handleTagSelect = (tag: EquipmentTag) => {
    // Auto-populate form fields from equipment tag
    form.setValue("generalInfo.clientName", tag.clientName, { shouldValidate: true });
    form.setValue("generalInfo.building", tag.building, { shouldValidate: true });
    form.setValue("generalInfo.equipmentName", tag.equipmentName, { shouldValidate: true });
    form.setValue("generalInfo.location", tag.location, { shouldValidate: true });
    form.setValue("generalInfo.tagNo", tag.tagNumber, { shouldValidate: true });
    
    // Call parent handler
    onEquipmentTagSelect?.(tag);
  };

  const handleTagClear = () => {
    // Clear auto-populated fields but keep user-entered data
    form.setValue("generalInfo.clientName", "", { shouldValidate: true });
    form.setValue("generalInfo.building", "", { shouldValidate: true });
    form.setValue("generalInfo.equipmentName", "", { shouldValidate: true });
    form.setValue("generalInfo.location", "", { shouldValidate: true });
    form.setValue("generalInfo.tagNo", "", { shouldValidate: true });
    
    // Call parent handler
    onEquipmentTagClear?.();
  };

  const isEquipmentFieldDisabled = !!selectedEquipmentTag;

  return (
    <div className="space-y-6">
      {/* Equipment Tag Selection */}
      <TagSelector
        onTagSelect={handleTagSelect}
        selectedTag={selectedEquipmentTag}
        onClear={handleTagClear}
        required={true}
      />

      {/* Warning if no equipment selected */}
      {!selectedEquipmentTag && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            <strong>Equipment selection required:</strong> Please select a valid equipment tag above before filling out the inspection details.
          </AlertDescription>
        </Alert>
      )}

      {/* General Information Form */}
      <Card className="group hover:shadow-lg hover:shadow-primary/5 transition-all duration-200">
        <CardHeader>
          <CardTitle className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
            General Information
          </CardTitle>
          <CardDescription className="text-base">
            Basic details about the inspection and equipment
            {selectedEquipmentTag && (
              <span className="block mt-1 text-green-600 font-medium">
                Equipment details auto-populated from tag: {selectedEquipmentTag.tagNumber}
              </span>
            )}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="generalInfo.clientName"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel className="text-base flex items-center gap-2">
                    Client Name
                    {isEquipmentFieldDisabled && <Lock className="h-3 w-3 text-muted-foreground" />}
                  </FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="e.g., Wyndham Doha West Bay" 
                      className="h-11 rounded-lg"
                      disabled={isEquipmentFieldDisabled}
                      {...field} 
                    />
                  </FormControl>
                  {isEquipmentFieldDisabled && (
                    <p className="text-xs text-muted-foreground">Auto-populated from equipment tag</p>
                  )}
                  <FormMessage className="text-sm" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="generalInfo.building"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel className="text-base flex items-center gap-2">
                    Building
                    {isEquipmentFieldDisabled && <Lock className="h-3 w-3 text-muted-foreground" />}
                  </FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="e.g., Wyndham hotel wb" 
                      className="h-11 rounded-lg"
                      disabled={isEquipmentFieldDisabled}
                      {...field} 
                    />
                  </FormControl>
                  {isEquipmentFieldDisabled && (
                    <p className="text-xs text-muted-foreground">Auto-populated from equipment tag</p>
                  )}
                  <FormMessage className="text-sm" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="generalInfo.inspectedBy"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel className="text-base">Inspected By</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="e.g., Navaneethan" 
                      className="h-11 rounded-lg"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage className="text-sm" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="generalInfo.approvedBy"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel className="text-base">Approved By</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="e.g., Sandeep" 
                      className="h-11 rounded-lg"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage className="text-sm" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="generalInfo.date"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel className="text-base">Date</FormLabel>
                  <FormControl>
                    <Input 
                      type="date"
                      className="h-11 rounded-lg"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage className="text-sm" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="generalInfo.ppmAttempt"
              render={({ field: { onChange, value, ...rest } }) => (
                <FormItem className="space-y-2">
                  <FormLabel className="text-base">PPM Attempt</FormLabel>
                  <FormControl>
                    <Input 
                      type="number"
                      placeholder="e.g., 3"
                      className="h-11 rounded-lg"
                      value={value || ''}
                      onChange={(e) => {
                        const val = e.target.value;
                        onChange(val === '' ? undefined : Number(val));
                      }}
                      {...rest}
                    />
                  </FormControl>
                  <FormMessage className="text-sm" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="generalInfo.equipmentName"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel className="text-base flex items-center gap-2">
                    Equipment Name
                    {isEquipmentFieldDisabled && <Lock className="h-3 w-3 text-muted-foreground" />}
                  </FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="e.g., JET FAN" 
                      className="h-11 rounded-lg"
                      disabled={isEquipmentFieldDisabled}
                      {...field} 
                    />
                  </FormControl>
                  {isEquipmentFieldDisabled && (
                    <p className="text-xs text-muted-foreground">Auto-populated from equipment tag</p>
                  )}
                  <FormMessage className="text-sm" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="generalInfo.location"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel className="text-base flex items-center gap-2">
                    Location
                    {isEquipmentFieldDisabled && <Lock className="h-3 w-3 text-muted-foreground" />}
                  </FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="e.g., BASEMENT 1" 
                      className="h-11 rounded-lg"
                      disabled={isEquipmentFieldDisabled}
                      {...field} 
                    />
                  </FormControl>
                  {isEquipmentFieldDisabled && (
                    <p className="text-xs text-muted-foreground">Auto-populated from equipment tag</p>
                  )}
                  <FormMessage className="text-sm" />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="generalInfo.tagNo"
              render={({ field }) => (
                <FormItem className="space-y-2">
                  <FormLabel className="text-base flex items-center gap-2">
                    Tag No
                    {isEquipmentFieldDisabled && <Lock className="h-3 w-3 text-muted-foreground" />}
                  </FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="e.g., JF 10" 
                      className="h-11 rounded-lg"
                      disabled={isEquipmentFieldDisabled}
                      {...field} 
                    />
                  </FormControl>
                  {isEquipmentFieldDisabled && (
                    <p className="text-xs text-muted-foreground">Auto-populated from equipment tag</p>
                  )}
                  <FormMessage className="text-sm" />
                </FormItem>
              )}
            />
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 
"use client";

import { UseFormReturn } from "react-hook-form";
import { ChecklistFormData } from "@/lib/utils/validation";
import { ChecklistFieldConfig } from "@/types/checklist";
import { CheckField } from "./check-field";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

interface ChecksSectionProps {
  title: string;
  description: string;
  fields: ChecklistFieldConfig[];
  section: 'mechanical' | 'electrical' | 'sequence';
  form: UseFormReturn<ChecklistFormData>;
  defaultOpen?: boolean;
}

export function ChecksSection({ title, description, fields, section, form, defaultOpen }: ChecksSectionProps) {
  return (
    <Card className="group hover:shadow-lg hover:shadow-primary/5 transition-all duration-200">
      <CardHeader>
        <CardTitle className="text-2xl font-bold bg-gradient-to-r from-primary to-primary/60 bg-clip-text text-transparent">
          {title}
        </CardTitle>
        <CardDescription className="text-base">
          {description}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Accordion type="single" collapsible defaultValue={defaultOpen ? "checks" : undefined} className="w-full">
          <AccordionItem value="checks" className="border-none">
            <AccordionTrigger className="hover:no-underline">
              <div className="flex items-center gap-2 text-lg font-medium">
                <span>View {title} Checks</span>
              </div>
            </AccordionTrigger>
            <AccordionContent>
              <div className="space-y-6 pt-4">
                {fields.map((field, index) => (
                  <CheckField
                    key={`${section}-${field.key}-${index}`}
                    field={field}
                    section={section}
                    form={form}
                  />
                ))}
              </div>
            </AccordionContent>
          </AccordionItem>
        </Accordion>
      </CardContent>
    </Card>
  );
} 
"use client";

import { UseFormReturn, FieldPath } from "react-hook-form";
import { ChecklistFormData } from "@/lib/utils/validation";
import { ChecklistFieldConfig, CheckStatus } from "@/types/checklist";
import { FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { StatusToggleGroup } from "./status-toggle-group";
import { cn } from "@/lib/utils/utils";

interface CheckFieldProps {
  form: UseFormReturn<ChecklistFormData>;
  field: ChecklistFieldConfig;
  section: 'mechanical' | 'electrical' | 'sequence';
}

export function CheckField({ form, field, section }: CheckFieldProps) {
  const getFieldName = (): FieldPath<ChecklistFormData> => {
    const sectionKey = section === 'sequence' ? 'sequenceControlsChecks' : `${section}Checks`;
    return `${sectionKey}.${field.key}` as FieldPath<ChecklistFormData>;
  };

  const fieldName = getFieldName();

  if (field.type === 'number') {
    return (
      <FormField
        control={form.control}
        name={fieldName}
        render={({ field: formField }) => {
          const isEmpty = formField.value === undefined || formField.value === null || formField.value === '';
          const displayValue = typeof formField.value === 'number' ? formField.value.toString() : '';
          
          return (
            <FormItem>
              <FormLabel className="text-sm">
                {field.label}
                {field.unit && (
                  <span className="text-muted-foreground ml-1">({field.unit})</span>
                )}
                {isEmpty && (
                  <span className="ml-2 inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300">
                    <span className="mr-1">?</span>
                    Missing
                  </span>
                )}
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <Input
                    type="number"
                    step="any"
                    placeholder={`Enter value in ${field.unit || 'units'}`}
                    {...formField}
                    className={cn(
                      "transition-all duration-200",
                      isEmpty 
                        ? "border-amber-300 focus:border-amber-500 focus:ring-amber-500/20" 
                        : "border-green-300 focus:border-green-500 focus:ring-green-500/20"
                    )}
                    onChange={(e) => {
                      const value = e.target.value;
                      
                      if (value === '') {
                        formField.onChange(undefined);
                      } else {
                        const numValue = parseFloat(value);
                        if (!isNaN(numValue)) {
                          formField.onChange(numValue);
                        }
                      }
                    }}
                    value={displayValue}
                  />
                  {!isEmpty && (
                    <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white shadow-sm" />
                  )}
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          );
        }}
      />
    );
  }

  return (
    <FormField
      control={form.control}
      name={fieldName}
      render={({ field: formField }) => (
        <FormItem>
          <FormLabel className="text-sm">{field.label}</FormLabel>
          <FormControl>
            <StatusToggleGroup
              value={formField.value as CheckStatus}
              onValueChange={(value) => formField.onChange(value)}
            />
          </FormControl>
          <FormMessage />
        </FormItem>
      )}
    />
  );
} 
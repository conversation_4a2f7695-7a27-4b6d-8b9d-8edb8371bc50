"use client";

import { CheckStatus } from "@/types/checklist";
import { CHECK_STATUS_OPTIONS } from "@/config/checklist-fields";
import { cn } from "@/lib/utils/utils";

interface StatusToggleGroupProps {
  value?: CheckStatus;
  onValueChange: (value: CheckStatus) => void;
  disabled?: boolean;
}

export function StatusToggleGroup({ value, onValueChange, disabled }: StatusToggleGroupProps) {
  const getStatusColor = (status: CheckStatus, isSelected: boolean): string => {
    const baseClasses = "relative px-3 py-2 text-xs font-medium rounded-lg transition-all duration-200 border-2 cursor-pointer hover:scale-105";
    
    if (disabled) {
      return cn(baseClasses, "opacity-50 cursor-not-allowed hover:scale-100");
    }

    switch (status) {
      case 'OK':
        return cn(
          baseClasses,
          isSelected
            ? "bg-green-500 text-white border-green-500 shadow-lg shadow-green-500/25"
            : "bg-green-50 text-green-700 border-green-200 hover:bg-green-100 hover:border-green-300 dark:bg-green-900/20 dark:text-green-300 dark:border-green-800"
        );
      case 'Faulty':
        return cn(
          baseClasses,
          isSelected
            ? "bg-red-500 text-white border-red-500 shadow-lg shadow-red-500/25"
            : "bg-red-50 text-red-700 border-red-200 hover:bg-red-100 hover:border-red-300 dark:bg-red-900/20 dark:text-red-300 dark:border-red-800"
        );
      case 'N/A':
        return cn(
          baseClasses,
          isSelected
            ? "bg-slate-500 text-white border-slate-500 shadow-lg shadow-slate-500/25"
            : "bg-slate-50 text-slate-700 border-slate-200 hover:bg-slate-100 hover:border-slate-300 dark:bg-slate-900/20 dark:text-slate-300 dark:border-slate-800"
        );
      case 'Missing':
        return cn(
          baseClasses,
          isSelected
            ? "bg-amber-500 text-white border-amber-500 shadow-lg shadow-amber-500/25"
            : "bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100 hover:border-amber-300 dark:bg-amber-900/20 dark:text-amber-300 dark:border-amber-800"
        );
      default:
        return cn(baseClasses, "bg-slate-50 text-slate-700 border-slate-200");
    }
  };

  const getStatusIcon = (status: CheckStatus): string => {
    switch (status) {
      case 'OK':
        return '✓';
      case 'Faulty':
        return '✗';
      case 'N/A':
        return '—';
      case 'Missing':
        return '?';
      default:
        return '';
    }
  };

  return (
    <div className="flex flex-wrap gap-2 sm:gap-3">
      {CHECK_STATUS_OPTIONS.map((option) => {
        const isSelected = value === option.value;
        return (
          <button
            key={option.value}
            type="button"
            onClick={() => !disabled && onValueChange(option.value as CheckStatus)}
            className={getStatusColor(option.value as CheckStatus, isSelected)}
            disabled={disabled}
            aria-pressed={isSelected}
            aria-label={`Set status to ${option.label}`}
          >
            <span className="flex items-center gap-1.5">
              <span className="text-sm font-bold" aria-hidden="true">
                {getStatusIcon(option.value as CheckStatus)}
              </span>
              <span className="hidden sm:inline">{option.label}</span>
              <span className="sm:hidden text-xs">{option.value}</span>
            </span>
            
            {/* Selection indicator */}
            {isSelected && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-white rounded-full border-2 border-current shadow-sm" />
            )}
          </button>
        );
      })}
    </div>
  );
} 
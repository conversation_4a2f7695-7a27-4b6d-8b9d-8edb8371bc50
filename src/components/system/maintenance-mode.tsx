'use client';

import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { <PERSON><PERSON>, Clock, AlertTriangle, RefreshCw, Shield } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { SystemService, MaintenanceConfig } from '@/lib/services/system/system-service';
import { AuburnIcon } from '@/components/auburn-icon';
import GradientText from '@/components/gradient-text';

interface MaintenanceModeProps {
  userRole?: string;
  userId?: string;
  onBypass?: () => void;
}

export function MaintenanceMode({ userRole, userId, onBypass }: MaintenanceModeProps) {
  const [maintenanceConfig, setMaintenanceConfig] = useState<MaintenanceConfig | null>(null);
  const [canBypass, setCanBypass] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState<string | null>(null);
  const [funnyMessageIndex, setFunnyMessageIndex] = useState(0);

  const funnyMessages = [
    "🔧 System Under Construction",
    "🏗️ Building Something Awesome",
    "⚡ Upgrading to Super Mode",
    "🚀 Launching New Features",
    "🔮 Adding More Magic",
    "🎨 Polishing the Pixels",
    "⚙️ Calibrating the Flux Capacitor",
    "🧪 Conducting Science Experiments"
  ];

  useEffect(() => {
    // Subscribe to maintenance mode changes
    const unsubscribe = SystemService.subscribeToMaintenanceMode((config) => {
      setMaintenanceConfig(config);
      
      // Check if user can bypass maintenance mode
      if (config && userId && userRole) {
        SystemService.canBypassMaintenance(userId, userRole).then(setCanBypass);
      }
    });

    return unsubscribe;
  }, [userId, userRole]);

  useEffect(() => {
    // Update countdown timer for scheduled maintenance
    if (maintenanceConfig?.scheduledEnd) {
      const interval = setInterval(() => {
        const endTime = new Date(maintenanceConfig.scheduledEnd!);
        const now = new Date();
        const diff = endTime.getTime() - now.getTime();

        if (diff > 0) {
          const hours = Math.floor(diff / (1000 * 60 * 60));
          const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
          setTimeRemaining(`${hours}h ${minutes}m`);
        } else {
          setTimeRemaining(null);
        }
      }, 60000); // Update every minute

      return () => clearInterval(interval);
    }
  }, [maintenanceConfig?.scheduledEnd]);

  useEffect(() => {
    // Rotate funny messages every 3 seconds
    const interval = setInterval(() => {
      setFunnyMessageIndex((prev) => (prev + 1) % funnyMessages.length);
    }, 3000);

    return () => clearInterval(interval);
  }, [funnyMessages.length]);

  const handleRefresh = async () => {
    setIsChecking(true);
    try {
      // Force refresh maintenance status
      const config = await SystemService.getMaintenanceStatus();
      if (!config?.enabled && onBypass) {
        onBypass();
      }
    } catch (error) {
      console.error('Failed to check maintenance status:', error);
    } finally {
      setIsChecking(false);
    }
  };

  const handleBypass = () => {
    if (canBypass && onBypass) {
      onBypass();
    }
  };

  if (!maintenanceConfig?.enabled) {
    return null;
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-black via-gray-950 to-gray-900 flex items-center justify-center p-4 relative overflow-hidden">
      {/* Background decorative elements */}
      <div className="absolute inset-0">
        <div className="absolute top-20 left-20 w-72 h-72 bg-red-600/15 rounded-full blur-3xl animate-pulse" />
        <div className="absolute bottom-20 right-20 w-96 h-96 bg-gray-600/10 rounded-full blur-3xl animate-pulse" />
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[600px] h-[600px] bg-gray-500/5 rounded-full blur-3xl animate-pulse" />
      </div>
      {/* Modern grid pattern */}
      <div className="absolute inset-0 opacity-10">
        <div className="absolute inset-0" style={{
          backgroundImage: `
            linear-gradient(rgba(255,255,255,0.03) 1px, transparent 1px),
            linear-gradient(90deg, rgba(255,255,255,0.03) 1px, transparent 1px)
          `,
          backgroundSize: '20px 20px'
        }} />
      </div>
      
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="relative z-10 w-full max-w-2xl"
      >
        <Card className="bg-gray-900/90 backdrop-blur-xl border-gray-700/50 shadow-2xl shadow-black/50 ring-1 ring-white/10">
          <CardHeader className="text-center pb-6">
            <div className="flex justify-center mb-6">
              <motion.div
                animate={{ rotate: [0, 10, -10, 0] }}
                transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
                className="relative"
              >
                <AuburnIcon size={80} className="drop-shadow-lg" />
                <div className="absolute -top-2 -right-2">
                  <motion.div
                    animate={{ scale: [1, 1.2, 1] }}
                    transition={{ duration: 1.5, repeat: Infinity }}
                  >
                    <Wrench className="h-6 w-6 text-red-400" />
                  </motion.div>
                </div>
              </motion.div>
            </div>
            
            <CardTitle className="text-3xl font-bold mb-2">
              <GradientText 
                gradient="from-red-400 to-red-600"
                className="text-3xl font-bold"
              >
                System Maintenance
              </GradientText>
            </CardTitle>
            
            <div className="flex justify-center mb-4">
              <Badge variant="outline" className="bg-red-950/50 border-red-500/30 text-red-400 backdrop-blur-sm">
                <AlertTriangle className="h-3 w-3 mr-1" />
                Temporarily Unavailable
              </Badge>
            </div>
            
            <motion.div
              key={funnyMessageIndex}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.5 }}
              className="text-center"
            >
              <p className="text-gray-400 text-sm font-medium">
                {funnyMessages[funnyMessageIndex]}
              </p>
            </motion.div>
          </CardHeader>

          <CardContent className="space-y-6">
            <div className="text-center">
              <p className="text-gray-300 text-lg leading-relaxed mb-2">
                {maintenanceConfig.message}
              </p>
              <p className="text-gray-400 text-sm italic">
                🤖 "Don't worry, I'm not becoming self-aware... yet." - The System
              </p>
            </div>

            {timeRemaining && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                className="bg-gradient-to-r from-gray-950/50 to-black/50 border border-gray-500/20 rounded-lg p-4 text-center backdrop-blur-sm"
              >
                <div className="flex items-center justify-center space-x-2 mb-2">
                  <Clock className="h-5 w-5 text-gray-400" />
                  <span className="text-gray-300 font-medium">⏳ Time Until We're Back (Hopefully)</span>
                </div>
                <div className="text-2xl font-bold text-white">
                  {timeRemaining}
                </div>
                <p className="text-xs text-gray-400 mt-2">
                  *Disclaimer: Our crystal ball might be slightly foggy
                </p>
              </motion.div>
            )}

            <div className="bg-gradient-to-r from-gray-950/30 to-black/30 border border-gray-500/20 rounded-lg p-4 backdrop-blur-sm">
              <h3 className="text-gray-200 font-semibold mb-3 flex items-center">
                <AlertTriangle className="h-4 w-4 mr-2 text-amber-400" />
                🔧 What's the Deal?
              </h3>
              <ul className="text-gray-300 space-y-2 text-sm">
                <li className="flex items-start space-x-2">
                  <span className="text-amber-400 mt-1 font-medium">🚀</span>
                  <span>Our engineers are giving the system a spa day (it's been working hard!)</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-amber-400 mt-1 font-medium">🔒</span>
                  <span>Your data is safer than snacks in a developer's desk drawer</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-amber-400 mt-1 font-medium">☕</span>
                  <span>Perfect time to grab some coffee (or take a power nap)</span>
                </li>
                <li className="flex items-start space-x-2">
                  <span className="text-amber-400 mt-1 font-medium">🎯</span>
                  <span>We promise we're not just turning it off and on again... mostly</span>
                </li>
              </ul>
            </div>

            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button
                onClick={handleRefresh}
                disabled={isChecking}
                variant="outline"
                className="border-gray-600 hover:border-gray-500 text-gray-300 hover:text-white hover:bg-gray-800/50 transition-all duration-200 backdrop-blur-sm"
              >
                <motion.div
                  animate={isChecking ? { rotate: 360 } : { rotate: 0 }}
                  transition={{ duration: 1, repeat: isChecking ? Infinity : 0, ease: "linear" }}
                >
                  <RefreshCw className="h-4 w-4 mr-2" />
                </motion.div>
                {isChecking ? "🔮 Consulting the Magic 8-Ball..." : "🔄 Are We There Yet?"}
              </Button>

              {canBypass && (
                <Button
                  onClick={handleBypass}
                  variant="default"
                  className="bg-gradient-to-r from-red-600 to-red-700 hover:from-red-500 hover:to-red-600 text-white shadow-lg hover:shadow-xl hover:shadow-red-500/25 transition-all duration-200"
                >
                  <Shield className="h-4 w-4 mr-2" />
                  🚪 Secret Admin Portal
                </Button>
              )}
            </div>

            <div className="text-center text-sm text-gray-400">
              <p>
                🚨 For urgent matters (like the coffee machine is broken), call us at{' '}
                <a 
                  href="tel:+97470300401" 
                  className="text-red-400 hover:text-red-300 font-medium transition-colors underline decoration-red-400/50 hover:decoration-red-300"
                >
                  +974 70300401
                </a>
              </p>
              <p className="mt-1">
                📅 Last update: {new Date(maintenanceConfig.lastUpdatedAt).toLocaleString()}
              </p>
              <p className="mt-1 text-xs text-gray-500">
                🎭 "Have you tried turning it off and on again?" - Our IT Department (probably)
              </p>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
} 
'use client';

import { useState, useEffect, ReactNode } from 'react';
import { useAuth } from '@/components/auth/auth-provider';
import { SystemService, MaintenanceConfig } from '@/lib/services/system/system-service';
import { MaintenanceMode } from './maintenance-mode';

interface MaintenanceWrapperProps {
  children: ReactNode;
}

export function MaintenanceWrapper({ children }: MaintenanceWrapperProps) {
  const { user, extendedUser } = useAuth();
  const [maintenanceConfig, setMaintenanceConfig] = useState<MaintenanceConfig | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [canBypass, setCanBypass] = useState(false);
  const [showMaintenance, setShowMaintenance] = useState(false);

  useEffect(() => {
    // Initialize system configuration
    const initializeSystem = async () => {
      try {
        await SystemService.initializeSystemConfig();
      } catch (error) {
        console.error('Failed to initialize system configuration:', error);
      }
    };

    initializeSystem();

    // Subscribe to maintenance mode changes
    const unsubscribe = SystemService.subscribeToMaintenanceMode(async (config) => {
      setMaintenanceConfig(config);
      
      if (config?.enabled && user && extendedUser) {
        // Check if user can bypass maintenance mode
        const bypass = await SystemService.canBypassMaintenance(user.uid, extendedUser.role);
        setCanBypass(bypass);
        setShowMaintenance(!bypass);
      } else {
        setShowMaintenance(false);
        setCanBypass(false);
      }
      
      setIsLoading(false);
    });

    return unsubscribe;
  }, [user, extendedUser]);

  const handleBypass = () => {
    setShowMaintenance(false);
  };

  // Show loading state while checking maintenance status
  if (isLoading) {
    return (
      <div className="min-h-screen bg-slate-900 flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-red-600"></div>
      </div>
    );
  }

  // Show maintenance mode if enabled and user cannot bypass
  if (showMaintenance && maintenanceConfig?.enabled) {
    return (
      <MaintenanceMode
        userRole={extendedUser?.role}
        userId={user?.uid}
        onBypass={handleBypass}
      />
    );
  }

  // Show normal content
  return <>{children}</>;
} 
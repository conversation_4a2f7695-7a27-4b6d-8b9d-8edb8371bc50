'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { RefreshCw, Info, Clock, GitCommit, AlertCircle, CheckCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip';
import { SystemService } from '@/lib/services/system/system-service';
import { APP_VERSION, VersionUtils, VersionInfo } from '@/config/version';

interface VersionDisplayProps {
  showFullInfo?: boolean;
  showUpdateCheck?: boolean;
  className?: string;
}

export function VersionDisplay({ 
  showFullInfo = false, 
  showUpdateCheck = false,
  className = '' 
}: VersionDisplayProps) {
  const [versionInfo, setVersionInfo] = useState<VersionInfo | null>(null);
  const [updateAvailable, setUpdateAvailable] = useState(false);
  const [isChecking, setIsChecking] = useState(false);
  const [lastChecked, setLastChecked] = useState<string | null>(null);

  useEffect(() => {
    // Initialize with current version
    setVersionInfo({
      current: APP_VERSION,
      displayName: VersionUtils.formatFullVersion(APP_VERSION),
      releaseDate: APP_VERSION.timestamp,
      releaseNotes: []
    });

    // Subscribe to version updates if update check is enabled
    if (showUpdateCheck) {
      const unsubscribe = SystemService.subscribeToVersionUpdates(
        (version, hasUpdate) => {
          if (version) {
            setVersionInfo(version);
          }
          setUpdateAvailable(hasUpdate);
        }
      );

      return unsubscribe;
    }
  }, [showUpdateCheck]);

  const handleCheckForUpdates = async () => {
    if (isChecking) return;

    setIsChecking(true);
    try {
      const hasUpdate = await SystemService.checkForUpdates();
      setUpdateAvailable(hasUpdate);
      setLastChecked(new Date().toISOString());
    } catch (error) {
      console.error('Failed to check for updates:', error);
    } finally {
      setIsChecking(false);
    }
  };

  const formatLastChecked = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 1) return 'Just now';
    if (diffMins < 60) return `${diffMins}m ago`;
    if (diffMins < 1440) return `${Math.floor(diffMins / 60)}h ago`;
    return date.toLocaleDateString();
  };

  if (!versionInfo) return null;

  return (
    <div className={`flex items-center space-x-2 ${className}`}>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center space-x-2">
              <Badge 
                variant="outline" 
                className="text-xs font-mono bg-slate-900/50 border-slate-700 text-slate-300"
              >
                {versionInfo.displayName}
              </Badge>
              
              {updateAvailable && (
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  className="flex items-center"
                >
                  <Badge 
                    variant="default" 
                    className="text-xs bg-green-600 hover:bg-green-700 text-white"
                  >
                    <CheckCircle className="h-3 w-3 mr-1" />
                    Update Available
                  </Badge>
                </motion.div>
              )}
            </div>
          </TooltipTrigger>
          <TooltipContent side="top" className="max-w-sm">
            <div className="space-y-2">
              <div className="font-semibold">Version Information</div>
              <div className="text-sm space-y-1">
                <div className="flex items-center space-x-2">
                  <Info className="h-3 w-3" />
                  <span>Version: {versionInfo.displayName}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Clock className="h-3 w-3" />
                  <span>{VersionUtils.formatBuildInfo(versionInfo.current)}</span>
                </div>
                <div className="flex items-center space-x-2">
                  <GitCommit className="h-3 w-3" />
                  <span>Commit: {VersionUtils.getShortCommitHash(versionInfo.current)}</span>
                </div>
                {lastChecked && (
                  <div className="flex items-center space-x-2 text-slate-400">
                    <RefreshCw className="h-3 w-3" />
                    <span>Checked: {formatLastChecked(lastChecked)}</span>
                  </div>
                )}
              </div>
              {versionInfo.releaseNotes && versionInfo.releaseNotes.length > 0 && (
                <div className="border-t border-slate-600 pt-2">
                  <div className="text-xs font-medium mb-1">Release Notes:</div>
                  <ul className="text-xs space-y-1 text-slate-300">
                    {versionInfo.releaseNotes.slice(0, 3).map((note, index) => (
                      <li key={index} className="flex items-start space-x-1">
                        <span className="text-slate-500">•</span>
                        <span>{note}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>

      {showUpdateCheck && (
        <Button
          variant="ghost"
          size="sm"
          onClick={handleCheckForUpdates}
          disabled={isChecking}
          className="h-6 w-6 p-0 text-slate-400 hover:text-slate-200"
        >
          <motion.div
            animate={isChecking ? { rotate: 360 } : { rotate: 0 }}
            transition={{ duration: 1, repeat: isChecking ? Infinity : 0, ease: "linear" }}
          >
            <RefreshCw className="h-3 w-3" />
          </motion.div>
        </Button>
      )}

      {showFullInfo && (
        <div className="text-xs text-slate-400 space-y-1">
          <div>Built: {new Date(versionInfo.current.timestamp).toLocaleDateString()}</div>
          <div>Commit: {VersionUtils.getShortCommitHash(versionInfo.current)}</div>
        </div>
      )}
    </div>
  );
} 
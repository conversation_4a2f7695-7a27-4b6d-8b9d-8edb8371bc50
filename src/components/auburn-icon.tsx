interface AuburnIconProps {
  className?: string;
  size?: number;
}

export function AuburnIcon({ className = "", size = 24 }: AuburnIconProps) {
  return (
    <div 
      className={`relative ${className}`}
      style={{ width: size, height: size }}
    >
      <img
        src="/icons/icon.png"
        alt="Auburn Engineering"
        width={size}
        height={size}
        className="rounded-lg shadow-lg"
        loading="eager"
      />
    </div>
  );
} 
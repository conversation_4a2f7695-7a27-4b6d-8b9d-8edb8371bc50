'use client';

import React, { useState, useEffect } from 'react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Button } from '@/components/ui/button';
import { X, AlertTriangle, HardDrive, CloudUpload, Trash2 } from 'lucide-react';
import { StorageService } from '@/lib/services/storage/storage-adapter';
import { FirestoreStorageService } from '@/lib/services/storage/firestore-storage';
import { useOptionalAuth } from '@/components/auth/safe-auth-hook';

interface StorageAlert {
  id: string;
  type: 'quota-exceeded' | 'warning' | 'critical';
  message: string;
  timestamp: number;
  dismissed?: boolean;
}

export function StorageAlert() {
  const { user } = useOptionalAuth();
  const [alerts, setAlerts] = useState<StorageAlert[]>([]);

  useEffect(() => {
    if (!user) {
      setAlerts([]);
      return;
    }

    // Check storage status
    checkStorageStatus();

    // Listen for storage events
    const handleStorageEvent = (event: CustomEvent) => {
      const { type, message } = event.detail;
      
      const newAlert: StorageAlert = {
        id: Date.now().toString(),
        type,
        message,
        timestamp: Date.now()
      };

      setAlerts(prev => [...prev.filter(a => a.type !== type), newAlert]);
    };

    // Listen for storage quota exceeded
    window.addEventListener('storageQuotaExceeded', handleStorageEvent as EventListener);
    
    return () => {
      window.removeEventListener('storageQuotaExceeded', handleStorageEvent as EventListener);
    };
  }, [user]);

  const checkStorageStatus = async () => {
    if (!user) return;

    // Check if storage service is initialized before trying to use it
    if (!StorageService.isInitialized()) {
      // Storage not ready yet - this is normal on app startup or public pages
      return;
    }

    try {
      const storageInfo = await StorageService.getStorageInfo();
      
      // Create alert based on usage
      if (storageInfo.usagePercentage > 90) {
        const alert: StorageAlert = {
          id: 'storage-critical',
          type: 'critical',
          message: `Storage is critically full (${storageInfo.usagePercentage.toFixed(1)}%). Clear cache to continue.`,
          timestamp: Date.now()
        };
        setAlerts(prev => [...prev.filter(a => a.id !== 'storage-critical'), alert]);
      } else if (storageInfo.usagePercentage > 80) {
        const alert: StorageAlert = {
          id: 'storage-warning',
          type: 'warning',
          message: `Storage usage is high (${storageInfo.usagePercentage.toFixed(1)}%). Consider cleaning up old data.`,
          timestamp: Date.now()
        };
        setAlerts(prev => [...prev.filter(a => a.id !== 'storage-warning'), alert]);
      } else {
        // Remove existing alerts if storage is healthy
        setAlerts(prev => prev.filter(a => !['storage-critical', 'storage-warning'].includes(a.id)));
      }
    } catch (error) {
      // Storage service not initialized is expected on public pages
      if (error instanceof Error && error.message.includes('Storage service not initialized')) {
        // Silently return - this is expected behavior on public pages
        return;
      }
      console.error('Failed to check storage status:', error);
    }
  };

  const dismissAlert = (id: string) => {
    setAlerts(prev => prev.filter(a => a.id !== id));
  };

  const handleCleanup = async () => {
    if (!user || !StorageService.isInitialized()) return;

    try {
      await StorageService.performCleanup();
      // Refresh storage status after cleanup
      await checkStorageStatus();
    } catch (error) {
      console.error('Cleanup failed:', error);
    }
  };

  const handleClearCache = async () => {
    if (!user || !StorageService.isInitialized()) return;

    try {
      await FirestoreStorageService.clearLocalCache();
      // Refresh storage status after clearing cache
      await checkStorageStatus();
    } catch (error) {
      console.error('Clear cache failed:', error);
    }
  };

  const getAlertIcon = (type: string) => {
    switch (type) {
      case 'critical':
        return <AlertTriangle className="h-4 w-4" />;
      case 'warning':
        return <HardDrive className="h-4 w-4" />;
      case 'quota-exceeded':
        return <CloudUpload className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getAlertVariant = (type: string): "default" | "destructive" => {
    return type === 'critical' ? 'destructive' : 'default';
  };

  if (alerts.length === 0) return null;

  return (
    <div className="fixed bottom-4 right-4 z-50 space-y-2 max-w-md">
      {alerts.map((alert) => (
        <Alert 
          key={alert.id} 
          variant={getAlertVariant(alert.type)}
          className="shadow-lg"
        >
          {getAlertIcon(alert.type)}
          <AlertDescription className="flex items-center justify-between">
            <span className="pr-2">{alert.message}</span>
            <div className="flex items-center gap-1">
              {alert.type === 'critical' && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleClearCache}
                  className="h-6 px-2 text-xs"
                >
                  <Trash2 className="h-3 w-3 mr-1" />
                  Clear Cache
                </Button>
              )}
              {alert.type === 'warning' && (
                <Button
                  size="sm"
                  variant="outline"
                  onClick={handleCleanup}
                  className="h-6 px-2 text-xs"
                >
                  <Trash2 className="h-3 w-3 mr-1" />
                  Cleanup
                </Button>
              )}
              <Button
                size="sm"
                variant="ghost"
                onClick={() => dismissAlert(alert.id)}
                className="h-6 w-6 p-0"
              >
                <X className="h-3 w-3" />
              </Button>
            </div>
          </AlertDescription>
        </Alert>
      ))}
    </div>
  );
} 
'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Separator } from '@/components/ui/separator';
import { 
  HardDrive, 
  Trash2, 
  Settings, 
  RefreshCw, 
  AlertTriangle, 
  CheckCircle,
  Info,
  Zap,
  Cloud
} from 'lucide-react';
import { StorageService, type StorageSettings } from '@/lib/services/storage/storage-adapter';
import { FirestoreStorageService } from '@/lib/services/storage/firestore-storage';
import { FirebaseStorageService } from '@/lib/services/storage/firebase-storage-service';
import { useAuth } from '@/components/auth';

// Define StorageInfo interface for compatibility
interface StorageInfo {
  currentSize: number;
  maxSize: number;
  usagePercentage: number;
  itemCount: number;
  canSave: boolean;
}

interface StorageManagerProps {
  onStorageChange?: () => void;
}

export function StorageManager({ onStorageChange }: StorageManagerProps) {
  const { user } = useAuth();
  const [storageInfo, setStorageInfo] = useState<StorageInfo | null>(null);
  const [storageSettings, setStorageSettings] = useState<StorageSettings | null>(null);
  const [isSettingsOpen, setIsSettingsOpen] = useState(false);
  const [isCleanupRunning, setIsCleanupRunning] = useState(false);
  const [lastCleanupResult, setLastCleanupResult] = useState<{ cleaned: number; errors: string[] } | null>(null);
  const [tempSettings, setTempSettings] = useState<StorageSettings | null>(null);
  const [cleanupDialog, setCleanupDialog] = useState<{
    isOpen: boolean;
    type: 'auto' | 'emergency' | 'optimize';
    title: string;
    description: string;
    action: () => Promise<void>;
  }>({
    isOpen: false,
    type: 'auto',
    title: '',
    description: '',
    action: async () => {}
  });

  useEffect(() => {
    loadStorageInfo();
    loadStorageSettings();
    
    // Listen for storage events
    const handleStorageUpdate = () => {
      loadStorageInfo();
    };

    window.addEventListener('storageUpdated', handleStorageUpdate);
    
    return () => {
      window.removeEventListener('storageUpdated', handleStorageUpdate);
    };
  }, []);

  const loadStorageInfo = async () => {
    if (!user) return;
    
    try {
      const [firestoreInfo, storageUsage] = await Promise.all([
        StorageService.getStorageInfo(),
        FirebaseStorageService.getUserStorageUsage(user.uid)
      ]);
      
      // Combine Firestore and Firebase Storage usage
      const totalUsed = firestoreInfo.currentSize + storageUsage.totalSize;
      const maxSize = firestoreInfo.maxSize + (5 * 1024 * 1024 * 1024); // 5GB for Firebase Storage
      
      setStorageInfo({
        currentSize: totalUsed,
        maxSize: maxSize,
        usagePercentage: (totalUsed / maxSize) * 100,
        itemCount: storageUsage.imageCount,
        canSave: firestoreInfo.canSave
      });
    } catch (error) {
      console.error('Failed to load storage info:', error);
      // Fallback to default values
      setStorageInfo({
        currentSize: 0,
        maxSize: 100 * 1024 * 1024, // 100MB Firestore only
        usagePercentage: 0,
        itemCount: 0,
        canSave: true
      });
    }
  };

  const loadStorageSettings = async () => {
    if (!user) return;
    
    try {
      const settings = await FirestoreStorageService.getSettings(user.uid);
      setStorageSettings(settings);
      setTempSettings(settings);
    } catch (error) {
      console.error('Failed to load storage settings:', error);
      // Use fallback settings
      const fallbackSettings: StorageSettings = {
        cacheSize: 100 * 1024 * 1024, // 100MB Firestore cache
        compressionEnabled: true,
        showStorageWarnings: true,
        daysToKeepCompleted: 30,
        userId: user?.uid || ''
      };
      setStorageSettings(fallbackSettings);
      setTempSettings(fallbackSettings);
    }
  };

  const formatBytes = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getStorageStatusColor = (percentage: number): string => {
    if (percentage < 70) return 'text-green-600';
    if (percentage < 90) return 'text-yellow-600';
    return 'text-red-600';
  };

  const getStorageStatusBadge = (percentage: number) => {
    if (percentage < 70) {
      return <Badge variant="outline" className="text-green-600 border-green-600">Excellent</Badge>;
    }
    if (percentage < 90) {
      return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Good</Badge>;
    }
    return <Badge variant="outline" className="text-red-600 border-red-600">Full</Badge>;
  };

  const showCleanupDialog = (type: 'auto' | 'emergency' | 'optimize') => {
    const dialogs = {
      auto: {
        title: 'Cleanup Old Data',
        description: 'Remove old completed checklists and optimize cache. This will free up storage space.',
        action: performCleanup
      },
      emergency: {
        title: 'Clear Local Cache',
        description: 'This will clear local cache. Data in Firestore will remain safe and will be re-downloaded when needed.',
        action: performEmergencyCleanup
      },
      optimize: {
        title: 'Optimize Storage',
        description: 'Compress and optimize stored data to improve performance and reduce storage usage.',
        action: performOptimization
      }
    };

    setCleanupDialog({
      isOpen: true,
      type,
      ...dialogs[type]
    });
  };

  const performCleanup = async () => {
    if (!user) return;
    
    try {
      // Clean up both Firestore data and Firebase Storage images
      const [firestoreResult, storageResult] = await Promise.all([
        StorageService.performCleanup(),
        FirebaseStorageService.cleanupOldImages(user.uid, 30)
      ]);
      
      const combinedResult = {
        cleaned: firestoreResult.cleaned + storageResult.cleaned,
        errors: [...firestoreResult.errors, ...storageResult.errors]
      };
      
      setLastCleanupResult(combinedResult);
      await loadStorageInfo();
      onStorageChange?.();
    } catch (error) {
      console.error('Cleanup failed:', error);
      setLastCleanupResult({ 
        cleaned: 0, 
        errors: [error instanceof Error ? error.message : 'Unknown error'] 
      });
    }
  };

  const performEmergencyCleanup = async () => {
    if (!user) return;
    
    try {
      await FirestoreStorageService.clearLocalCache();
      await loadStorageInfo();
      onStorageChange?.();
      setLastCleanupResult({ cleaned: 1, errors: [] });
    } catch (error) {
      console.error('Emergency cleanup failed:', error);
      setLastCleanupResult({ 
        cleaned: 0, 
        errors: [error instanceof Error ? error.message : 'Failed to clear cache'] 
      });
    }
  };

  const performOptimization = async () => {
    try {
      await FirestoreStorageService.refreshCache();
      await loadStorageInfo();
      onStorageChange?.();
      setLastCleanupResult({ cleaned: 1, errors: [] });
    } catch (error) {
      console.error('Optimization failed:', error);
      setLastCleanupResult({ 
        cleaned: 0, 
        errors: [error instanceof Error ? error.message : 'Optimization failed'] 
      });
    }
  };

  const executeCleanupAction = async () => {
    setIsCleanupRunning(true);
    await cleanupDialog.action();
    setIsCleanupRunning(false);
    setCleanupDialog({ ...cleanupDialog, isOpen: false });
  };

  const handleSettingsSave = async () => {
    if (tempSettings && user) {
      try {
        await FirestoreStorageService.updateSettings(user.uid, tempSettings);
        setStorageSettings(tempSettings);
        setIsSettingsOpen(false);
      } catch (error) {
        console.error('Failed to save settings:', error);
        alert('Failed to save settings. Please try again.');
      }
    }
  };

  const handleSettingsCancel = () => {
    setTempSettings(storageSettings);
    setIsSettingsOpen(false);
  };

  if (!storageInfo || !storageSettings) {
    return (
      <div className="flex items-center justify-center p-8">
        <RefreshCw className="h-6 w-6 animate-spin mr-2" />
        Loading storage information...
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Storage Overview */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-base font-medium flex items-center gap-2">
            <Cloud className="h-5 w-5" />
                            Cloud Storage (Database + Files)
          </CardTitle>
          {getStorageStatusBadge(storageInfo.usagePercentage)}
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Usage</span>
              <span className={getStorageStatusColor(storageInfo.usagePercentage)}>
                {formatBytes(storageInfo.currentSize)} / {formatBytes(storageInfo.maxSize)}
              </span>
            </div>
            <Progress 
              value={Math.min(storageInfo.usagePercentage, 100)} 
              className="h-2"
            />
            <div className="text-xs text-muted-foreground">
              {storageInfo.usagePercentage.toFixed(1)}% used
            </div>
          </div>

          <div className="flex flex-wrap gap-2">
            <Button
              size="sm"
              variant="outline"
              onClick={() => showCleanupDialog('auto')}
              disabled={isCleanupRunning}
            >
              <Trash2 className="h-4 w-4 mr-1" />
              Cleanup
            </Button>
            
            <Button
              size="sm"
              variant="outline"
              onClick={() => showCleanupDialog('optimize')}
              disabled={isCleanupRunning}
            >
              <Zap className="h-4 w-4 mr-1" />
              Optimize
            </Button>

            <Button
              size="sm"
              variant="outline"
              onClick={() => setIsSettingsOpen(true)}
            >
              <Settings className="h-4 w-4 mr-1" />
              Settings
            </Button>
          </div>

          {lastCleanupResult && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                {lastCleanupResult.errors.length === 0 
                  ? `Successfully cleaned ${lastCleanupResult.cleaned} items`
                  : `Cleanup completed with ${lastCleanupResult.errors.length} errors`
                }
              </AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {/* Storage Warnings */}
      {storageInfo.usagePercentage > 80 && (
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>
            Storage usage is high ({storageInfo.usagePercentage.toFixed(1)}%). 
            Consider running cleanup or clearing old data.
          </AlertDescription>
        </Alert>
      )}

      {/* Cleanup Confirmation Dialog */}
      <Dialog open={cleanupDialog.isOpen} onOpenChange={(open) => 
        setCleanupDialog({ ...cleanupDialog, isOpen: open })
      }>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>{cleanupDialog.title}</DialogTitle>
            <DialogDescription>
              {cleanupDialog.description}
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setCleanupDialog({ ...cleanupDialog, isOpen: false })}
              disabled={isCleanupRunning}
            >
              Cancel
            </Button>
            <Button
              onClick={executeCleanupAction}
              disabled={isCleanupRunning}
            >
              {isCleanupRunning ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Processing...
                </>
              ) : (
                'Confirm'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Settings Dialog */}
      <Dialog open={isSettingsOpen} onOpenChange={setIsSettingsOpen}>
        <DialogContent className="max-w-md">
          <DialogHeader>
            <DialogTitle>Storage Settings</DialogTitle>
            <DialogDescription>
              Configure storage behavior and limits
            </DialogDescription>
          </DialogHeader>

          {tempSettings && (
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="cacheSize">Cache Size (MB)</Label>
                <Input
                  id="cacheSize"
                  type="number"
                  value={Math.round(tempSettings.cacheSize / (1024 * 1024))}
                  onChange={(e) => setTempSettings({
                    ...tempSettings,
                    cacheSize: parseInt(e.target.value) * 1024 * 1024
                  })}
                  min="50"
                  max="500"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="daysToKeepCompleted">Days to Keep Completed (days)</Label>
                <Input
                  id="daysToKeepCompleted"
                  type="number"
                  value={tempSettings.daysToKeepCompleted || 30}
                  onChange={(e) => setTempSettings({
                    ...tempSettings,
                    daysToKeepCompleted: parseInt(e.target.value)
                  })}
                  min="1"
                  max="365"
                />
              </div>

              <Separator />

              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <Label htmlFor="autoSync">Auto Sync</Label>
                  <Switch
                    id="autoSync"
                    checked={tempSettings.autoSync}
                    onCheckedChange={(checked) => setTempSettings({
                      ...tempSettings,
                      autoSync: checked
                    })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="compressionEnabled">Compression</Label>
                  <Switch
                    id="compressionEnabled"
                    checked={tempSettings.compressionEnabled}
                    onCheckedChange={(checked) => setTempSettings({
                      ...tempSettings,
                      compressionEnabled: checked
                    })}
                  />
                </div>

                <div className="flex items-center justify-between">
                  <Label htmlFor="showStorageWarnings">Storage Warnings</Label>
                  <Switch
                    id="showStorageWarnings"
                    checked={tempSettings.showStorageWarnings || true}
                    onCheckedChange={(checked) => setTempSettings({
                      ...tempSettings,
                      showStorageWarnings: checked
                    })}
                  />
                </div>
              </div>
            </div>
          )}

          <DialogFooter>
            <Button variant="outline" onClick={handleSettingsCancel}>
              Cancel
            </Button>
            <Button onClick={handleSettingsSave}>
              Save Settings
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 
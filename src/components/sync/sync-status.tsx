'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/components/auth/auth-provider';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { FirestoreStorageService } from '@/lib/services/storage/firestore-storage';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Wifi, 
  WifiOff, 
  Cloud, 
  CloudOff, 
  RefreshCw, 
  CheckCircle, 
  AlertTriangle,
  Clock,
  Info
} from 'lucide-react';

interface SyncStatusProps {
  onSyncTriggered?: () => void;
}

export function SyncStatus({ onSyncTriggered }: SyncStatusProps) {
  const { user } = useAuth();
  const { isOnline } = useNetworkStatus();
  const [syncStatus, setSyncStatus] = useState({
    inProgress: false,
    lastSync: null as string | null,
    hasChanges: false,
    isOnline: false,
    queueSize: 0
  });
  const [isRefreshing, setIsRefreshing] = useState(false);

  useEffect(() => {
    if (user) {
      updateSyncStatus();
      
      // Update sync status periodically
      const interval = setInterval(updateSyncStatus, 30000); // Every 30 seconds
      
      return () => clearInterval(interval);
    }
  }, [user, isOnline]);

  const updateSyncStatus = () => {
    const status = FirestoreStorageService.getSyncStatus();
    setSyncStatus({
      inProgress: status.inProgress,
      lastSync: status.lastSync,
      hasChanges: status.hasChanges,
      isOnline: isOnline,
      queueSize: status.queueSize
    });
  };

  const handleRefreshCache = async () => {
    if (!user) return;
    
    setIsRefreshing(true);
    try {
      await FirestoreStorageService.refreshCache();
      updateSyncStatus();
      onSyncTriggered?.();
    } catch (error) {
      console.error('Failed to refresh cache:', error);
    } finally {
      setIsRefreshing(false);
    }
  };

  const getSyncStatusBadge = () => {
    if (!isOnline) {
      return <Badge variant="outline" className="text-yellow-600 border-yellow-600">Offline</Badge>;
    }
    
    if (syncStatus.inProgress) {
      return <Badge variant="outline" className="text-blue-600 border-blue-600">Syncing</Badge>;
    }
    
    return <Badge variant="outline" className="text-green-600 border-green-600">Online</Badge>;
  };

  const getNetworkIcon = () => {
    return isOnline ? (
      <Wifi className="h-4 w-4 text-green-600" />
    ) : (
      <WifiOff className="h-4 w-4 text-red-600" />
    );
  };

  const getCloudIcon = () => {
    if (!isOnline) {
      return <CloudOff className="h-4 w-4 text-red-600" />;
    }
    
    if (syncStatus.inProgress) {
      return <RefreshCw className="h-4 w-4 text-blue-600 animate-spin" />;
    }
    
    return <Cloud className="h-4 w-4 text-green-600" />;
  };

  const formatLastSync = (lastSync: string | null) => {
    if (!lastSync) return 'Never';
    
    const date = new Date(lastSync);
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    
    if (diff < 60000) return 'Just now';
    if (diff < 3600000) return `${Math.floor(diff / 60000)}m ago`;
    if (diff < 86400000) return `${Math.floor(diff / 3600000)}h ago`;
    return date.toLocaleDateString();
  };

  if (!user) return null;

  return (
    <Card className="w-full">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-base font-medium flex items-center gap-2">
          {getCloudIcon()}
          Firestore Sync Status
        </CardTitle>
        {getSyncStatusBadge()}
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              {getNetworkIcon()}
              <span className="text-muted-foreground">Network</span>
            </div>
            <p className="font-medium">
              {isOnline ? 'Connected' : 'Offline'}
            </p>
          </div>
          
          <div className="space-y-1">
            <div className="flex items-center gap-2">
              <Clock className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">Last Sync</span>
            </div>
            <p className="font-medium">
              {formatLastSync(syncStatus.lastSync)}
            </p>
          </div>
        </div>

        {!isOnline && (
          <Alert>
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              You're offline. Changes will sync automatically when you reconnect.
            </AlertDescription>
          </Alert>
        )}

        {isOnline && (
          <Alert>
            <CheckCircle className="h-4 w-4" />
            <AlertDescription>
              Firestore automatically syncs your data in real-time across all devices.
            </AlertDescription>
          </Alert>
        )}

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefreshCache}
            disabled={isRefreshing || !isOnline}
          >
            {isRefreshing ? (
              <>
                <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                Refreshing...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh Cache
              </>
            )}
          </Button>
        </div>

        <div className="text-xs text-muted-foreground">
          <Info className="h-3 w-3 inline mr-1" />
          Firestore provides automatic real-time synchronization and offline persistence.
        </div>
      </CardContent>
    </Card>
  );
} 
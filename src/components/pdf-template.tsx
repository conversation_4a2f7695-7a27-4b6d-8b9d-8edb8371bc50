import { Checklist<PERSON><PERSON>, MechanicalCheck, ElectricalCheck, SequenceControlsCheck, CheckStatus } from '@/types/checklist';
import { ALL_CHECKS } from '@/config/checklist-fields';
import { getSectionFieldsWithValues, getDisplayValue } from '@/lib/utils/field-sorting';

interface PDFTemplateProps {
  checklist: ChecklistData;
  logoDataUrl?: string;
  beforeImageDataUrl?: string;
  afterImageDataUrl?: string;
  signatureDataUrl?: string;
}

export function PDFTemplate({ 
  checklist, 
  logoDataUrl,
  beforeImageDataUrl,
  afterImageDataUrl,
  signatureDataUrl
}: PDFTemplateProps) {
  // Calculate summary statistics
  const calculateStats = () => {
    const allItems = [
      ...Object.entries(checklist.mechanicalChecks),
      ...Object.entries(checklist.electricalChecks),
      ...Object.entries(checklist.sequenceControlsChecks)
    ];
    
    const stats = {
      OK: 0,
      Faulty: 0,
      'N/A': 0,
      Missing: 0,
      total: 0
    };
    
    allItems.forEach(([, value]) => {
      if (typeof value === 'string' && (value === 'OK' || value === 'Faulty' || value === 'N/A' || value === 'Missing')) {
        stats[value as CheckStatus]++;
        stats.total++;
      }
    });
    
    return stats;
  };

  const stats = calculateStats();
  const okPercentage = stats.total > 0 ? Math.round((stats.OK / stats.total) * 100) : 0;

  // Create compact pie chart
  const createPieChart = () => {
    if (stats.total === 0) {
      return (
        <div style={{
          width: '80px',
          height: '80px',
          margin: '0 auto 8px',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          borderRadius: '50%',
          backgroundColor: '#ffffff',
          border: '2px solid #e9ecef',
          color: '#6c757d',
          fontSize: '10px',
          fontWeight: 'bold'
        }}>
          No Data
        </div>
      );
    }

    const colors = {
      OK: '#28a745',
      Faulty: '#dc3545',
      'N/A': '#ffc107',
      Missing: '#6c757d'
    };

    const data = [
      { label: 'OK', value: stats.OK, color: colors.OK },
      { label: 'Faulty', value: stats.Faulty, color: colors.Faulty },
      { label: 'N/A', value: stats['N/A'], color: colors['N/A'] },
      { label: 'Missing', value: stats.Missing, color: colors.Missing }
    ].filter(item => item.value > 0);

    if (data.length === 1) {
      return (
        <div style={{ margin: '0 auto 8px', width: '80px', height: '80px', display: 'flex', justifyContent: 'center' }}>
          <svg width="80" height="80" viewBox="0 0 80 80">
            <circle
              cx="40"
              cy="40"
              r="35"
              fill={data[0].color}
              stroke="#fff"
              strokeWidth="2"
            />
            <circle
              cx="40"
              cy="40"
              r="18"
              fill="white"
              stroke="#e9ecef"
              strokeWidth="1"
            />
            <text
              x="40"
              y="38"
              textAnchor="middle"
              fontSize="12"
              fontWeight="bold"
              fill="#000"
            >
              100%
            </text>
            <text
              x="40"
              y="48"
              textAnchor="middle"
              fontSize="7"
              fill="#666"
            >
              {data[0].label}
            </text>
          </svg>
        </div>
      );
    }

    const radius = 35;
    const centerX = 40;
    const centerY = 40;
    let currentAngle = -Math.PI / 2;

    const slices = data.map(item => {
      const sliceAngle = (item.value / stats.total) * 2 * Math.PI;
      const startAngle = currentAngle;
      const endAngle = currentAngle + sliceAngle;
      
      const x1 = centerX + radius * Math.cos(startAngle);
      const y1 = centerY + radius * Math.sin(startAngle);
      const x2 = centerX + radius * Math.cos(endAngle);
      const y2 = centerY + radius * Math.sin(endAngle);
      
      const largeArcFlag = sliceAngle > Math.PI ? 1 : 0;
      
      const pathData = [
        `M ${centerX} ${centerY}`,
        `L ${x1} ${y1}`,
        `A ${radius} ${radius} 0 ${largeArcFlag} 1 ${x2} ${y2}`,
        'Z'
      ].join(' ');
      
      currentAngle = endAngle;
      
      return {
        pathData,
        color: item.color,
        percentage: Math.round((item.value / stats.total) * 100)
      };
    });

    return (
      <div style={{ margin: '0 auto 8px', width: '80px', height: '80px', display: 'flex', justifyContent: 'center' }}>
        <svg width="80" height="80" viewBox="0 0 80 80">
          {slices.map((slice, index) => (
            <path
              key={index}
              d={slice.pathData}
              fill={slice.color}
              stroke="#fff"
              strokeWidth="2"
            />
          ))}
          <circle
            cx={centerX}
            cy={centerY}
            r="18"
            fill="white"
            stroke="#e9ecef"
            strokeWidth="1"
          />
          <text
            x={centerX}
            y={centerY - 3}
            textAnchor="middle"
            fontSize="12"
            fontWeight="bold"
            fill="#000"
          >
            {okPercentage}%
          </text>
          <text
            x={centerX}
            y={centerY + 8}
            textAnchor="middle"
            fontSize="7"
            fill="#666"
          >
            OK
          </text>
        </svg>
      </div>
    );
  };

  // Create a mapping from field keys to proper labels
  const getFieldLabel = (fieldKey: string): string => {
    const field = ALL_CHECKS.find(check => check.key === fieldKey);
    return field ? field.label : fieldKey;
  };

  const renderChecklistSection = (title: string, data: MechanicalCheck | ElectricalCheck | SequenceControlsCheck, section: 'mechanical' | 'electrical' | 'sequence') => {
    // Get all fields with values, including empty fields as "N/A"
    const items = getSectionFieldsWithValues(data, section, true);

    const getStatusColor = (value: string | number | boolean) => {
      if (typeof value === 'string') {
        switch (value) {
          case 'OK': return { color: '#28a745', bg: '#e8f5e8', border: '#28a745' };
          case 'Faulty': return { color: '#dc3545', bg: '#fdeaea', border: '#dc3545' };
          case 'N/A': return { color: '#856404', bg: '#fff3cd', border: '#ffc107' };
          case 'Missing': return { color: '#6c757d', bg: '#ffffff', border: '#6c757d' };
        }
      }
      // For numerical or other values
      return { color: '#0066cc', bg: '#e6f3ff', border: '#0066cc' };
    };

    return (
      <div style={{
        backgroundColor: 'white',
        border: '1px solid #e9ecef',
        borderRadius: '8px',
        overflow: 'hidden'
      }}>
        <div className="pdf-red-header" style={{
          backgroundColor: '#CC0000',
          color: 'white',
          padding: '10px 12px',
          fontSize: '13px',
          fontWeight: '700',
          textAlign: 'center',
          letterSpacing: '0.2px',
          borderRadius: '6px 6px 0 0'
        }}>
          {title}
        </div>
        <div style={{
          padding: '8px',
          maxHeight: '680px',
          overflowY: 'auto',
          backgroundColor: 'white'
        }}>
          {items.map((item, index) => {
            const displayValue = getDisplayValue(item.value, true);
            const statusStyle = getStatusColor(item.value);
            
            return (
              <div key={index} style={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'space-between',
                padding: '5px 0',
                fontSize: '10.5px',
                borderBottom: index < items.length - 1 ? '1px solid #e9ecef' : 'none',
                minHeight: '24px'
              }}>
                <span style={{ 
                  flex: 1, 
                  lineHeight: '1.3',
                  wordWrap: 'break-word',
                  overflowWrap: 'break-word',
                  hyphens: 'auto',
                  color: '#212529',
                  fontWeight: '500',
                  marginRight: '8px'
                }}>
                  {item.label}
                </span>
                <span style={{
                  color: statusStyle.color,
                  fontWeight: '700',
                  fontSize: '10px',
                  flexShrink: 0
                }}>
                  {displayValue}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  return (
    <>
      <style dangerouslySetInnerHTML={{
        __html: `
          @media print {
            * {
              -webkit-print-color-adjust: exact !important;
              color-adjust: exact !important;
              print-color-adjust: exact !important;
            }
            .pdf-container {
              page-break-inside: avoid !important;
              break-inside: avoid !important;
            }
          }
          @page {
            size: A4;
            margin: 0 12mm 12mm 12mm;
          }
          .pdf-red-header {
            background-color: #CC0000 !important;
            color: white !important;
          }
        `
      }} />

      <div className="pdf-container" style={{
        fontFamily: 'Arial, "Helvetica Neue", Helvetica, sans-serif',
        fontSize: '10px',
        lineHeight: '1.3',
        color: '#212529',
        backgroundColor: '#fff',
        width: '210mm',
        height: '297mm',
        margin: '0 auto',
        padding: '5mm 10mm 10mm 10mm',
        boxSizing: 'border-box',
        display: 'flex',
        flexDirection: 'column'
      }}>
        {/* Modern Header */}
        <div style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          marginBottom: '10px',
          padding: '8px 12px',
          borderBottom: '3px solid #CC0000',
          backgroundColor: '#ffffff',
          borderRadius: '4px 4px 0 0'
        }}>
          <div>
            <h1 style={{
              fontSize: '18px',
              fontWeight: '700',
              color: '#CC0000',
              margin: '0 0 3px 0',
              letterSpacing: '-0.2px'
            }}>
              PPM Ventilation System Checklist
            </h1>
            <p style={{
              fontSize: '11px',
              color: '#666666',
              margin: '0',
              fontWeight: '600'
            }}>
              Preventive Maintenance Inspection Report
            </p>
          </div>
          <div style={{
            width: '200px',
            height: '90px',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            overflow: 'hidden',
            borderRadius: '6px',
            backgroundColor: 'transparent'
          }}>
            <img 
              src={logoDataUrl || "/images/logo.jpeg"} 
              alt="Auburn Engineering"
              style={{
                maxWidth: '100%',
                maxHeight: '100%',
                objectFit: 'contain',
                border: 'none',
                background: 'transparent'
              }}
            />
          </div>
        </div>

        {/* Compact Information Grid */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: 'repeat(3, 1fr)',
          gap: '10px',
          marginBottom: '12px',
          fontSize: '11px'
        }}>
          <div style={{
            backgroundColor: '#ffffff',
            padding: '10px',
            borderRadius: '6px',
            border: '1px solid #e9ecef'
          }}>
            <div style={{ marginBottom: '4px', display: 'flex' }}>
              <span style={{ fontWeight: '700', color: '#495057', minWidth: '55px' }}>Client:</span>
              <span style={{ color: '#212529', fontWeight: '600' }}>{checklist.generalInfo.clientName}</span>
            </div>
            <div style={{ marginBottom: '4px', display: 'flex' }}>
              <span style={{ fontWeight: '700', color: '#495057', minWidth: '55px' }}>Building:</span>
              <span style={{ color: '#212529', fontWeight: '600' }}>{checklist.generalInfo.building}</span>
            </div>
            <div style={{ display: 'flex' }}>
              <span style={{ fontWeight: '700', color: '#495057', minWidth: '55px' }}>Inspector:</span>
              <span style={{ color: '#212529', fontWeight: '600' }}>{checklist.generalInfo.inspectedBy}</span>
            </div>
          </div>
          <div style={{
            backgroundColor: '#ffffff',
            padding: '10px',
            borderRadius: '6px',
            border: '1px solid #e9ecef'
          }}>
            <div style={{ marginBottom: '4px', display: 'flex' }}>
              <span style={{ fontWeight: '700', color: '#495057', minWidth: '55px' }}>Equipment:</span>
              <span style={{ color: '#212529', fontWeight: '600' }}>{checklist.generalInfo.equipmentName}</span>
            </div>
            <div style={{ marginBottom: '4px', display: 'flex' }}>
              <span style={{ fontWeight: '700', color: '#495057', minWidth: '55px' }}>Location:</span>
              <span style={{ color: '#212529', fontWeight: '600' }}>{checklist.generalInfo.location}</span>
            </div>
            <div style={{ display: 'flex' }}>
              <span style={{ fontWeight: '700', color: '#495057', minWidth: '55px' }}>Tag No:</span>
              <span style={{ color: '#212529', fontWeight: '600' }}>{checklist.generalInfo.tagNo}</span>
            </div>
          </div>
          <div style={{
            backgroundColor: '#ffffff',
            padding: '10px',
            borderRadius: '6px',
            border: '1px solid #e9ecef'
          }}>
            <div style={{ marginBottom: '4px', display: 'flex' }}>
              <span style={{ fontWeight: '700', color: '#495057', minWidth: '55px' }}>Date:</span>
              <span style={{ color: '#212529', fontWeight: '600' }}>{checklist.generalInfo.date}</span>
            </div>
            <div style={{ marginBottom: '4px', display: 'flex' }}>
              <span style={{ fontWeight: '700', color: '#495057', minWidth: '55px' }}>PPM:</span>
              <span style={{ color: '#212529', fontWeight: '600' }}>{checklist.generalInfo.ppmAttempt}</span>
            </div>
            <div style={{ display: 'flex' }}>
              <span style={{ fontWeight: '700', color: '#495057', minWidth: '55px' }}>Approved:</span>
              <span style={{ color: '#212529', fontWeight: '600' }}>{checklist.generalInfo.approvedBy}</span>
            </div>
          </div>
        </div>

        {/* Main Content Grid - Updated Layout */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: '1fr 1fr 1fr',
          gap: '10px',
          marginBottom: '12px'
        }}>
          {/* Checklists */}
          {renderChecklistSection('Mechanical List', checklist.mechanicalChecks, 'mechanical')}
          {renderChecklistSection('Electrical List', checklist.electricalChecks, 'electrical')}
          
          {/* Sequence/Controls with Chart */}
          <div style={{ display: 'flex', flexDirection: 'column', gap: '10px' }}>
            {renderChecklistSection('Sequence/Controls List', checklist.sequenceControlsChecks, 'sequence')}
            
            {            /* Status Chart under Sequence/Controls */}
            <div style={{
              backgroundColor: 'white',
              border: '1px solid #e9ecef',
              borderRadius: '8px',
              overflow: 'hidden'
            }}>
              <div className="pdf-red-header" style={{
                backgroundColor: '#CC0000',
                color: 'white',
                padding: '10px 12px',
                fontSize: '12px',
                fontWeight: '700',
                textAlign: 'center',
                letterSpacing: '0.2px',
                borderRadius: '6px 6px 0 0'
              }}>
                Status Overview
              </div>
              <div style={{
                padding: '12px',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                gap: '8px'
              }}>
                {/* Pie Chart */}
                <div style={{ textAlign: 'center' }}>
                  {createPieChart()}
                  <div style={{
                    fontSize: '7px',
                    color: '#6c757d',
                    marginTop: '4px'
                  }}>
                    Overall Status
                  </div>
                </div>
                
                {/* Status Legend */}
                <div style={{
                  display: 'flex',
                  flexDirection: 'column',
                  gap: '4px',
                  width: '100%'
                }}>
                  <div style={{ display: 'flex', alignItems: 'center', fontSize: '10px' }}>
                    <div style={{ width: '12px', height: '12px', backgroundColor: '#28a745', marginRight: '6px', borderRadius: '2px' }}></div>
                    <span style={{ flex: 1, fontWeight: '600' }}>Passed:</span>
                    <span style={{ fontWeight: '700', color: '#28a745' }}>{stats.OK}</span>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', fontSize: '10px' }}>
                    <div style={{ width: '12px', height: '12px', backgroundColor: '#dc3545', marginRight: '6px', borderRadius: '2px' }}></div>
                    <span style={{ flex: 1, fontWeight: '600' }}>Failed:</span>
                    <span style={{ fontWeight: '700', color: '#dc3545' }}>{stats.Faulty}</span>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', fontSize: '10px' }}>
                    <div style={{ width: '12px', height: '12px', backgroundColor: '#ffc107', marginRight: '6px', borderRadius: '2px' }}></div>
                    <span style={{ flex: 1, fontWeight: '600' }}>N/A:</span>
                    <span style={{ fontWeight: '700', color: '#856404' }}>{stats['N/A']}</span>
                  </div>
                  <div style={{ display: 'flex', alignItems: 'center', fontSize: '10px' }}>
                    <div style={{ width: '12px', height: '12px', backgroundColor: '#6c757d', marginRight: '6px', borderRadius: '2px' }}></div>
                    <span style={{ flex: 1, fontWeight: '600' }}>Missing:</span>
                    <span style={{ fontWeight: '700', color: '#6c757d' }}>{stats.Missing}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>



        {/* Before and After Images Section */}
        <div style={{
          marginBottom: '8px'
        }}>
          <div style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr',
            gap: '10px'
          }}>
            {/* Before Image */}
            <div>
              <h4 style={{
                fontSize: '12px',
                fontWeight: '700',
                margin: '0 0 6px 0',
                color: '#CC0000',
                textAlign: 'center'
              }}>
                Before Image
              </h4>
              <div style={{
                border: '1px solid #e9ecef',
                borderRadius: '6px',
                height: '198px',
                backgroundColor: '#ffffff',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '10px',
                color: '#6c757d',
                overflow: 'hidden'
              }}>
                {beforeImageDataUrl ? (
                  <img 
                    src={beforeImageDataUrl} 
                    alt="Before"
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const parent = target.parentElement;
                      if (parent) {
                        parent.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; font-size: 10px; color: #6c757d;">Image unavailable</div>';
                      }
                    }}
                  />
                ) : (
                  'No image available'
                )}
              </div>
            </div>

            {/* After Image */}
            <div>
              <h4 style={{
                fontSize: '12px',
                fontWeight: '700',
                margin: '0 0 6px 0',
                color: '#CC0000',
                textAlign: 'center'
              }}>
                After Image
              </h4>
              <div style={{
                border: '1px solid #e9ecef',
                borderRadius: '6px',
                height: '198px',
                backgroundColor: '#ffffff',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                fontSize: '10px',
                color: '#6c757d',
                overflow: 'hidden'
              }}>
                {afterImageDataUrl ? (
                  <img 
                    src={afterImageDataUrl} 
                    alt="After"
                    style={{
                      width: '100%',
                      height: '100%',
                      objectFit: 'cover'
                    }}
                    onError={(e) => {
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      const parent = target.parentElement;
                      if (parent) {
                        parent.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; font-size: 10px; color: #6c757d;">Image unavailable</div>';
                      }
                    }}
                  />
                ) : (
                  'No image available'
                )}
              </div>
            </div>
          </div>
        </div>

        {/* Remarks and Signature Section */}
        <div style={{
          display: 'grid',
          gridTemplateColumns: '2fr 1fr',
          gap: '10px',
          marginTop: 'auto'
        }}>
          {/* Remarks */}
          <div style={{
            padding: '8px 0'
          }}>
            <h4 style={{
              fontSize: '12px',
              fontWeight: '700',
              margin: '0 0 6px 0',
              color: '#CC0000'
            }}>
              Remarks
            </h4>
            <div style={{
              fontSize: '11px',
              color: '#495057',
              lineHeight: '1.4',
              minHeight: '35px'
            }}>
              {checklist.remarks || 'No remarks provided'}
            </div>
          </div>

          {/* Signature */}
          <div>
            <h4 style={{
              fontSize: '12px',
              fontWeight: '700',
              margin: '0 0 6px 0',
              color: '#CC0000',
              textAlign: 'center'
            }}>
              Engineer Signature
            </h4>
            <div style={{
              borderRadius: '4px',
              height: '60px',
              backgroundColor: 'transparent',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              fontSize: '9px',
              color: '#6c757d',
              overflow: 'hidden'
            }}>
              {signatureDataUrl ? (
                <img 
                  src={signatureDataUrl} 
                  alt="Signature"
                  style={{
                    width: '100%',
                    height: '100%',
                    objectFit: 'contain',
                    border: 'none',
                    background: 'transparent'
                  }}
                  onError={(e) => {
                    const target = e.target as HTMLImageElement;
                    target.style.display = 'none';
                    const parent = target.parentElement;
                    if (parent) {
                      parent.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; font-size: 9px; color: #6c757d;">Signature unavailable</div>';
                    }
                  }}
                />
              ) : (
                'Signature pending'
              )}
            </div>
          </div>
        </div>
      </div>
    </>
  );
} 
'use client';

import React, { useState, useMemo } from 'react';
import { motion } from 'framer-motion';
import { ChecklistData } from '@/types/checklist';
import {
  calculateStatusDistribution,
  calculateTrendData,
  calculateCategoryPerformance,
  calculateEquipmentHealthScore,
  getFailureAnalysis
} from '@/lib/utils/data/chart-data-processor';
import {
  InspectionStatusChart,
  InspectionTrendsChart,
  CategoryPerformanceChart,
  EquipmentHealthGauge
} from '@/components/charts';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  BarChart3, 
  TrendingUp, 
  AlertTriangle, 
  Activity,
  RefreshCw,
  Download,
  Filter
} from 'lucide-react';

interface DashboardAnalyticsProps {
  checklists: ChecklistData[];
  loading?: boolean;
  className?: string;
}

interface FailureInsightProps {
  failures: ReturnType<typeof getFailureAnalysis>;
}

const FailureInsights: React.FC<FailureInsightProps> = ({ failures }) => {
  const topFailures = failures.slice(0, 5);

  if (topFailures.length === 0) {
    return (
      <Card className="glass border-border/50">
        <CardHeader>
          <CardTitle className="text-lg font-semibold flex items-center gap-2">
            <AlertTriangle className="h-5 w-5 text-yellow-500" />
            Failure Analysis
          </CardTitle>
          <CardDescription>Common issues and failure patterns</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mx-auto mb-4">
              <span className="text-2xl">✅</span>
            </div>
            <p className="text-muted-foreground">No significant failure patterns detected</p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="glass border-border/50">
      <CardHeader>
        <CardTitle className="text-lg font-semibold flex items-center gap-2">
          <AlertTriangle className="h-5 w-5 text-yellow-500" />
          Failure Analysis
        </CardTitle>
        <CardDescription>
          Top issues requiring attention • {failures.length} patterns identified
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {topFailures.map((failure, index) => (
            <motion.div
              key={`${failure.section}-${failure.field}`}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: index * 0.1 }}
              className="flex items-center justify-between p-3 bg-muted/50 rounded-lg"
            >
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <Badge variant="outline" className="text-xs">
                    {failure.section}
                  </Badge>
                  <span className={`text-sm font-medium ${
                    failure.failureRate >= 50 ? 'text-red-600' :
                    failure.failureRate >= 25 ? 'text-yellow-600' : 'text-orange-600'
                  }`}>
                    {failure.failureRate}% failure rate
                  </span>
                </div>
                <p className="text-sm text-foreground capitalize">
                  {failure.field.toLowerCase()}
                </p>
                <p className="text-xs text-muted-foreground">
                  {failure.failures} of {failure.totalChecks} checks failed
                </p>
              </div>
              <div className="flex items-center">
                <div className="w-16 bg-gray-200 rounded-full h-2 mr-3">
                  <div 
                    className={`h-2 rounded-full ${
                      failure.failureRate >= 50 ? 'bg-red-500' :
                      failure.failureRate >= 25 ? 'bg-yellow-500' : 'bg-orange-500'
                    }`}
                    style={{ width: `${failure.failureRate}%` }}
                  />
                </div>
                <span className="text-lg">{index === 0 ? '🔥' : index === 1 ? '⚠️' : '📊'}</span>
              </div>
            </motion.div>
          ))}
        </div>
        
        {failures.length > 5 && (
          <Button variant="outline" className="w-full mt-4">
            View All {failures.length} Issues
          </Button>
        )}
      </CardContent>
    </Card>
  );
};

export function DashboardAnalytics({ checklists, loading = false, className = '' }: DashboardAnalyticsProps) {
  const [timeRange, setTimeRange] = useState('30d');
  const [activeTab, setActiveTab] = useState('overview');

  // Memoized chart data calculations
  const chartData = useMemo(() => {
    if (loading || checklists.length === 0) {
      return {
        statusDistribution: [],
        trendData: [],
        categoryPerformance: [],
        equipmentHealth: { score: 0, grade: 'No Data', color: '#6b7280', trend: 'stable' as const },
        failureAnalysis: []
      };
    }

    return {
      statusDistribution: calculateStatusDistribution(checklists),
      trendData: calculateTrendData(checklists, timeRange),
      categoryPerformance: calculateCategoryPerformance(checklists),
      equipmentHealth: calculateEquipmentHealthScore(checklists),
      failureAnalysis: getFailureAnalysis(checklists)
    };
  }, [checklists, timeRange, loading]);

  // Quick stats
  const quickStats = useMemo(() => {
    if (checklists.length === 0) return null;

    const completedCount = checklists.filter(c => c.syncMetadata.status === 'synced').length;
    const pendingCount = checklists.filter(c => c.syncMetadata.status === 'pending').length;
    const totalChecks = chartData.categoryPerformance.reduce((sum, cat) => sum + cat.total, 0);
    const criticalIssues = chartData.failureAnalysis.filter(f => f.failureRate >= 50).length;

    return {
      completedCount,
      pendingCount,
      totalChecks,
      criticalIssues,
      successRate: completedCount > 0 ? Math.round((completedCount / checklists.length) * 100) : 0
    };
  }, [checklists, chartData]);

  if (loading) {
    return (
      <div className={`space-y-6 ${className}`}>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="glass border-border/50 animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-muted rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-muted rounded w-1/2"></div>
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {[...Array(4)].map((_, i) => (
            <Card key={i} className="glass border-border/50 animate-pulse">
              <CardContent className="p-6">
                <div className="h-64 bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (checklists.length === 0) {
    return (
      <div className={`${className}`}>
        <Card className="glass border-border/50">
          <CardContent className="p-12 text-center">
            <div className="w-24 h-24 bg-muted rounded-full flex items-center justify-center mx-auto mb-6">
              <BarChart3 className="h-12 w-12 text-muted-foreground" />
            </div>
            <h3 className="text-xl font-semibold mb-2">No Analytics Available</h3>
            <p className="text-muted-foreground mb-6">
              Complete some inspections to see detailed analytics and insights
            </p>
            <Button asChild>
              <a href="/checklist">Start Your First Inspection</a>
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div className="min-w-0 flex-1">
          <h2 className="text-2xl font-bold text-foreground">Analytics Dashboard</h2>
          <p className="text-muted-foreground">
            Comprehensive insights from {checklists.length} inspection{checklists.length !== 1 ? 's' : ''}
          </p>
        </div>
        <div className="flex items-center gap-2 flex-wrap sm:flex-nowrap">
          <Button variant="outline" size="sm" className="flex-1 sm:flex-none">
            <Filter className="h-4 w-4 sm:mr-2" />
            <span className="hidden sm:inline">Filters</span>
          </Button>
          <Button variant="outline" size="sm" className="flex-1 sm:flex-none">
            <Download className="h-4 w-4 sm:mr-2" />
            <span className="hidden sm:inline">Export</span>
          </Button>
          <Button variant="outline" size="sm" className="flex-1 sm:flex-none">
            <RefreshCw className="h-4 w-4 sm:mr-2" />
            <span className="hidden sm:inline">Refresh</span>
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      {quickStats && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4"
        >
          <Card className="glass border-border/50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Completed</p>
                  <p className="text-2xl font-bold text-green-600">{quickStats.completedCount}</p>
                </div>
                <Activity className="h-8 w-8 text-green-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="glass border-border/50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Success Rate</p>
                  <p className="text-2xl font-bold text-blue-600">{quickStats.successRate}%</p>
                </div>
                <TrendingUp className="h-8 w-8 text-blue-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="glass border-border/50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Checks</p>
                  <p className="text-2xl font-bold text-purple-600">{quickStats.totalChecks}</p>
                </div>
                <BarChart3 className="h-8 w-8 text-purple-500" />
              </div>
            </CardContent>
          </Card>
          
          <Card className="glass border-border/50">
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Critical Issues</p>
                  <p className={`text-2xl font-bold ${quickStats.criticalIssues > 0 ? 'text-red-600' : 'text-green-600'}`}>
                    {quickStats.criticalIssues}
                  </p>
                </div>
                <AlertTriangle className={`h-8 w-8 ${quickStats.criticalIssues > 0 ? 'text-red-500' : 'text-green-500'}`} />
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}

      {/* Main Charts */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="performance">Performance</TabsTrigger>
          <TabsTrigger value="insights">Insights</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <InspectionStatusChart 
              data={chartData.statusDistribution}
            />
            <EquipmentHealthGauge 
              score={chartData.equipmentHealth.score}
              grade={chartData.equipmentHealth.grade}
              color={chartData.equipmentHealth.color}
              trend={chartData.equipmentHealth.trend}
            />
          </div>
          
          <InspectionTrendsChart 
            data={chartData.trendData}
            timeRange={timeRange}
            onTimeRangeChange={setTimeRange}
          />
        </TabsContent>
        
        <TabsContent value="performance" className="space-y-6">
          <CategoryPerformanceChart 
            data={chartData.categoryPerformance}
          />
        </TabsContent>
        
        <TabsContent value="insights" className="space-y-6">
          <FailureInsights failures={chartData.failureAnalysis} />
        </TabsContent>
      </Tabs>
    </div>
  );
} 
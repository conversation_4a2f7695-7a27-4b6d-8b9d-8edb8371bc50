"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { 
  Spark<PERSON>, 
  Wand2, 
  AlertCircle, 
  CheckCircle, 
  Copy,
  RotateCcw,
  Settings
} from 'lucide-react';
import { useAI } from '@/hooks/useAI';
import { ChecklistFormData } from '@/lib/utils/validation';
import { RemarkGenerationOptions } from '@/lib/services/ai/ai-service';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

interface AIRemarksGeneratorProps {
  checklistData: Partial<ChecklistFormData>;
  currentRemarks: string;
  onRemarksChange: (remarks: string) => void;
  disabled?: boolean;
  placeholder?: string;
}

export function AIRemarksGenerator({
  checklistData,
  currentRemarks,
  onRemarksChange,
  disabled = false,
  placeholder = "Enter any additional remarks, observations, or recommendations..."
}: AIRemarksGeneratorProps) {
  const { isGenerating, error, generateRemarks, enhanceRemarks, clearError } = useAI();
  const [generatedRemarks, setGeneratedRemarks] = useState<string>('');
  const [showGenerated, setShowGenerated] = useState(false);

  const handleGenerateRemarks = async (options: RemarkGenerationOptions = {}) => {
    clearError();
    const result = await generateRemarks(checklistData, currentRemarks, options);
    
    if (result) {
      setGeneratedRemarks(result);
      setShowGenerated(true);
    }
  };

  const handleEnhanceRemarks = async () => {
    if (!currentRemarks.trim()) {
      return;
    }
    
    clearError();
    const result = await enhanceRemarks(currentRemarks, checklistData);
    
    if (result) {
      setGeneratedRemarks(result);
      setShowGenerated(true);
    }
  };

  const handleAcceptGenerated = () => {
    onRemarksChange(generatedRemarks);
    setShowGenerated(false);
    setGeneratedRemarks('');
  };

  const handleCopyGenerated = async () => {
    try {
      if (typeof window !== 'undefined' && navigator.clipboard) {
        await navigator.clipboard.writeText(generatedRemarks);
      }
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  const handleRegenerate = () => {
    handleGenerateRemarks({ tone: 'professional', includeRecommendations: true });
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="min-w-0 flex-1">
            <CardTitle className="flex items-center gap-2">
              <Sparkles className="h-5 w-5 text-primary" />
              AI-Powered Remarks
            </CardTitle>
            <CardDescription>
              Generate detailed inspection remarks (max 500 characters for PDF compatibility)
            </CardDescription>
          </div>
          
          <div className="flex items-center gap-2 flex-wrap sm:flex-nowrap">
            {/* Generation Options Dropdown */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" size="sm" disabled={disabled || isGenerating} className="flex-1 sm:flex-none">
                  <Settings className="h-4 w-4 sm:mr-2" />
                  <span className="hidden sm:inline">Options</span>
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end">
                <DropdownMenuLabel>Generation Style</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleGenerateRemarks({ tone: 'professional' })}>
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Professional
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleGenerateRemarks({ tone: 'technical' })}>
                  <Settings className="h-4 w-4 mr-2" />
                  Technical
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleGenerateRemarks({ tone: 'detailed' })}>
                  <AlertCircle className="h-4 w-4 mr-2" />
                  Detailed
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem onClick={() => handleGenerateRemarks({ 
                  tone: 'professional', 
                  includeRecommendations: false 
                })}>
                  Observations Only
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            {/* Main Generate Button */}
            <Button
              onClick={() => handleGenerateRemarks()}
              disabled={disabled || isGenerating}
              size="sm"
              className="bg-gradient-to-r from-primary to-primary/80 hover:from-primary/90 hover:to-primary/70 flex-1 sm:flex-none"
            >
              {isGenerating ? (
                <>
                  <Sparkles className="h-4 w-4 sm:mr-2 animate-spin" />
                  <span className="hidden sm:inline">Generating...</span>
                </>
              ) : (
                <>
                  <Wand2 className="h-4 w-4 sm:mr-2" />
                  <span className="hidden sm:inline">Generate Remarks</span>
                  <span className="sm:hidden">Generate</span>
                </>
              )}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Current Remarks Input */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <label className="text-sm font-medium">Your Remarks</label>
              {!currentRemarks.trim() && (
                <span className="inline-flex items-center px-2 py-1 rounded-lg text-xs font-medium bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300">
                  <span className="mr-1">?</span>
                  Missing
                </span>
              )}
            </div>
            {currentRemarks.trim() && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleEnhanceRemarks}
                disabled={disabled || isGenerating}
              >
                <Sparkles className="h-4 w-4 mr-2" />
                Enhance
              </Button>
            )}
          </div>
          <div className="relative">
            <Textarea
              value={currentRemarks}
              onChange={(e) => onRemarksChange(e.target.value)}
              placeholder={placeholder}
              className={`min-h-[100px] resize-none transition-all duration-200 ${
                !currentRemarks.trim() 
                  ? "border-amber-300 focus:border-amber-500 focus:ring-amber-500/20" 
                  : "border-green-300 focus:border-green-500 focus:ring-green-500/20"
              }`}
              disabled={disabled}
              maxLength={500}
            />
            {currentRemarks.trim() && (
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white shadow-sm" />
            )}
          </div>
          <div className="flex justify-between items-center text-xs text-muted-foreground">
            <span>Keep remarks concise for PDF export</span>
            <span className={`${currentRemarks.length > 500 ? 'text-red-600' : currentRemarks.length > 450 ? 'text-orange-600' : ''}`}>
              {currentRemarks.length}/500 characters
            </span>
          </div>
        </div>

        {/* Generated Remarks Display */}
        {showGenerated && generatedRemarks && (
          <>
            <Separator />
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary" className="bg-primary/10 text-primary">
                    <Sparkles className="h-3 w-3 mr-1" />
                    AI Generated
                  </Badge>
                </div>
                <div className="flex items-center gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleCopyGenerated}
                  >
                    <Copy className="h-4 w-4 mr-2" />
                    Copy
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleRegenerate}
                    disabled={isGenerating}
                  >
                    <RotateCcw className="h-4 w-4 mr-2" />
                    Regenerate
                  </Button>
                </div>
              </div>
              
              <div className="bg-muted/50 p-4 rounded-lg border-l-4 border-primary">
                <p className="text-sm whitespace-pre-wrap">{generatedRemarks}</p>
                <div className="mt-2 text-xs text-muted-foreground text-right">
                  <span className={`${generatedRemarks.length > 500 ? 'text-red-600' : generatedRemarks.length > 450 ? 'text-orange-600' : 'text-green-600'}`}>
                    {generatedRemarks.length}/500 characters
                  </span>
                </div>
              </div>
              
              <div className="flex justify-end gap-2">
                <Button
                  variant="outline"
                  onClick={() => setShowGenerated(false)}
                >
                  Dismiss
                </Button>
                <Button
                  onClick={handleAcceptGenerated}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle className="h-4 w-4 mr-2" />
                  Use This Remark
                </Button>
              </div>
            </div>
          </>
        )}

        {/* Quick Tips */}
        <div className="bg-blue-50 dark:bg-blue-950/20 p-3 rounded-lg">
          <p className="text-xs text-blue-700 dark:text-blue-300">
                                                   <strong>💡 Tips:</strong> AI generates detailed, professional remarks (≤500 chars) optimized for PDF export. 
               Use comprehensive technical descriptions. &quot;Enhance&quot; improves existing text, &quot;Generate&quot; creates new detailed remarks.
          </p>
        </div>
      </CardContent>
    </Card>
  );
} 
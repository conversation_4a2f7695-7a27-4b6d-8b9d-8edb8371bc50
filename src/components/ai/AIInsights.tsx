"use client";

import { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { 
  Brain, 
  Lightbulb, 
  AlertCircle, 
  TrendingUp,
  RefreshCw
} from 'lucide-react';
import { useAI } from '@/hooks/useAI';
import { ChecklistFormData } from '@/lib/utils/validation';

interface AIInsightsProps {
  checklistData: ChecklistFormData;
  className?: string;
}

export function AIInsights({ checklistData, className = '' }: AIInsightsProps) {
  const { isGenerating, error, generateInsights, clearError } = useAI();
  const [insights, setInsights] = useState<string>('');
  const [hasGenerated, setHasGenerated] = useState(false);

  const handleGenerateInsights = async () => {
    clearError();
    const result = await generateInsights(checklistData);
    
    if (result) {
      setInsights(result);
      setHasGenerated(true);
    }
  };

  const handleRefresh = () => {
    setInsights('');
    setHasGenerated(false);
    handleGenerateInsights();
  };

  return (
    <Card className={`${className}`}>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-primary" />
            <div>
              <CardTitle>AI Insights</CardTitle>
              <CardDescription>
                Intelligent analysis of your inspection data
              </CardDescription>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            {hasGenerated && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleRefresh}
                disabled={isGenerating}
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Refresh
              </Button>
            )}
            
            {!hasGenerated && (
              <Button
                onClick={handleGenerateInsights}
                disabled={isGenerating}
                size="sm"
                className="bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800"
              >
                {isGenerating ? (
                  <>
                    <Brain className="h-4 w-4 mr-2 animate-pulse" />
                    Analyzing...
                  </>
                ) : (
                  <>
                    <Lightbulb className="h-4 w-4 mr-2" />
                    Generate Insights
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {/* Loading State */}
        {isGenerating && (
          <div className="flex items-center justify-center py-8">
            <div className="text-center space-y-2">
              <Brain className="h-8 w-8 mx-auto text-primary animate-pulse" />
              <p className="text-sm text-muted-foreground">
                Analyzing inspection data...
              </p>
            </div>
          </div>
        )}

        {/* Insights Display */}
        {insights && !isGenerating && (
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <Badge variant="secondary" className="bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300">
                <Brain className="h-3 w-3 mr-1" />
                AI Analysis
              </Badge>
              <Badge variant="outline" className="text-xs">
                <TrendingUp className="h-3 w-3 mr-1" />
                Smart Insights
              </Badge>
            </div>
            
            <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 p-4 rounded-lg border-l-4 border-blue-500">
              <div className="prose prose-sm max-w-none">
                <p className="text-sm whitespace-pre-wrap leading-relaxed">
                  {insights}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* Empty State */}
        {!insights && !isGenerating && !hasGenerated && (
          <div className="text-center py-8 space-y-3">
            <div className="mx-auto w-16 h-16 bg-gradient-to-br from-blue-100 to-indigo-100 dark:from-blue-900/20 dark:to-indigo-900/20 rounded-full flex items-center justify-center">
              <Lightbulb className="h-8 w-8 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="font-medium text-sm">Ready for AI Analysis</h3>
              <p className="text-xs text-muted-foreground mt-1">
                Get intelligent insights about your inspection findings, recommendations, and system health assessment.
              </p>
            </div>
          </div>
        )}

        {/* Feature Info */}
        <div className="bg-amber-50 dark:bg-amber-950/20 p-3 rounded-lg">
          <p className="text-xs text-amber-700 dark:text-amber-300">
            <strong>🔍 AI Analysis:</strong> Our AI analyzes your inspection data to provide insights on system health, 
            identify potential issues, and suggest maintenance priorities based on industry best practices.
          </p>
        </div>
      </CardContent>
    </Card>
  );
} 
'use client';

import React, { useState } from 'react';
import { Dialog, DialogContent, DialogTitle, DialogDescription, VisuallyHidden } from '@/components/ui';
import { SignInForm } from './sign-in-form';
import { ResetPasswordForm } from './reset-password-form';

type AuthModalView = 'signin' | 'reset';

interface AuthModalProps {
  isOpen: boolean;
  onClose: () => void;
  defaultView?: AuthModalView;
}

export function AuthModal({ isOpen, onClose, defaultView = 'signin' }: AuthModalProps) {
  const [currentView, setCurrentView] = useState<AuthModalView>(defaultView);

  const handleSuccess = () => {
    onClose();
    // Reset to default view for next time
    setTimeout(() => setCurrentView('signin'), 300);
  };

  const handleOpenChange = (open: boolean) => {
    if (!open) {
      onClose();
      // Reset to default view when modal is closed
      setTimeout(() => setCurrentView('signin'), 300);
    }
  };

  const getModalTitle = () => {
    switch (currentView) {
      case 'reset':
        return 'Reset Password';
      default:
        return 'Sign In';
    }
  };

  const getModalDescription = () => {
    switch (currentView) {
      case 'reset':
        return 'Enter your email address to receive password reset instructions';
      default:
        return 'Sign in to your Auburn Engineering account to access checklists and reports';
    }
  };

  const renderContent = () => {
    switch (currentView) {
      case 'reset':
        return (
          <ResetPasswordForm
            onBack={() => setCurrentView('signin')}
          />
        );
      default:
        return (
          <SignInForm
            onSwitchToReset={() => setCurrentView('reset')}
            onSuccess={handleSuccess}
          />
        );
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={handleOpenChange}>
      <DialogContent className="sm:max-w-md p-0 bg-transparent border-none shadow-none">
        <VisuallyHidden>
          <DialogTitle>{getModalTitle()}</DialogTitle>
          <DialogDescription>{getModalDescription()}</DialogDescription>
        </VisuallyHidden>
        <div className="relative">
          {renderContent()}
        </div>
      </DialogContent>
    </Dialog>
  );
} 
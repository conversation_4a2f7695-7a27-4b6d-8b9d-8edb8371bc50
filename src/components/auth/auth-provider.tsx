'use client';

import React, { createContext, useContext, useEffect, useState, useRef } from 'react';
import { AuthContextType, AuthUser } from '@/types/auth';
import { ExtendedUser } from '@/types/user';
import { AuthService } from '@/lib/services/auth/auth';
import { AnalyticsService } from '@/lib/utils/analytics';
import { UserService } from '@/lib/services/auth/user-service';

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function useAuth(): AuthContextType {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}

interface AuthProviderProps {
  children: React.ReactNode;
}

// Global state to prevent redundant processing
let globalAuthState = {
  lastProcessedUserId: null as string | null,
  isProcessing: false,
  processedTimestamp: null as number | null
};

export function AuthProvider({ children }: AuthProviderProps) {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [extendedUser, setExtendedUser] = useState<ExtendedUser | null>(null);
  const [loading, setLoading] = useState(true);
  const [firebaseError, setFirebaseError] = useState<string | null>(null);
  const authListenerSetupRef = useRef(false);
  const processingRef = useRef(false);

  console.log('AuthProvider: Component render', { user: user?.uid, loading });

  useEffect(() => {
    // Prevent multiple auth listener setups
    if (authListenerSetupRef.current) {
      console.log('AuthProvider: Auth listener already set up, skipping');
      return;
    }

    console.log('AuthProvider: Setting up auth listener');
    authListenerSetupRef.current = true;
    
    // Set a timeout to prevent infinite loading
    const timeout = setTimeout(() => {
      console.warn('AuthProvider: Auth loading timeout reached - forcing loading to false');
      setLoading(false);
    }, 10000);
    
    try {
      console.log('AuthProvider: Calling AuthService.onAuthStateChanged');
      
      const unsubscribe = AuthService.onAuthStateChanged(async (user) => {
        console.log('AuthProvider: onAuthStateChanged callback fired', { 
          user: user ? { uid: user.uid, email: user.email } : null,
          timestamp: new Date().toISOString()
        });
        
        clearTimeout(timeout); // Clear timeout since auth state changed
        
        const newUserId = user?.uid || null;
        
        // Check if we should process this auth state change
        const shouldProcess = shouldProcessAuthStateChange(newUserId);
        
        if (!shouldProcess) {
          console.log('AuthProvider: Skipping redundant auth state processing', { 
            currentUserId: newUserId,
            lastProcessed: globalAuthState.lastProcessedUserId,
            isProcessing: globalAuthState.isProcessing 
          });
          
          // Still update the user state for UI consistency
          setUser(user);
          setLoading(false);
          return;
        }
        
        // Prevent concurrent processing
        if (processingRef.current) {
          console.log('AuthProvider: Already processing, skipping concurrent request');
          return;
        }
        
        processingRef.current = true;
        globalAuthState.isProcessing = true;
        
        console.log('AuthProvider: Processing auth state change', { 
          from: globalAuthState.lastProcessedUserId, 
          to: newUserId
        });
        
        try {
          await processAuthStateChange(user, newUserId);
        } finally {
          processingRef.current = false;
          globalAuthState.isProcessing = false;
          globalAuthState.lastProcessedUserId = newUserId;
          globalAuthState.processedTimestamp = Date.now();
        }
      });

      console.log('AuthProvider: Auth state listener set up successfully');

      return () => {
        console.log('AuthProvider: Cleaning up auth state listener');
        clearTimeout(timeout);
        authListenerSetupRef.current = false;
        unsubscribe();
      };
    } catch (error) {
      console.error('AuthProvider: Failed to initialize Firebase Auth:', error);
      setFirebaseError(error instanceof Error ? error.message : 'Firebase initialization failed');
      setLoading(false);
      clearTimeout(timeout);
      return () => {};
    }
  }, []); // Keep empty dependency array - auth listener should only be set up once

  // Helper function to determine if we should process auth state change
  const shouldProcessAuthStateChange = (newUserId: string | null): boolean => {
    // Always process if no user (logout)
    if (!newUserId) {
      return true;
    }
    
    // Always process if we haven't processed this user before
    if (globalAuthState.lastProcessedUserId !== newUserId) {
      return true;
    }
    
    // Don't process if we recently processed the same user (within 30 seconds)
    const recentlyProcessed = globalAuthState.processedTimestamp && 
      (Date.now() - globalAuthState.processedTimestamp < 30 * 1000);
    
    if (recentlyProcessed) {
      return false;
    }
    
    // Don't process if currently processing
    if (globalAuthState.isProcessing) {
      return false;
    }
    
    return true;
  };

  // Separated auth processing logic
  const processAuthStateChange = async (user: AuthUser | null, newUserId: string | null) => {
    setUser(user);
    
    // Get extended user data if user exists
    if (user) {
      console.log('AuthProvider: User authenticated, processing extended user data');
      
      try {
        // Optimized extended user data fetching
        const extendedUserData = await fetchExtendedUserData(user);
        setExtendedUser(extendedUserData);
        
        // Set analytics user properties (only once per session)
        if (!globalAuthState.lastProcessedUserId || globalAuthState.lastProcessedUserId !== user.uid) {
          try {
            AnalyticsService.setUserId(user.uid);
            AnalyticsService.setUserProperties({
              user_id: user.uid,
              email_verified: user.emailVerified,
            });
          } catch (analyticsError) {
            console.warn('AuthProvider: Analytics setup failed', analyticsError);
          }
        }
      } catch (error) {
        console.error('AuthProvider: Error processing extended user data', error);
        setExtendedUser(null);
      }
    } else {
      console.log('AuthProvider: No user, clearing extended user data');
      setExtendedUser(null);
    }
    
    console.log('AuthProvider: Setting loading to false');
    setLoading(false);
  };

  // Optimized extended user data fetching with caching
  const fetchExtendedUserData = async (user: AuthUser): Promise<ExtendedUser | null> => {
    try {
      console.log('AuthProvider: Fetching extended user data');
      let extendedUserData = await UserService.getExtendedUser(user);
      
      if (extendedUserData) {
        console.log('AuthProvider: Extended user data found', { role: extendedUserData.role });
        return extendedUserData;
      }
      
      console.log('AuthProvider: No extended user data, creating user document');
      
      // Try to create missing user document as fallback
      try {
        await UserService.createUserDocument(user);
        console.log('AuthProvider: User document created, retrying fetch');
        
        // Retry fetching extended user data after creation
        extendedUserData = await UserService.getExtendedUser(user);
        if (extendedUserData) {
          console.log('AuthProvider: Extended user data retrieved after creation');
          return extendedUserData;
        } else {
          console.log('AuthProvider: Still no extended user data after creation');
          return null;
        }
      } catch (createError) {
        console.error('AuthProvider: Failed to create user document', createError);
        return null;
      }
    } catch (error) {
      console.error('AuthProvider: Error fetching extended user data', error);
      return null;
    }
  };

  const signIn = async (email: string, password: string): Promise<void> => {
    try {
      await AuthService.signIn(email, password);
      AnalyticsService.logSignIn('email');
    } catch (error) {
      throw error;
    }
  };

  const signUp = async (email: string, password: string, displayName?: string): Promise<void> => {
    try {
      const newUser = await AuthService.signUp(email, password, displayName);
      
      // Sync the user profile to ensure Firestore has the correct displayName
      if (displayName && newUser) {
        try {
          await UserService.syncUserProfileFromAuth(newUser);
        } catch (syncError) {
          console.warn('AuthProvider: Failed to sync user profile after signup:', syncError);
          // Don't throw here as the user was created successfully
        }
      }
      
      AnalyticsService.logSignUp('email');
    } catch (error) {
      throw error;
    }
  };

  const signOut = async (): Promise<void> => {
    try {
      // Reset global auth state on sign out
      globalAuthState = {
        lastProcessedUserId: null,
        isProcessing: false,
        processedTimestamp: null
      };
      
      await AuthService.signOut();
      AnalyticsService.logEvent('logout');
    } catch (error) {
      throw error;
    }
  };

  const resetPassword = async (email: string): Promise<void> => {
    try {
      await AuthService.resetPassword(email);
      AnalyticsService.logEvent('password_reset_request');
    } catch (error) {
      throw error;
    }
  };

  const updateProfile = async (displayName: string, photoURL?: string): Promise<void> => {
    try {
      // Update Firebase Auth profile
      await AuthService.updateProfile(displayName, photoURL);
      
      // Also update the Firestore user document to keep them in sync
      if (user?.uid) {
        await UserService.updateUserProfile(user.uid, {
          displayName,
          ...(photoURL && { photoURL })
        });
        
        // Refresh the extended user data to reflect the changes
        const updatedExtendedUser = await UserService.getExtendedUser(user);
        if (updatedExtendedUser) {
          setExtendedUser(updatedExtendedUser);
        }
      }
      
      AnalyticsService.logEvent('profile_updated');
    } catch (error) {
      throw error;
    }
  };

  const value: AuthContextType = {
    user,
    extendedUser,
    loading,
    signIn,
    signOut,
    resetPassword,
    updateProfile,
  };

  // Show Firebase error if initialization failed
  if (firebaseError) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4 max-w-md mx-auto p-6">
          <div className="text-red-500 text-lg font-semibold">Firebase Error</div>
          <p className="text-sm text-muted-foreground">{firebaseError}</p>
          <button 
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
          >
            Reload Page
          </button>
        </div>
      </div>
    );
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
} 
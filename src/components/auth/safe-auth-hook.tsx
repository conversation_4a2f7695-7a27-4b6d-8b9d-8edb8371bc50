'use client';

import { useContext } from 'react';
import { AuthContextType } from '@/types/auth';
import { AuthContext } from './auth-provider';

// Safe version of useAuth that doesn't throw when used outside AuthProvider
export function useSafeAuth(): AuthContextType | null {
  try {
    const context = useContext(AuthContext);
    return context || null;
  } catch {
    return null;
  }
}

// Hook that provides default values when auth is not available
export function useOptionalAuth(): AuthContextType {
  const context = useSafeAuth();
  
  if (context) {
    return context;
  }
  
  // Return safe default values when auth context is not available
  return {
    user: null,
    extendedUser: null,
    loading: false,
    signIn: async () => {
      throw new Error('Authentication not available on this page');
    },
    signOut: async () => {
      throw new Error('Authentication not available on this page');
    },
    resetPassword: async () => {
      throw new Error('Authentication not available on this page');
    },
    updateProfile: async () => {
      throw new Error('Authentication not available on this page');
    },
  };
} 
"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { CameraCapture } from "@/components/checklist/camera-capture";
import { <PERSON>, CheckCircle, AlertTriangle, Info } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";

export function CameraTest() {
  const [isTestOpen, setIsTestOpen] = useState(false);
  const [testResult, setTestResult] = useState<{
    success: boolean;
    message: string;
    imageUrl?: string;
  } | null>(null);
  const [systemInfo, setSystemInfo] = useState<{
    userAgent: string;
    isHttps: boolean;
    hasMediaDevices: boolean;
    hasGetUserMedia: boolean;
    isSecureContext: boolean;
  } | null>(null);

  const checkSystemInfo = () => {
    const info = {
      userAgent: navigator.userAgent,
      isHttps: window.location.protocol === 'https:',
      hasMediaDevices: !!navigator.mediaDevices,
      hasGetUserMedia: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia),
      isSecureContext: window.isSecureContext
    };
    setSystemInfo(info);
  };

  const handleCameraCapture = (file: File) => {
    const imageUrl = URL.createObjectURL(file);
    setTestResult({
      success: true,
      message: `Camera capture successful! File size: ${(file.size / 1024).toFixed(1)}KB`,
      imageUrl
    });
    setIsTestOpen(false);
  };

  const handleTestStart = () => {
    setTestResult(null);
    setIsTestOpen(true);
  };

  return (
    <div className="max-w-2xl mx-auto p-6 space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Camera className="h-5 w-5" />
            Camera Functionality Test
          </CardTitle>
          <CardDescription>
            Test camera capture functionality for mobile and web browsers
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Button onClick={handleTestStart}>
              <Camera className="h-4 w-4 mr-2" />
              Test Camera
            </Button>
            <Button variant="outline" onClick={checkSystemInfo}>
              <Info className="h-4 w-4 mr-2" />
              Check System Info
            </Button>
          </div>

          {/* System Information */}
          {systemInfo && (
            <Alert>
              <Info className="h-4 w-4" />
              <AlertDescription>
                <div className="space-y-1 text-sm">
                  <div><strong>Browser:</strong> {systemInfo.userAgent.includes('Chrome') ? 'Chrome' : 
                                                   systemInfo.userAgent.includes('Safari') ? 'Safari' : 
                                                   systemInfo.userAgent.includes('Firefox') ? 'Firefox' : 'Other'}</div>
                  <div><strong>Platform:</strong> {systemInfo.userAgent.includes('Mobile') ? 'Mobile' : 'Desktop'}</div>
                  <div><strong>HTTPS:</strong> {systemInfo.isHttps ? '✅ Yes' : '❌ No'}</div>
                  <div><strong>Secure Context:</strong> {systemInfo.isSecureContext ? '✅ Yes' : '❌ No'}</div>
                  <div><strong>MediaDevices API:</strong> {systemInfo.hasMediaDevices ? '✅ Available' : '❌ Not Available'}</div>
                  <div><strong>getUserMedia:</strong> {systemInfo.hasGetUserMedia ? '✅ Available' : '❌ Not Available'}</div>
                </div>
              </AlertDescription>
            </Alert>
          )}

          {/* Test Result */}
          {testResult && (
            <Alert variant={testResult.success ? "default" : "destructive"}>
              {testResult.success ? <CheckCircle className="h-4 w-4" /> : <AlertTriangle className="h-4 w-4" />}
              <AlertDescription>
                <div>{testResult.message}</div>
                {testResult.imageUrl && (
                  <div className="mt-2">
                    <img 
                      src={testResult.imageUrl} 
                      alt="Camera test capture" 
                      className="max-w-xs rounded border"
                    />
                  </div>
                )}
              </AlertDescription>
            </Alert>
          )}

          {/* Usage Instructions */}
          <div className="text-sm text-muted-foreground space-y-2">
            <div><strong>Testing Instructions:</strong></div>
            <ul className="list-disc list-inside space-y-1">
              <li>Click "Test Camera" to open the camera capture dialog</li>
              <li>Allow camera permissions when prompted</li>
              <li>Take a photo and confirm to test the full flow</li>
              <li>Check "System Info" to verify browser compatibility</li>
            </ul>
            
            <div className="mt-4">
              <strong>Troubleshooting:</strong>
              <ul className="list-disc list-inside space-y-1">
                <li>Ensure you're using HTTPS (required for camera access)</li>
                <li>Grant camera permissions in browser settings</li>
                <li>Try refreshing the page if camera doesn't load</li>
                <li>Use Chrome, Safari, Firefox, or Edge for best compatibility</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Camera Capture Modal */}
      <CameraCapture
        isOpen={isTestOpen}
        onClose={() => setIsTestOpen(false)}
        onCapture={handleCameraCapture}
        title="Camera Test"
        description="Test camera capture functionality"
      />
    </div>
  );
}

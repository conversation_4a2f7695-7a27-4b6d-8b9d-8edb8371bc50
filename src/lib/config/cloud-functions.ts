/**
 * Cloud Function configuration and utilities
 * Centralizes Cloud Function URL management for the application
 */

// Base Cloud Function URL configuration
const CLOUD_FUNCTION_CONFIG = {
  region: 'asia-east1',
  projectId: 'auburn-engineering',
} as const;

// Environment-specific base URLs
const getBaseUrl = () => {
  if (process.env.NODE_ENV === 'production') {
    return `https://${CLOUD_FUNCTION_CONFIG.region}-${CLOUD_FUNCTION_CONFIG.projectId}.cloudfunctions.net`;
  } else {
    return `http://127.0.0.1:5001/${CLOUD_FUNCTION_CONFIG.projectId}/${CLOUD_FUNCTION_CONFIG.region}`;
  }
};

/**
 * Cloud Function endpoints
 */
export const CLOUD_FUNCTION_URLS = {
  // Proxy functions
  proxyImage: `${getBaseUrl()}/proxyImage`,
  proxyPdf: `${getBaseUrl()}/proxyPdf`,
  
  // Admin functions (HTTP endpoints for REST compatibility)
  queueExport: `${getBaseUrl()}/queueExportHttp`,
  
  // Admin functions (callable - for direct Firebase SDK usage)
  queueExportCallable: 'queueExport',
  getExportQueueStatsCallable: 'getExportQueueStats',
  
  // Individual PDF generation
  generateIndividualPDFCallable: 'generateIndividualPDF'
} as const;

/**
 * Get Cloud Function URL for a specific function
 */
export function getCloudFunctionUrl(functionName: keyof typeof CLOUD_FUNCTION_URLS): string {
  return CLOUD_FUNCTION_URLS[functionName];
}

/**
 * Helper to determine if we should use HTTP endpoints or callable functions
 * Returns true for HTTP, false for callable
 */
export function useHttpEndpoint(): boolean {
  // Use HTTP for better compatibility and easier migration
  return true;
}

/**
 * Helper to create proxy image URL
 */
export function createProxyImageUrl(imageUrl: string, options?: { width?: number; quality?: number }): string {
  const params = new URLSearchParams({
    url: imageUrl,
  });
  
  if (options?.width) {
    params.set('w', options.width.toString());
  }
  
  if (options?.quality) {
    params.set('q', options.quality.toString());
  }
  
  return `${CLOUD_FUNCTION_URLS.proxyImage}?${params.toString()}`;
}

/**
 * Helper to create proxy PDF URL
 */
export function createProxyPdfUrl(pdfUrl: string): string {
  const params = new URLSearchParams({
    url: pdfUrl,
  });
  
  return `${CLOUD_FUNCTION_URLS.proxyPdf}?${params.toString()}`;
} 
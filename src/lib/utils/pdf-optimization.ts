import { jsPDF } from 'jspdf';

export interface PDFOptimizationOptions {
  imageQuality: number; // 0.1 - 1.0
  scale: number; // 0.5 - 2.0
  format: 'jpeg' | 'png';
  enableCompression: boolean;
  enableImageDownscaling: boolean;
  maxImageWidth?: number;
  maxImageHeight?: number;
}

export const PDF_OPTIMIZATION_PRESETS = {
  // Ultra compression for web viewing (smallest file size)
  ultraCompressed: {
    imageQuality: 0.6,
    scale: 0.8,
    format: 'jpeg' as const,
    enableCompression: true,
    enableImageDownscaling: true,
    maxImageWidth: 1200,
    maxImageHeight: 800
  },
  
  // Balanced compression (good quality vs size)
  balanced: {
    imageQuality: 0.75,
    scale: 1.0,
    format: 'jpeg' as const,
    enableCompression: true,
    enableImageDownscaling: false
  },
  
  // High quality for printing
  highQuality: {
    imageQuality: 0.9,
    scale: 1.2,
    format: 'png' as const,
    enableCompression: true,
    enableImageDownscaling: false
  }
} as const;

/**
 * Optimize canvas before converting to PDF
 */
export function optimizeCanvas(
  canvas: HTMLCanvasElement, 
  options: PDFOptimizationOptions
): HTMLCanvasElement {
  if (!options.enableImageDownscaling || (!options.maxImageWidth && !options.maxImageHeight)) {
    return canvas;
  }

  const { maxImageWidth = 1920, maxImageHeight = 1080 } = options;
  
  // Check if resizing is needed
  if (canvas.width <= maxImageWidth && canvas.height <= maxImageHeight) {
    return canvas;
  }

  // Calculate new dimensions maintaining aspect ratio
  const aspectRatio = canvas.width / canvas.height;
  let newWidth = canvas.width;
  let newHeight = canvas.height;

  if (canvas.width > maxImageWidth) {
    newWidth = maxImageWidth;
    newHeight = newWidth / aspectRatio;
  }

  if (newHeight > maxImageHeight) {
    newHeight = maxImageHeight;
    newWidth = newHeight * aspectRatio;
  }

  // Create optimized canvas
  const optimizedCanvas = document.createElement('canvas');
  optimizedCanvas.width = newWidth;
  optimizedCanvas.height = newHeight;
  
  const ctx = optimizedCanvas.getContext('2d');
  if (!ctx) {
    return canvas; // Fallback to original
  }

  // Use better image smoothing
  ctx.imageSmoothingEnabled = true;
  ctx.imageSmoothingQuality = 'high';
  
  // Draw resized image
  ctx.drawImage(canvas, 0, 0, newWidth, newHeight);
  
  return optimizedCanvas;
}

/**
 * Create an optimized PDF with custom compression settings
 */
export function createOptimizedPDF(options: PDFOptimizationOptions = PDF_OPTIMIZATION_PRESETS.balanced): jsPDF {
  return new jsPDF({
    orientation: 'portrait',
    unit: 'mm',
    format: 'a4',
    compress: options.enableCompression,
    precision: 2 // Reduce decimal precision for smaller file size
  });
}

/**
 * Convert canvas to optimized data URL
 */
export function canvasToOptimizedDataURL(
  canvas: HTMLCanvasElement, 
  options: PDFOptimizationOptions
): string {
  const optimizedCanvas = optimizeCanvas(canvas, options);
  return optimizedCanvas.toDataURL(`image/${options.format}`, options.imageQuality);
}

/**
 * Estimate PDF file size reduction
 */
export function estimateSizeReduction(
  currentOptions: PDFOptimizationOptions,
  newOptions: PDFOptimizationOptions
): number {
  // Rough estimation based on quality and scale differences
  const qualityReduction = (currentOptions.imageQuality - newOptions.imageQuality) / currentOptions.imageQuality;
  const scaleReduction = (currentOptions.scale - newOptions.scale) / currentOptions.scale;
  const formatReduction = currentOptions.format === 'png' && newOptions.format === 'jpeg' ? 0.3 : 0;
  
  const totalReduction = qualityReduction + scaleReduction + formatReduction;
  return Math.min(Math.max(totalReduction, 0), 0.8); // Cap at 80% reduction
}

/**
 * Get recommended optimization based on file size target
 */
export function getRecommendedOptimization(targetSizeMB: number): PDFOptimizationOptions {
  if (targetSizeMB <= 1) {
    return PDF_OPTIMIZATION_PRESETS.ultraCompressed;
  } else if (targetSizeMB <= 3) {
    return PDF_OPTIMIZATION_PRESETS.balanced;
  } else {
    return PDF_OPTIMIZATION_PRESETS.highQuality;
  }
} 
/**
 * Color utility functions and constants for consistent theming
 * Provides balanced color schemes while respecting the primary red brand color
 */

// Status colors for different states
export const STATUS_COLORS = {
  success: {
    bg: 'bg-green-100 dark:bg-green-900/30',
    text: 'text-green-800 dark:text-green-300',
    border: 'border-green-200 dark:border-green-800',
    ring: 'ring-green-500/20',
    gradient: 'from-green-500 to-green-600'
  },
  warning: {
    bg: 'bg-amber-100 dark:bg-amber-900/30',
    text: 'text-amber-800 dark:text-amber-300',
    border: 'border-amber-200 dark:border-amber-800',
    ring: 'ring-amber-500/20',
    gradient: 'from-amber-500 to-amber-600'
  },
  error: {
    bg: 'bg-red-100 dark:bg-red-900/30',
    text: 'text-red-800 dark:text-red-300',
    border: 'border-red-200 dark:border-red-800',
    ring: 'ring-red-500/20',
    gradient: 'from-red-500 to-red-600'
  },
  info: {
    bg: 'bg-blue-100 dark:bg-blue-900/30',
    text: 'text-blue-800 dark:text-blue-300',
    border: 'border-blue-200 dark:border-blue-800',
    ring: 'ring-blue-500/20',
    gradient: 'from-blue-500 to-blue-600'
  },
  neutral: {
    bg: 'bg-slate-100 dark:bg-slate-900/30',
    text: 'text-slate-800 dark:text-slate-300',
    border: 'border-slate-200 dark:border-slate-800',
    ring: 'ring-slate-500/20',
    gradient: 'from-slate-500 to-slate-600'
  }
} as const;

// Equipment health colors
export const HEALTH_COLORS = {
  excellent: STATUS_COLORS.success,
  good: {
    bg: 'bg-emerald-100 dark:bg-emerald-900/30',
    text: 'text-emerald-800 dark:text-emerald-300',
    border: 'border-emerald-200 dark:border-emerald-800',
    ring: 'ring-emerald-500/20',
    gradient: 'from-emerald-500 to-emerald-600'
  },
  fair: STATUS_COLORS.warning,
  poor: {
    bg: 'bg-orange-100 dark:bg-orange-900/30',
    text: 'text-orange-800 dark:text-orange-300',
    border: 'border-orange-200 dark:border-orange-800',
    ring: 'ring-orange-500/20',
    gradient: 'from-orange-500 to-orange-600'
  },
  critical: STATUS_COLORS.error
} as const;

// Priority colors
export const PRIORITY_COLORS = {
  low: STATUS_COLORS.success,
  medium: STATUS_COLORS.warning,
  high: STATUS_COLORS.error,
  urgent: {
    bg: 'bg-red-200 dark:bg-red-900/50',
    text: 'text-red-900 dark:text-red-200',
    border: 'border-red-300 dark:border-red-700',
    ring: 'ring-red-500/30',
    gradient: 'from-red-600 to-red-700'
  }
} as const;

// Chart colors for data visualization
export const CHART_COLORS = {
  primary: '#dc2626', // Red - primary brand color
  secondary: '#3b82f6', // Blue - secondary data
  success: '#22c55e', // Green - positive metrics
  warning: '#f59e0b', // Amber - warnings
  danger: '#ef4444', // Red - errors/critical
  info: '#6366f1', // Indigo - informational
  neutral: '#64748b', // Slate - neutral data
  purple: '#a855f7', // Purple - additional variety
} as const;

// Gradient combinations
export const GRADIENTS = {
  primary: 'from-red-600 to-red-500',
  secondary: 'from-blue-600 to-blue-500',
  success: 'from-green-600 to-green-500',
  warning: 'from-amber-600 to-amber-500',
  danger: 'from-red-600 to-red-500',
  info: 'from-blue-600 to-indigo-500',
  neutral: 'from-slate-600 to-slate-500',
  purple: 'from-purple-600 to-purple-500',
  // Multi-color gradients
  rainbow: 'from-red-500 via-blue-500 to-green-500',
  sunset: 'from-red-500 via-amber-500 to-orange-500',
  ocean: 'from-blue-600 via-blue-500 to-teal-500',
} as const;

// Button color variants
export const BUTTON_VARIANTS = {
  primary: 'bg-red-600 hover:bg-red-700 text-white shadow-red-500/25',
  secondary: 'bg-blue-600 hover:bg-blue-700 text-white shadow-blue-500/25',
  success: 'bg-green-600 hover:bg-green-700 text-white shadow-green-500/25',
  warning: 'bg-amber-600 hover:bg-amber-700 text-white shadow-amber-500/25',
  danger: 'bg-red-600 hover:bg-red-700 text-white shadow-red-500/25',
  outline: 'border border-blue-500/30 hover:bg-blue-900/20 text-slate-200',
  ghost: 'hover:bg-slate-800/50 text-slate-200',
} as const;

// Utility functions
export function getStatusColor(status: 'success' | 'warning' | 'error' | 'info' | 'neutral') {
  return STATUS_COLORS[status];
}

export function getHealthColor(health: 'excellent' | 'good' | 'fair' | 'poor' | 'critical') {
  return HEALTH_COLORS[health];
}

export function getPriorityColor(priority: 'low' | 'medium' | 'high' | 'urgent') {
  return PRIORITY_COLORS[priority];
}

export function getChartColor(index: number): string {
  const colors = Object.values(CHART_COLORS);
  return colors[index % colors.length];
}

// Helper function to get contrasting text color
export function getContrastingTextColor(backgroundColor: string): string {
  // Simple implementation - in a real app, you might want to use a more sophisticated algorithm
  const darkColors = ['red', 'blue', 'green', 'purple', 'indigo'];
  const isDark = darkColors.some(color => backgroundColor.includes(color));
  return isDark ? 'text-white' : 'text-slate-900';
}

// Color palette for consistent usage
export const COLOR_PALETTE = {
  brand: {
    primary: '#dc2626', // Red
    secondary: '#3b82f6', // Blue
  },
  semantic: {
    success: '#22c55e', // Green
    warning: '#f59e0b', // Amber
    error: '#ef4444', // Red
    info: '#3b82f6', // Blue
  },
  neutral: {
    50: '#f8fafc',
    100: '#f1f5f9',
    200: '#e2e8f0',
    300: '#cbd5e1',
    400: '#94a3b8',
    500: '#64748b',
    600: '#475569',
    700: '#334155',
    800: '#1e293b',
    900: '#0f172a',
  }
} as const; 
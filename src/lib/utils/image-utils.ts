/**
 * Utility functions for handling images, particularly Firebase Storage URLs
 */

/**
 * Check if a URL is a Firebase Storage URL
 */
export function isFirebaseStorageUrl(url: string): boolean {
  return url.includes('firebasestorage.googleapis.com') || url.includes('firebasestorage.app');
}

/**
 * Get image props for Next.js Image component
 * Automatically handles Firebase Storage URLs in production
 */
export function getImageProps(src: string, options: {
  alt: string;
  width?: number;
  height?: number;
  sizes?: string;
  className?: string;
  priority?: boolean;
  quality?: number;
} = { alt: '' }) {
  const isProduction = process.env.NODE_ENV === 'production';
  const isFirebaseImage = isFirebaseStorageUrl(src);
  
  return {
    src,
    alt: options.alt,
    width: options.width,
    height: options.height,
    sizes: options.sizes,
    className: options.className,
    priority: options.priority,
    quality: options.quality,
    // Disable optimization for Firebase Storage in production
    unoptimized: isProduction && isFirebaseImage,
  };
}

/**
 * Get optimized image URL for Firebase Storage
 * Uses proxy API if needed
 */
export function getOptimizedImageUrl(src: string, width?: number, quality?: number): string {
  const isProduction = process.env.NODE_ENV === 'production';
  const isFirebaseImage = isFirebaseStorageUrl(src);
  
  // In production, use Cloud Function proxy for Firebase Storage URLs
  if (isProduction && isFirebaseImage) {
    const { createProxyImageUrl } = require('@/lib/config/cloud-functions');
    return createProxyImageUrl(src, { width, quality });
  }
  
  // In development or for non-Firebase images, return original URL
  return src;
}

/**
 * Validate if an image URL is accessible
 */
export async function validateImageUrl(url: string): Promise<boolean> {
  try {
    const response = await fetch(url, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.warn('Image URL validation failed:', url, error);
    return false;
  }
} 
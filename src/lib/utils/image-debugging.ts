/**
 * Image debugging utilities for fixing 404 image issues
 */

import { FirebaseStorageService } from '@/lib/services/storage/firebase-storage-service';
import { FirestoreStorageService } from '@/lib/services/storage/firestore-storage';
import { ChecklistData } from '@/types/checklist';
import { log } from './logger';

export interface ImageValidationResult {
  checklistId: string;
  beforeImage?: {
    url: string;
    isValid: boolean;
    path?: string;
  };
  afterImage?: {
    url: string;
    isValid: boolean;
    path?: string;
  };
  inspectorSignature?: {
    url: string;
    isValid: boolean;
    path?: string;
  };
}

/**
 * Validate all images in a checklist
 */
export async function validateChecklistImages(checklist: ChecklistData): Promise<ImageValidationResult> {
  const result: ImageValidationResult = {
    checklistId: checklist.id,
  };

  // Check before image
  if (checklist.beforeImage) {
    const isValid = await FirebaseStorageService.validateImageUrl(checklist.beforeImage);
    result.beforeImage = {
      url: checklist.beforeImage,
      isValid,
      path: checklist.imageMetadata?.beforeImage?.path,
    };
  }

  // Check after image
  if (checklist.afterImage) {
    const isValid = await FirebaseStorageService.validateImageUrl(checklist.afterImage);
    result.afterImage = {
      url: checklist.afterImage,
      isValid,
      path: checklist.imageMetadata?.afterImage?.path,
    };
  }

  // Check inspector signature
  if (checklist.inspectorSignature) {
    const isValid = await FirebaseStorageService.validateImageUrl(checklist.inspectorSignature);
    result.inspectorSignature = {
      url: checklist.inspectorSignature,
      isValid,
      path: checklist.imageMetadata?.inspectorSignature?.path,
    };
  }

  return result;
}

/**
 * Attempt to fix broken image URLs by refreshing them
 */
export async function fixChecklistImages(
  checklist: ChecklistData,
  userId: string
): Promise<ChecklistData | null> {
  try {
    const validation = await validateChecklistImages(checklist);
    let hasChanges = false;
    const updatedChecklist = { ...checklist };

    // Fix before image if needed
    if (validation.beforeImage && !validation.beforeImage.isValid && validation.beforeImage.path) {
      const refreshedUrl = await FirebaseStorageService.refreshImageUrl(validation.beforeImage.path);
      if (refreshedUrl) {
        updatedChecklist.beforeImage = refreshedUrl;
        hasChanges = true;
        log.info('Fixed before image URL', 'IMAGE_DEBUG', {
          checklistId: checklist.id,
          oldUrl: validation.beforeImage.url,
          newUrl: refreshedUrl,
        });
      }
    }

    // Fix after image if needed
    if (validation.afterImage && !validation.afterImage.isValid && validation.afterImage.path) {
      const refreshedUrl = await FirebaseStorageService.refreshImageUrl(validation.afterImage.path);
      if (refreshedUrl) {
        updatedChecklist.afterImage = refreshedUrl;
        hasChanges = true;
        log.info('Fixed after image URL', 'IMAGE_DEBUG', {
          checklistId: checklist.id,
          oldUrl: validation.afterImage.url,
          newUrl: refreshedUrl,
        });
      }
    }

    // Fix inspector signature if needed
    if (validation.inspectorSignature && !validation.inspectorSignature.isValid && validation.inspectorSignature.path) {
      const refreshedUrl = await FirebaseStorageService.refreshImageUrl(validation.inspectorSignature.path);
      if (refreshedUrl) {
        updatedChecklist.inspectorSignature = refreshedUrl;
        hasChanges = true;
        log.info('Fixed inspector signature URL', 'IMAGE_DEBUG', {
          checklistId: checklist.id,
          oldUrl: validation.inspectorSignature.url,
          newUrl: refreshedUrl,
        });
      }
    }

    // Save changes if any URLs were fixed
    if (hasChanges) {
      await FirestoreStorageService.saveChecklist(updatedChecklist, userId);
      return updatedChecklist;
    }

    return null; // No changes needed
  } catch (error) {
    log.error('Failed to fix checklist images', 'IMAGE_DEBUG', {
      checklistId: checklist.id,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    return null;
  }
}

/**
 * Debug all checklists for image issues
 */
export async function debugAllChecklistImages(userId: string): Promise<ImageValidationResult[]> {
  try {
    const checklists = await FirestoreStorageService.getAllChecklists(userId);
    const results: ImageValidationResult[] = [];

    for (const checklist of checklists) {
      const validation = await validateChecklistImages(checklist);
      results.push(validation);
    }

    return results;
  } catch (error) {
    log.error('Failed to debug checklist images', 'IMAGE_DEBUG', {
      userId,
      error: error instanceof Error ? error.message : 'Unknown error',
    });
    return [];
  }
}

/**
 * Get summary of image issues
 */
export function getImageIssueSummary(results: ImageValidationResult[]): {
  totalChecklists: number;
  checklistsWithIssues: number;
  brokenBeforeImages: number;
  brokenAfterImages: number;
  brokenSignatures: number;
} {
  const summary = {
    totalChecklists: results.length,
    checklistsWithIssues: 0,
    brokenBeforeImages: 0,
    brokenAfterImages: 0,
    brokenSignatures: 0,
  };

  results.forEach((result) => {
    let hasIssues = false;

    if (result.beforeImage && !result.beforeImage.isValid) {
      summary.brokenBeforeImages++;
      hasIssues = true;
    }

    if (result.afterImage && !result.afterImage.isValid) {
      summary.brokenAfterImages++;
      hasIssues = true;
    }

    if (result.inspectorSignature && !result.inspectorSignature.isValid) {
      summary.brokenSignatures++;
      hasIssues = true;
    }

    if (hasIssues) {
      summary.checklistsWithIssues++;
    }
  });

  return summary;
} 
import { getAnalytics, logEvent as firebaseLogEvent, Analytics } from 'firebase/analytics';
import app from '@/config/firebase-persistence';
import { setUserProperties, setUserId } from 'firebase/analytics';

let analytics: Analytics | null = null;

export const logEvent = (eventName: string, parameters: Record<string, unknown> = {}) => {
  if (typeof window !== 'undefined' && !analytics) {
    analytics = getAnalytics(app);
  }
  
  if (analytics) {
    firebaseLogEvent(analytics, eventName, parameters);
  }
};

export class AnalyticsService {
  static logEvent(eventName: string, parameters: Record<string, unknown> = {}): void {
    logEvent(eventName, parameters);
  }

  static setUserProperties(properties: Record<string, unknown>): void {
    if (analytics) {
      setUserProperties(analytics, properties);
    }
  }

  static setUserId(userId: string): void {
    if (analytics) {
      setUserId(analytics, userId);
    }
  }

  // Custom event helpers
  static logSignIn(method: string): void {
    this.logEvent('login', { method });
  }

  static logSignUp(method: string): void {
    this.logEvent('sign_up', { method });
  }

  static logPageView(pageName: string): void {
    this.logEvent('page_view', { page_title: pageName });
  }

  static logChecklistCreated(): void {
    this.logEvent('checklist_created');
  }

  static logChecklistSaved(): void {
    this.logEvent('checklist_saved');
  }

  static logChecklistExported(format: string): void {
    this.logEvent('checklist_exported', { export_format: format });
  }

  static logUserEngagement(action: string, category?: string): void {
    this.logEvent('user_engagement', { 
      action, 
      ...(category && { category }) 
    });
  }
} 
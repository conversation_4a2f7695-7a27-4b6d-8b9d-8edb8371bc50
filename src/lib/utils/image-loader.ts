/**
 * Custom image loader for Next.js to handle Firebase Storage URLs in production
 * This routes Firebase Storage URLs through our Cloud Function proxy to avoid CORS issues
 */

import { createProxyImageUrl } from '@/lib/config/cloud-functions';

interface LoaderProps {
  src: string;
  width: number;
  quality?: number;
}

export default function customImageLoader({ src, width, quality }: LoaderProps) {
  // Check if it's a Firebase Storage URL
  if (src.includes('firebasestorage.googleapis.com') || src.includes('firebasestorage.app')) {
    // Use our Cloud Function proxy for Firebase Storage URLs
    return createProxyImageUrl(src, { width, quality: quality || 75 });
  }
  
  // For non-Firebase URLs, return as-is
  return src;
} 
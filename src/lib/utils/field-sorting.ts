import { ALL_CHECKS, FIELD_DISPLAY_ORDER, validateExportFieldOrder } from '@/config/checklist-fields';
import { MechanicalCheck, ElectricalCheck, SequenceControlsCheck, ChecklistData } from '@/types/checklist';

export interface FieldEntry {
  key: string;
  value: any;
  label: string;
  unit?: string;
  type: 'status' | 'number';
  section: 'mechanical' | 'electrical' | 'sequence';
}

/**
 * Sort checklist field entries by their order in ALL_CHECKS configuration
 * This ensures consistent ordering across all export methods
 */
export function sortFieldEntries<T extends Record<string, any>>(
  entries: [string, T[keyof T]][]
): [string, T[keyof T]][] {
  return entries.sort(([keyA], [keyB]) => {
    const indexA = ALL_CHECKS.findIndex(check => check.key === keyA);
    const indexB = ALL_CHECKS.findIndex(check => check.key === keyB);
    
    // If field not found in ALL_CHECKS, put it at the end
    if (indexA === -1 && indexB === -1) return 0;
    if (indexA === -1) return 1;
    if (indexB === -1) return -1;
    
    return indexA - indexB;
  });
}

/**
 * Get sorted field entries for a specific section with optional empty field handling
 */
export function getSortedSectionEntries(
  data: MechanicalCheck | ElectricalCheck | SequenceControlsCheck,
  section: 'mechanical' | 'electrical' | 'sequence',
  includeEmpty: boolean = false
): [string, any][] {
  // Get all configured fields for this section first
  const sectionFields = ALL_CHECKS
    .filter(field => field.section === section)
    .map(field => field.key);
  
  // Create entries for all configured fields
  const allEntries: [string, any][] = sectionFields.map(fieldKey => {
    const value = data[fieldKey as keyof typeof data];
    const shouldInclude = includeEmpty || (value !== undefined && value !== null && String(value).trim() !== '');
    const displayValue = shouldInclude 
      ? (value !== undefined && value !== null && String(value).trim() !== '' ? value : 'N/A')
      : value;
    
    return [fieldKey, displayValue];
  });

  // Filter out undefined/null/empty values if includeEmpty is false
  const filteredEntries = includeEmpty 
    ? allEntries
    : allEntries.filter(([, value]) => value !== undefined && value !== null && String(value).trim() !== '');
    
  return sortFieldEntries(filteredEntries);
}

/**
 * Get all fields with values for a section, including empty fields as "N/A"
 */
export function getSectionFieldsWithValues(
  data: MechanicalCheck | ElectricalCheck | SequenceControlsCheck,
  section: 'mechanical' | 'electrical' | 'sequence',
  includeEmpty: boolean = true
): FieldEntry[] {
  const entries = getSortedSectionEntries(data, section, includeEmpty);
  
  return entries.map(([key, value]) => {
    const fieldConfig = ALL_CHECKS.find(field => field.key === key);
    const processedValue = (value !== undefined && value !== null && String(value).trim() !== '') 
      ? value 
      : (includeEmpty ? 'N/A' : value);
    
    return {
      key,
      value: processedValue,
      label: fieldConfig?.label || key,
      unit: fieldConfig?.unit,
      type: fieldConfig?.type || 'status',
      section: fieldConfig?.section || section
    };
  });
}

/**
 * Get all fields from all sections with proper ordering and empty field handling
 */
export function getAllFieldsWithValues(
  checklistData: ChecklistData,
  includeEmpty: boolean = true
): {
  mechanical: FieldEntry[];
  electrical: FieldEntry[];
  sequence: FieldEntry[];
} {
  return {
    mechanical: getSectionFieldsWithValues(checklistData.mechanicalChecks, 'mechanical', includeEmpty),
    electrical: getSectionFieldsWithValues(checklistData.electricalChecks, 'electrical', includeEmpty),
    sequence: getSectionFieldsWithValues(checklistData.sequenceControlsChecks, 'sequence', includeEmpty)
  };
}

/**
 * Get fields sorted by their configuration order for a specific section
 */
export function getSortedFields(section: 'mechanical' | 'electrical' | 'sequence') {
  return ALL_CHECKS
    .filter(field => field.section === section)
    .sort((a, b) => {
      const indexA = ALL_CHECKS.findIndex(check => check.key === a.key);
      const indexB = ALL_CHECKS.findIndex(check => check.key === b.key);
      return indexA - indexB;
    });
}

/**
 * Validate field ordering consistency
 */
export function validateFieldOrdering(
  exportData: { key: string; order: number }[],
  section: 'mechanical' | 'electrical' | 'sequence'
): { isValid: boolean; issues: string[] } {
  const expectedFields = getSortedFields(section);
  const issues: string[] = [];
  
  // Check if all expected fields are present
  expectedFields.forEach((expectedField, index) => {
    const foundField = exportData.find(item => item.key === expectedField.key);
    if (!foundField) {
      issues.push(`Missing field: ${expectedField.label} (${expectedField.key})`);
    } else if (foundField.order !== index) {
      issues.push(`Incorrect order for field: ${expectedField.label} (expected ${index}, got ${foundField.order})`);
    }
  });
  
  return {
    isValid: issues.length === 0,
    issues
  };
}

/**
 * Get display value for any field value, handling empty states consistently
 */
export function getDisplayValue(value: any, includeEmpty: boolean = true): string {
  if (value === undefined || value === null || String(value).trim() === '') {
    return includeEmpty ? 'N/A' : '';
  }
  
  if (typeof value === 'string' && (value === 'OK' || value === 'Faulty' || value === 'N/A' || value === 'Missing')) {
    return value;
  }
  
  return String(value);
}

/**
 * Comprehensive validation for export consistency
 * Tests field ordering and empty field handling across different export scenarios
 */
export function validateExportConsistency(
  checklistData: ChecklistData
): {
  isValid: boolean;
  issues: string[];
  summary: {
    totalFields: number;
    emptyFields: number;
    fieldsBySection: Record<string, number>;
    orderingValid: boolean;
  };
} {
  const issues: string[] = [];
  let orderingValid = true;
  
  // Get all fields with values
  const allFields = getAllFieldsWithValues(checklistData, true);
  
  // Count fields by section
  const fieldsBySection = {
    mechanical: allFields.mechanical.length,
    electrical: allFields.electrical.length,
    sequence: allFields.sequence.length
  };
  
  const totalFields = fieldsBySection.mechanical + fieldsBySection.electrical + fieldsBySection.sequence;
  
  // Count empty fields (those with "N/A" value)
  const emptyFields = [
    ...allFields.mechanical,
    ...allFields.electrical,
    ...allFields.sequence
  ].filter(field => field.value === 'N/A').length;
  
  // Validate section ordering
  FIELD_DISPLAY_ORDER.sections.forEach(section => {
    const sectionFields = allFields[section];
    const fieldKeys = sectionFields.map(f => f.key);
    
    const validation = validateExportFieldOrder(fieldKeys, section);
    if (!validation.isValid) {
      orderingValid = false;
      issues.push(...validation.issues);
    }
  });
  
  // Validate that all configured fields are present
  ALL_CHECKS.forEach(configField => {
    const found = [
      ...allFields.mechanical,
      ...allFields.electrical,
      ...allFields.sequence
    ].find(field => field.key === configField.key);
    
    if (!found) {
      issues.push(`Missing configured field: ${configField.label} (${configField.key})`);
    }
  });
  
  // Validate expected field counts
  const expectedMechanical = FIELD_DISPLAY_ORDER.mechanical.length;
  const expectedElectrical = FIELD_DISPLAY_ORDER.electrical.length;
  const expectedSequence = FIELD_DISPLAY_ORDER.sequence.length;
  
  if (fieldsBySection.mechanical !== expectedMechanical) {
    issues.push(`Mechanical field count mismatch: expected ${expectedMechanical}, got ${fieldsBySection.mechanical}`);
  }
  if (fieldsBySection.electrical !== expectedElectrical) {
    issues.push(`Electrical field count mismatch: expected ${expectedElectrical}, got ${fieldsBySection.electrical}`);
  }
  if (fieldsBySection.sequence !== expectedSequence) {
    issues.push(`Sequence field count mismatch: expected ${expectedSequence}, got ${fieldsBySection.sequence}`);
  }
  
  return {
    isValid: issues.length === 0 && orderingValid,
    issues,
    summary: {
      totalFields,
      emptyFields,
      fieldsBySection,
      orderingValid
    }
  };
}

/**
 * Test export consistency with a mock checklist
 * Useful for validating export functionality during development
 */
export function testExportConsistency() {
  // Create a mock checklist with some empty fields
  const mockChecklist: ChecklistData = {
    id: 'test-checklist',
    createdAt: '2024-01-15T10:00:00Z',
    updatedAt: '2024-01-15T10:00:00Z',
    syncMetadata: {
      status: 'local-only',
      lastLocalUpdateAt: '2024-01-15T10:00:00Z',
      localVersion: 1
    },
    generalInfo: {
      clientName: 'Test Client',
      building: 'Test Building',
      equipmentName: 'Test Equipment',
      location: 'Test Location',
      tagNo: 'TEST-001',
      date: '2024-01-15',
      ppmAttempt: 1,
      inspectedBy: 'Test Inspector',
      approvedBy: 'Test Approver'
    },
    mechanicalChecks: {
      airflowVelocity: 2.5,
      beltWearPulleyAlignment: 'OK',
      bladeImpellerDamage: 'N/A', // Empty field represented as N/A
      boltSetScrewTightness: 'Faulty',
      bladeTipClearance: 'N/A',
      excessiveVibration: 'N/A',
      fanGuardProtection: 'N/A',
      fanPowerOff: 'N/A',
      motorOverheating: 'N/A',
      rotationDirection: 'N/A',
      cleanBladesHousing: 'N/A',
      dustDebrisRemoval: 'N/A',
      erraticOperation: 'N/A',
      inletVanesOperation: 'N/A',
      bearingLubrication: 'N/A',
      noObstructionsBackflow: 'N/A',
      physicalDamageStability: 'N/A',
      springMountVibrationIsolator: 'N/A'
    },
    electricalChecks: {
      currentAmps: 15.2,
      bmsControlsInterlocks: 'OK',
      burntMarksDiscolorMelted: 'N/A', // Empty field represented as N/A
      circuitBreakerFunctional: 'Missing',
      contractorsBreakers: 'N/A',
      fireAlarmConnected: 'N/A',
      fuseTerminals: 'N/A',
      mccPowerOffBreaker: 'N/A',
      signsLiquidLeaks: 'N/A',
      tripSettingsFunction: 'N/A',
      controlRelaysOperations: 'N/A',
      doorsCoversCloseProperly: 'N/A',
      frayingExposedWires: 'N/A',
      highLowSpeedVerification: 'N/A',
      indicationsOnOffTrip: 'N/A',
      looseWiresToBeTightened: 'N/A',
      selectorHandStopAuto: 'N/A',
      testEmergencyStopButton: 'N/A'
    },
    sequenceControlsChecks: {
      dptDifferentialPressureTransmitter: 'OK',
      erraticOperationMalfunctioning: 'N/A', // Empty field represented as N/A
      indicationsOnOffTrip: 'Faulty',
      mccOffOverrideFunction: 'N/A',
      msfdDamperFunctional: 'N/A',
      offWithDuctDetectorActivation: 'N/A',
      overrideFscsPanelStatus: 'N/A',
      sameTagNameInMccFan: 'N/A',
      selectorRunStopAuto: 'N/A',
      vfdVariableFrequencyDrive: 'N/A'
    },
    remarks: 'Test remarks',
    isCompleted: false
  };
  
  const validation = validateExportConsistency(mockChecklist);
  
  console.log('Export Consistency Test Results:');
  console.log('================================');
  console.log(`Valid: ${validation.isValid}`);
  console.log(`Total Fields: ${validation.summary.totalFields}`);
  console.log(`Empty Fields: ${validation.summary.emptyFields}`);
  console.log('Fields by Section:', validation.summary.fieldsBySection);
  console.log(`Ordering Valid: ${validation.summary.orderingValid}`);
  
  if (validation.issues.length > 0) {
    console.log('\nIssues Found:');
    validation.issues.forEach((issue, index) => {
      console.log(`${index + 1}. ${issue}`);
    });
  } else {
    console.log('\n✅ All validation checks passed!');
  }
  
  return validation;
} 
import * as admin from 'firebase-admin';

let isInitialized = false;

export function initializeFirebaseAdmin() {
  if (isInitialized || admin.apps.length > 0) {
    return admin.app();
  }

  try {
    // Strategy 1: Use service account credentials from environment variables
    if (process.env.FIREBASE_PROJECT_ID && process.env.FIREBASE_CLIENT_EMAIL && process.env.FIREBASE_PRIVATE_KEY) {
      console.log('Initializing Firebase Admin with service account credentials');
      const app = admin.initializeApp({
        credential: admin.credential.cert({
          projectId: process.env.FIREBASE_PROJECT_ID,
          clientEmail: process.env.FIREBASE_CLIENT_EMAIL,
          privateKey: process.env.FIREBASE_PRIVATE_KEY.replace(/\\n/g, '\n'),
        }),
        projectId: process.env.FIREBASE_PROJECT_ID,
      });
      isInitialized = true;
      return app;
    }

    // Strategy 2: Use JSON credentials from environment variable
    if (process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON) {
      console.log('Initializing Firebase Admin with JSON credentials');
      const serviceAccount = JSON.parse(process.env.GOOGLE_APPLICATION_CREDENTIALS_JSON);
      const app = admin.initializeApp({
        credential: admin.credential.cert(serviceAccount),
        projectId: serviceAccount.project_id,
      });
      isInitialized = true;
      return app;
    }

    // Strategy 3: Use GOOGLE_APPLICATION_CREDENTIALS file path
    if (process.env.GOOGLE_APPLICATION_CREDENTIALS) {
      console.log('Initializing Firebase Admin with credentials file');
      const app = admin.initializeApp({
        projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'auburn-engineering',
      });
      isInitialized = true;
      return app;
    }

    // Strategy 4: Default initialization with project ID only (for development)
    console.log('Initializing Firebase Admin with project ID only');
    const app = admin.initializeApp({
      projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || 'auburn-engineering',
    });
    isInitialized = true;
    return app;

  } catch (error) {
    console.error('Failed to initialize Firebase Admin SDK:', error);
    throw new Error(`Firebase Admin initialization failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

export function getFirebaseAdmin() {
  if (!isInitialized && admin.apps.length === 0) {
    return initializeFirebaseAdmin();
  }
  return admin.app();
} 
import { ChecklistData, MechanicalCheck, ElectricalCheck, SequenceControlsCheck } from '@/types/checklist';
import { ALL_CHECKS } from '@/config/checklist-fields';
import { getSortedFields, getDisplayValue } from '@/lib/utils/field-sorting';
import * as XLSX from 'xlsx';
import { format } from 'date-fns';
// Remove old client-side PDF imports
// import { jsPDF } from 'jspdf';
// import html2canvas from 'html2canvas';
// import React from 'react';
// import { createRoot } from 'react-dom/client';
// import { PDFTemplate } from '@/components/pdf-template';
// import { CompanyAssetsService } from '@/lib/services/assets/company-assets-service';

// Import new cloud PDF service
import { CloudPDFService } from './cloud-pdf-service';

export function exportToExcel(checklist: ChecklistData): void {
  try {
    // Create a new workbook
    const workbook = XLSX.utils.book_new();
    
    // Sheet 1: General Information
    const generalInfoData = [
      ['General Information', ''],
      ['Client Name', checklist.generalInfo.clientName],
      ['Building', checklist.generalInfo.building],
      ['Equipment Name', checklist.generalInfo.equipmentName],
      ['Location', checklist.generalInfo.location],
      ['Tag Number', checklist.generalInfo.tagNo],
      ['Inspection Date', checklist.generalInfo.date],
      ['PPM Attempt', checklist.generalInfo.ppmAttempt],
      ['Inspected By', checklist.generalInfo.inspectedBy],
      ['Approved By', checklist.generalInfo.approvedBy],
      [''],
      ['Remarks', ''],
      ['Notes', checklist.remarks || 'No remarks provided'],
      [''],
      ['Images', ''],
      ['Before Image', checklist.beforeImage ? 'Attached' : 'Not provided'],
      ['After Image', checklist.afterImage ? 'Attached' : 'Not provided']
    ];
    
    const generalInfoSheet = XLSX.utils.aoa_to_sheet(generalInfoData);
    
    // Set column widths for general info sheet
    generalInfoSheet['!cols'] = [
      { width: 20 },
      { width: 40 }
    ];
    
    // Apply styling to headers
    generalInfoSheet['A1'] = { v: 'General Information', t: 's' };
    generalInfoSheet['A12'] = { v: 'Remarks', t: 's' };
    generalInfoSheet['A15'] = { v: 'Images', t: 's' };
    
    XLSX.utils.book_append_sheet(workbook, generalInfoSheet, 'General Info');
    
    // Sheet 2: Mechanical Checks
    const mechanicalData = [
      ['Field', 'Value/Status', 'Unit', 'Type']
    ];
    
    // Get mechanical fields sorted by configuration order
    const mechanicalFields = getSortedFields('mechanical');
    
    mechanicalFields.forEach(field => {
      const value = checklist.mechanicalChecks[field.key as keyof MechanicalCheck];
      const displayValue = getDisplayValue(value, true); // Always include, use "N/A" for empty
      
      mechanicalData.push([
        field.label,
        displayValue,
        field.unit || '',
        field.type === 'number' ? 'Measurement' : 'Status'
      ]);
    });
    
    const mechanicalSheet = XLSX.utils.aoa_to_sheet(mechanicalData);
    mechanicalSheet['!cols'] = [
      { width: 35 },
      { width: 15 },
      { width: 10 },
      { width: 12 }
    ];
    
    XLSX.utils.book_append_sheet(workbook, mechanicalSheet, 'Mechanical List');
    
    // Sheet 3: Electrical Checks
    const electricalData = [
      ['Field', 'Value/Status', 'Unit', 'Type']
    ];
    
    // Get electrical fields sorted by configuration order
    const electricalFields = getSortedFields('electrical');
    
    electricalFields.forEach(field => {
      const value = checklist.electricalChecks[field.key as keyof ElectricalCheck];
      const displayValue = getDisplayValue(value, true); // Always include, use "N/A" for empty
      
      electricalData.push([
        field.label,
        displayValue,
        field.unit || '',
        field.type === 'number' ? 'Measurement' : 'Status'
      ]);
    });
    
    const electricalSheet = XLSX.utils.aoa_to_sheet(electricalData);
    electricalSheet['!cols'] = [
      { width: 35 },
      { width: 15 },
      { width: 10 },
      { width: 12 }
    ];
    
    XLSX.utils.book_append_sheet(workbook, electricalSheet, 'Electrical List');
    
    // Sheet 4: Sequence/Controls Checks
    const sequenceData = [
      ['Field', 'Value/Status', 'Unit', 'Type']
    ];
    
    // Get sequence fields sorted by configuration order
    const sequenceFields = getSortedFields('sequence');
    
    sequenceFields.forEach(field => {
      const value = checklist.sequenceControlsChecks[field.key as keyof SequenceControlsCheck];
      const displayValue = getDisplayValue(value, true); // Always include, use "N/A" for empty
      
      sequenceData.push([
        field.label,
        displayValue,
        field.unit || '',
        field.type === 'number' ? 'Measurement' : 'Status'
      ]);
    });
    
    const sequenceSheet = XLSX.utils.aoa_to_sheet(sequenceData);
    sequenceSheet['!cols'] = [
      { width: 35 },
      { width: 15 },
      { width: 10 },
      { width: 12 }
    ];
    
    XLSX.utils.book_append_sheet(workbook, sequenceSheet, 'Sequence-Controls List');
    
    // Sheet 5: Summary (Updated to include all fields in statistics)
    const allItems = [
      ...Object.entries(checklist.mechanicalChecks),
      ...Object.entries(checklist.electricalChecks),
      ...Object.entries(checklist.sequenceControlsChecks)
    ];
    
    const stats = {
      OK: 0,
      Faulty: 0,
      'N/A': 0,
      Missing: 0,
      total: 0
    };
    
    // Count all configured fields, treating empty as N/A
    const allConfiguredFields = [
      ...mechanicalFields,
      ...electricalFields,
      ...sequenceFields
    ];
    
    allConfiguredFields.forEach(field => {
      const sectionData = field.section === 'mechanical' ? checklist.mechanicalChecks :
                         field.section === 'electrical' ? checklist.electricalChecks :
                         checklist.sequenceControlsChecks;
      
      const value = sectionData[field.key as keyof typeof sectionData];
      const displayValue = getDisplayValue(value, true);
      
      if (displayValue === 'OK' || displayValue === 'Faulty' || displayValue === 'N/A' || displayValue === 'Missing') {
        stats[displayValue as keyof typeof stats]++;
        stats.total++;
      } else if (displayValue !== '') {
        // Count numeric or other non-empty values as completed
        stats.total++;
      }
    });
    
    const okPercentage = stats.total > 0 ? Math.round((stats.OK / stats.total) * 100) : 0;
    const completionPercentage = stats.total > 0 ? Math.round(((stats.total - stats.Missing) / stats.total) * 100) : 0;
    
    const summaryData = [
      ['Inspection Summary', ''],
      [''],
      ['Status Breakdown', ''],
      ['Passed (OK)', stats.OK],
      ['Failed (Faulty)', stats.Faulty],
      ['Not Applicable (N/A)', stats['N/A']],
      ['Missing', stats.Missing],
      ['Total Tests', stats.total],
      [''],
      ['Performance Metrics', ''],
      ['Success Rate (%)', okPercentage],
      ['Completion Rate (%)', completionPercentage],
      ['Critical Issues', stats.Faulty],
      ['Assessment', okPercentage >= 90 ? 'EXCELLENT' : okPercentage >= 70 ? 'SATISFACTORY' : 'NEEDS ATTENTION'],
      [''],
      ['Report Generated', new Date().toLocaleString()]
    ];
    
    const summarySheet = XLSX.utils.aoa_to_sheet(summaryData);
    summarySheet['!cols'] = [
      { width: 25 },
      { width: 20 }
    ];
    
    XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');
    
    // Generate and validate filename
    const fileName = generateFileName(checklist, 'xlsx');
    const validation = validateFileName(fileName);
    
    if (!validation.isValid) {
      // Use fallback filename if validation fails
      const fallbackFileName = generateFallbackFileName(checklist, 'xlsx');
      XLSX.writeFile(workbook, fallbackFileName);
    } else {
      // Write the file with validated filename
      XLSX.writeFile(workbook, fileName);
    }
    
  } catch (error) {
    console.error('Error exporting Excel:', error);
    throw new Error('Failed to export Excel file');
  }
}

/**
 * Generate a safe fallback filename when validation fails
 */
function generateFallbackFileName(checklist: ChecklistData, extension: 'xlsx' | 'pdf'): string {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
  const safeId = checklist.id.substring(0, 8);
  return `PPM_Checklist_${safeId}_${timestamp}.${extension}`;
}

/**
 * Generate a structured filename for exported files
 * Format: EQUIPMENT_NAME_TAG_NUMBER_YYYY-MM-DD.extension
 * 
 * Examples:
 * - JET_FAN_JF_20_2025-01-15.pdf
 * - EXHAUST_FAN_EF_101_2024-12-03.xlsx
 * - AIR_HANDLER_AH_05A_2025-01-20.xlsx
 * 
 * @param checklist - The checklist data
 * @param extension - File extension ('xlsx' | 'pdf')
 * @returns Structured filename string
 */
function generateFileName(checklist: ChecklistData, extension: 'xlsx' | 'pdf'): string {
  try {
    const date = new Date(checklist.generalInfo.date);
    const formattedDate = format(date, 'yyyy-MM-dd');
    
    // Clean and format the tag number
    const tagNo = checklist.generalInfo.tagNo
      .replace(/[^a-zA-Z0-9]/g, '_')
      .replace(/_+/g, '_') // Replace multiple underscores with single
      .replace(/^_|_$/g, '') // Remove leading/trailing underscores
      .toUpperCase();
    
    // Clean and format equipment name for better readability
    const equipmentName = checklist.generalInfo.equipmentName
      .replace(/[^a-zA-Z0-9\s]/g, '') // Remove special chars but keep spaces
      .replace(/\s+/g, '_') // Replace spaces with underscores
      .replace(/_+/g, '_') // Replace multiple underscores with single
      .replace(/^_|_$/g, '') // Remove leading/trailing underscores
      .toUpperCase();
    
    // Generate structured filename: EQUIPMENT_TAG_DATE.extension
    return `${equipmentName}_${tagNo}_${formattedDate}.${extension}`;
  } catch {
    // Fallback to timestamp if date parsing fails
    const timestamp = new Date().toISOString().split('T')[0];
    const fallbackTag = (checklist.generalInfo.tagNo || 'UNKNOWN').replace(/[^a-zA-Z0-9]/g, '_').toUpperCase();
    const fallbackEquipment = (checklist.generalInfo.equipmentName || 'CHECKLIST').replace(/[^a-zA-Z0-9]/g, '_').toUpperCase();
    return `${fallbackEquipment}_${fallbackTag}_${timestamp}.${extension}`;
  }
}

/**
 * Validate filename structure and provide suggestions if needed
 * @param filename - The generated filename
 * @returns Object with validation result and suggestions
 */
function validateFileName(filename: string): { 
  isValid: boolean; 
  suggestions?: string[];
  structure: string;
} {
  const parts = filename.split('.');
  const nameWithoutExt = parts[0];
  const segments = nameWithoutExt.split('_');
  
  const structure = `${segments.length} segments: ${segments.join(' | ')}`;
  
  if (segments.length < 3) {
    return {
      isValid: false,
      suggestions: [
        'Filename should have at least 3 segments: EQUIPMENT_TAG_DATE',
        'Example: JET_FAN_JF_20_2025-01-15.pdf'
      ],
      structure
    };
  }
  
  // Check if last segment looks like a date
  const lastSegment = segments[segments.length - 1];
  const datePattern = /^\d{4}-\d{2}-\d{2}$/;
  
  if (!datePattern.test(lastSegment)) {
    return {
      isValid: false,
      suggestions: [
        'Last segment should be a date in YYYY-MM-DD format',
        `Current: ${lastSegment}, Expected format: 2025-01-15`
      ],
      structure
    };
  }
  
  return {
    isValid: true,
    structure
  };
}

/**
 * Load an image as a data URL to avoid CORS issues during PDF generation
 * Uses fetch API with proper CORS handling and proxy for Firebase images
 */
async function loadImageAsDataUrl(imagePath: string): Promise<string> {
  try {
    let imageUrl = imagePath;
    
    // For Firebase Storage URLs, use our Cloud Function proxy
    if (imagePath.includes('firebasestorage.googleapis.com') || imagePath.includes('firebasestorage.app')) {
      const { createProxyImageUrl } = require('@/lib/config/cloud-functions');
      imageUrl = createProxyImageUrl(imagePath);
      console.log('Using Cloud Function proxy for Firebase image:', imageUrl);
    }

    // For local images, use the direct approach
    if (imagePath.startsWith('/') || imagePath.startsWith('./')) {
      return new Promise((resolve, reject) => {
        const img = new Image();
        
        img.onload = () => {
          try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            if (!ctx) {
              reject(new Error('Failed to get canvas context'));
              return;
            }
            
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);
            
            const dataUrl = canvas.toDataURL('image/jpeg', 0.75);
            resolve(dataUrl);
          } catch (error) {
            reject(error);
          }
        };
        
        img.onerror = () => {
          reject(new Error(`Failed to load image: ${imagePath}`));
        };
        
        img.src = imagePath;
      });
    }

    // For all other URLs (including proxied Firebase URLs), use fetch
    const response = await fetch(imageUrl, {
      method: 'GET',
      mode: 'cors',
      credentials: 'omit',
      headers: {
        'Accept': 'image/*'
      }
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
    }

    const blob = await response.blob();
    
    return new Promise((resolve, reject) => {
      const img = new Image();
      const objectUrl = URL.createObjectURL(blob);
      
      img.onload = () => {
        try {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d');
          
          if (!ctx) {
            URL.revokeObjectURL(objectUrl);
            reject(new Error('Failed to get canvas context'));
            return;
          }
          
          canvas.width = img.width;
          canvas.height = img.height;
          ctx.drawImage(img, 0, 0);
          
          const dataUrl = canvas.toDataURL('image/jpeg', 0.85); // Increased quality from 0.75 to 0.85 to prevent corruption
          URL.revokeObjectURL(objectUrl);
          resolve(dataUrl);
        } catch (error) {
          URL.revokeObjectURL(objectUrl);
          reject(error);
        }
      };
      
      img.onerror = () => {
        URL.revokeObjectURL(objectUrl);
        reject(new Error(`Failed to load image from blob: ${imagePath}`));
      };
      
      img.src = objectUrl;
    });

  } catch (error) {
    console.error('Error loading image as data URL:', error);
    throw error;
  }
}

/**
 * Load Firebase Storage images as data URLs to avoid CORS issues
 */
async function loadChecklistImagesAsDataUrls(checklist: ChecklistData): Promise<{
  beforeImageDataUrl?: string;
  afterImageDataUrl?: string;
  signatureDataUrl?: string;
}> {
  const imageDataUrls: {
    beforeImageDataUrl?: string;
    afterImageDataUrl?: string;
    signatureDataUrl?: string;
  } = {};

  const loadImageWithFallback = async (url: string, imageType: string): Promise<string | undefined> => {
    try {
      console.log(`Loading ${imageType} image:`, url);
      const dataUrl = await loadImageAsDataUrl(url);
      console.log(`Successfully loaded ${imageType} image as data URL (${dataUrl.length} bytes)`);
      return dataUrl;
    } catch (error) {
      console.warn(`Failed to load ${imageType} image:`, error);
      
      // Try a simpler approach as fallback
      try {
        console.log(`Trying fallback approach for ${imageType} image...`);
        const img = new Image();
        img.crossOrigin = 'anonymous';
        
        const fallbackDataUrl = await new Promise<string>((resolve, reject) => {
          img.onload = () => {
            try {
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');
              if (!ctx) {
                reject(new Error('No canvas context'));
                return;
              }
              canvas.width = img.naturalWidth || img.width;
              canvas.height = img.naturalHeight || img.height;
              ctx.drawImage(img, 0, 0);
              resolve(canvas.toDataURL('image/jpeg', 0.85)); // Increased quality for better reliability
            } catch (canvasError) {
              reject(canvasError);
            }
          };
          img.onerror = () => reject(new Error('Image load failed'));
          
          // Add a timeout
          setTimeout(() => reject(new Error('Image load timeout')), 10000);
          img.src = url;
        });
        
        console.log(`Fallback approach succeeded for ${imageType} image`);
        return fallbackDataUrl;
      } catch (fallbackError) {
        console.error(`Both primary and fallback approaches failed for ${imageType} image:`, fallbackError);
        return undefined;
      }
    }
  };

  try {
    // Load before image
    if (checklist.beforeImage) {
      imageDataUrls.beforeImageDataUrl = await loadImageWithFallback(checklist.beforeImage, 'before');
    }

    // Load after image
    if (checklist.afterImage) {
      imageDataUrls.afterImageDataUrl = await loadImageWithFallback(checklist.afterImage, 'after');
    }

    // Load signature
    if (checklist.inspectorSignature) {
      imageDataUrls.signatureDataUrl = await loadImageWithFallback(checklist.inspectorSignature, 'signature');
    }
  } catch (error) {
    console.error('Error loading checklist images:', error);
  }

  return imageDataUrls;
}

export async function exportToPDF(checklist: ChecklistData) {
  console.log('Starting cloud PDF export for checklist:', checklist.id);
  
  try {
    // Use cloud function for PDF generation
    await CloudPDFService.generateAndDownloadPDF(checklist);
    
    console.log('Cloud PDF export completed successfully');
    
  } catch (error) {
    console.error('Cloud PDF export failed:', error);
    throw new Error(`PDF export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

// Export utility functions for testing and debugging
export { generateFileName as _generateFileName, validateFileName as _validateFileName }; 
import { ChecklistData } from '@/types/checklist';
import { BulkExportOptions, BulkExportResult, BulkExportProgress } from './bulk-export-service';
import { PDFDocument } from 'pdf-lib';
import { format } from 'date-fns';
import { CompanyAssetsService } from '@/lib/services/assets/company-assets-service';
import { validateFieldOrdering } from '@/lib/utils/field-sorting';
import { CloudPDFService } from './cloud-pdf-service';

export class ParallelBulkExportService {
  private static readonly MAX_CONCURRENT_PDFS = 4; // Optimal for most browsers
  private static readonly CHUNK_SIZE = 8; // Process in chunks to manage memory
  private static readonly GENERATION_WEIGHT = 0.85;
  private static readonly MERGE_WEIGHT = 0.15;

  /**
   * High-performance parallel bulk PDF export with consistent field ordering
   */
  static async exportBulkPDFParallel(
    checklists: ChecklistData[],
    options: BulkExportOptions = {}
  ): Promise<BulkExportResult> {
    const startTime = Date.now();
    const { onProgress, onError } = options;
    
    if (checklists.length === 0) {
      throw new Error('No checklists provided for export');
    }

    const result: BulkExportResult = {
      success: false,
      processedCount: 0,
      failedCount: 0,
      failedChecklists: []
    };

    try {
      // Validate field ordering consistency before starting
      console.log('Validating field ordering consistency for parallel export...');
      
      // Create the main PDF document
      const mergedPdf = await PDFDocument.create();
      
      // Add metadata
      mergedPdf.setTitle(`PPM Checklist Report - ${checklists.length} Items`);
      mergedPdf.setAuthor('Auburn Engineering');
      mergedPdf.setCreationDate(new Date());

      const totalChecklists = checklists.length;
      const chunks = this.chunkArray(checklists, this.CHUNK_SIZE);
      
      // Process chunks sequentially, but PDFs within each chunk in parallel
      for (let chunkIndex = 0; chunkIndex < chunks.length; chunkIndex++) {
        const chunk = chunks[chunkIndex];
        
        // Update progress for chunk start
        if (onProgress) {
          const chunkProgress = (chunkIndex / chunks.length) * this.GENERATION_WEIGHT;
          onProgress({
            currentIndex: result.processedCount,
            totalCount: totalChecklists,
            currentChecklist: `Processing chunk ${chunkIndex + 1}/${chunks.length}...`,
            stage: 'generating',
            percentage: Math.round(chunkProgress * 100)
          });
        }

        // Generate PDFs in parallel for this chunk
        const pdfPromises = chunk.map(checklist => 
          this.generatePDFWithCache(checklist, result.processedCount + 1, totalChecklists, startTime, onProgress, onError)
        );

        // Wait for all PDFs in this chunk to complete
        const chunkResults = await Promise.allSettled(pdfPromises);
        
        // Process results and merge PDFs
        for (let i = 0; i < chunkResults.length; i++) {
          const pdfResult = chunkResults[i];
          const checklist = chunk[i];
          
          if (pdfResult.status === 'fulfilled' && pdfResult.value) {
            try {
              // Load and merge the PDF
              const individualPdf = await PDFDocument.load(pdfResult.value);
              const pages = await mergedPdf.copyPages(individualPdf, individualPdf.getPageIndices());
              pages.forEach(page => mergedPdf.addPage(page));
              
              result.processedCount++;
            } catch (mergeError) {
              console.error(`Failed to merge PDF for checklist ${checklist.id}:`, mergeError);
              result.failedChecklists.push(checklist.id);
              result.failedCount++;
              
              if (onError) {
                onError(`Failed to merge PDF: ${mergeError instanceof Error ? mergeError.message : 'Unknown error'}`, checklist.id);
              }
            }
          } else {
            result.failedChecklists.push(checklist.id);
            result.failedCount++;
            
            if (onError) {
              const error = pdfResult.status === 'rejected' ? pdfResult.reason : 'Unknown error';
              onError(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`, checklist.id);
            }
          }
        }

        // Small delay between chunks to prevent memory issues
        if (chunkIndex < chunks.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      // Merging stage
      if (onProgress) {
        onProgress({
          currentIndex: totalChecklists,
          totalCount: totalChecklists,
          currentChecklist: 'Finalizing combined PDF...',
          stage: 'merging',
          percentage: Math.round((this.GENERATION_WEIGHT + this.MERGE_WEIGHT * 0.5) * 100)
        });
      }

      // Add cover page
      await this.addCoverPage(mergedPdf, checklists, result);

      // Finalization stage
      if (onProgress) {
        onProgress({
          currentIndex: totalChecklists,
          totalCount: totalChecklists,
          currentChecklist: 'Preparing download...',
          stage: 'finalizing',
          percentage: 95
        });
      }

      // Generate final PDF bytes with optimization
      const finalPdfBytes = await mergedPdf.save({
        useObjectStreams: false,  // Disable object streams for better compatibility and smaller size
        objectsPerTick: 50        // Process in chunks to prevent UI blocking
      });
      
      // Create download
      const fileName = this.generateBulkFileName(checklists, 'pdf');
      this.downloadFile(finalPdfBytes, fileName, 'application/pdf');
      
      result.success = true;
      result.fileName = fileName;
      result.fileSize = finalPdfBytes.length;

      // Complete with validation log
      if (onProgress) {
        onProgress({
          currentIndex: totalChecklists,
          totalCount: totalChecklists,
          currentChecklist: 'Export complete! Field ordering validated.',
          stage: 'complete',
          percentage: 100
        });
      }

      console.log('Parallel bulk PDF export completed successfully with consistent field ordering');
      return result;

    } catch (error) {
      console.error('Parallel bulk PDF export failed:', error);
      throw new Error(`Parallel bulk PDF export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate PDF with cache check and fallback
   */
  private static async generatePDFWithCache(
    checklist: ChecklistData,
    currentIndex: number,
    totalCount: number,
    startTime: number,
    onProgress?: (progress: BulkExportProgress) => void,
    onError?: (error: string, checklistId: string) => void
  ): Promise<Uint8Array | null> {
    try {
      // Update progress for this specific checklist
      if (onProgress) {
        const elapsed = Date.now() - startTime;
        const estimatedTotal = (elapsed / currentIndex) * totalCount;
        const estimatedRemaining = Math.max(0, estimatedTotal - elapsed);
        
        onProgress({
          currentIndex,
          totalCount,
          currentChecklist: `${checklist.generalInfo.tagNo} - ${checklist.generalInfo.equipmentName}`,
          stage: 'generating',
          percentage: Math.round((currentIndex / totalCount) * this.GENERATION_WEIGHT * 100),
          estimatedTimeRemaining: estimatedRemaining
        });
      }

      // Phase 1: PDF Caching DISABLED for parallel bulk export
      // if (checklist.userId) {
      //   try {
      //     const { PDFStorageService } = await import('../storage/pdf-storage-service');
      //     const cachedPDF = await PDFStorageService.getCachedPDFBytes(
      //       checklist.userId,
      //       checklist.id,
      //       checklist
      //     );
      //     
      //     if (cachedPDF) {
      //       console.log(`Using cached PDF for bulk export: ${checklist.id}`);
      //       return cachedPDF;
      //     }
      //   } catch (cacheError) {
      //     console.debug(`Cache check failed for bulk export, generating PDF: ${checklist.id}`, cacheError);
      //   }
      // }

      // Phase 2: Generate PDF if not cached (with high-quality assets)
      return await this.generateIndividualPDFBytes(checklist);
      
    } catch (error) {
      console.error(`Failed to process checklist ${checklist.id}:`, error);
      if (onError) {
        onError(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`, checklist.id);
      }
      return null;
    }
  }

  /**
   * Generate individual PDF bytes using cloud function (optimized version)
   */
  private static async generateIndividualPDFBytes(checklist: ChecklistData): Promise<Uint8Array> {
    try {
      // Use cloud function for PDF generation
      const result = await CloudPDFService.generatePDF(checklist);
      
      // Fetch the PDF bytes from the cloud storage URL
      const response = await fetch(result.downloadUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch PDF from cloud storage: ${response.status}`);
      }
      
      const arrayBuffer = await response.arrayBuffer();
      return new Uint8Array(arrayBuffer);
      
    } catch (error) {
      console.error('Cloud PDF generation failed for checklist:', checklist.id, error);
      throw new Error(`Failed to generate PDF for checklist ${checklist.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Fallback method - kept for emergency use only
   * This is the old client-side method as backup
   */
  private static async generateIndividualPDFBytesClientSide(checklist: ChecklistData): Promise<Uint8Array> {
    return new Promise(async (resolve, reject) => {
      try {
        // Load company assets and checklist images concurrently (high quality)
        const [logoDataUrl, companySignatureDataUrl, imageDataUrls] = await Promise.all([
          CompanyAssetsService.loadLogoAsHighQualityDataUrl(),
          CompanyAssetsService.loadSignatureAsHighQualityDataUrl(),
          this.loadChecklistImagesAsDataUrls(checklist)
        ]);
        
        // Create temporary container
        const container = document.createElement('div');
        container.style.position = 'absolute';
        container.style.left = '-9999px';
        container.style.top = '-9999px';
        container.style.width = '210mm';
        container.style.backgroundColor = '#ffffff';
        container.style.fontFamily = 'Arial, sans-serif';
        document.body.appendChild(container);

        // Dynamic imports for better performance
        const [React, { createRoot }, { PDFTemplate }, html2canvas, { jsPDF }] = await Promise.all([
          import('react'),
          import('react-dom/client'),
          import('@/components/pdf-template'),
          import('html2canvas'),
          import('jspdf')
        ]);

        const root = createRoot(container);
        
        // Render PDF template with company signature
        root.render(React.createElement(PDFTemplate, {
          checklist,
          logoDataUrl,
          beforeImageDataUrl: imageDataUrls.beforeImageDataUrl,
          afterImageDataUrl: imageDataUrls.afterImageDataUrl,
          signatureDataUrl: companySignatureDataUrl // Use company signature
        }));
        
        // Wait for rendering with optimized timeout
        setTimeout(async () => {
          try {
            // Generate canvas with optimized settings
            const canvas = await html2canvas.default(container, {
              scale: 0.8, // Reduced scale for faster processing
              useCORS: true,
              logging: false,
              backgroundColor: '#ffffff',
              allowTaint: true,
              removeContainer: false,
              width: container.scrollWidth,
              height: container.scrollHeight,
              foreignObjectRendering: false,
              imageTimeout: 8000, // Reduced timeout
              onclone: (clonedDoc) => {
                // Optimize cloned document
                const logoImages = clonedDoc.querySelectorAll('img[src="/images/logo.jpeg"], img[src="/logo.jpeg"]');
                logoImages.forEach(img => {
                  (img as HTMLImageElement).src = logoDataUrl;
                });
                
                // Add optimized styles
                const style = clonedDoc.createElement('style');
                style.textContent = `
                  * { 
                    -webkit-print-color-adjust: exact !important;
                    color-adjust: exact !important;
                    print-color-adjust: exact !important;
                    box-shadow: none !important;
                  }
                  body, html, div, span {
                    background-color: white !important;
                    background: white !important;
                    background-image: none !important;
                  }
                `;
                clonedDoc.head.appendChild(style);
              }
            });

            // Create PDF with high compression
            const pdf = new jsPDF({
              orientation: 'portrait',
              unit: 'mm',
              format: 'a4',
              compress: true
            });

            const pageWidth = 210;
            const pageHeight = 297;
            const canvasWidth = canvas.width;
            const canvasHeight = canvas.height;
            const ratio = pageWidth / canvasWidth;
            const scaledHeight = canvasHeight * ratio;

            // Optimize image format selection
            let imageData;
            try {
              // Use JPEG with high compression for bulk exports
              imageData = canvas.toDataURL('image/jpeg', 0.75); // Higher compression
            } catch (canvasError) {
              imageData = canvas.toDataURL('image/png', 0.8);
            }

            if (scaledHeight <= pageHeight) {
              pdf.addImage(imageData, 'JPEG', 0, 0, pageWidth, scaledHeight);
            } else {
              // Multi-page handling (simplified for performance)
              const pageCanvasHeight = pageHeight / ratio;
              let yPosition = 0;
              let pageNumber = 1;

              while (yPosition < canvasHeight) {
                // Calculate remaining content height
                const remainingHeight = canvasHeight - yPosition;
                
                // Ensure we have meaningful content to render
                if (remainingHeight <= 0) {
                  console.log(`Parallel PDF page generation completed at page ${pageNumber - 1}`);
                  break;
                }
                
                const pageCanvas = document.createElement('canvas');
                const pageCtx = pageCanvas.getContext('2d')!;
                
                pageCanvas.width = canvasWidth;
                
                // Calculate page height with improved bounds checking
                const calculatedHeight = Math.min(pageCanvasHeight, remainingHeight);
                const minHeight = 10; // Minimum viable height in pixels
                pageCanvas.height = Math.max(calculatedHeight, Math.min(minHeight, remainingHeight));
                
                // Validate dimensions before proceeding
                if (pageCanvas.width === 0 || pageCanvas.height === 0 || pageCanvas.height < minHeight) {
                  console.log(`Skipping page ${pageNumber} due to invalid dimensions: ${pageCanvas.width}x${pageCanvas.height}`);
                  break;
                }
                
                pageCtx.fillStyle = '#ffffff';
                pageCtx.fillRect(0, 0, pageCanvas.width, pageCanvas.height);
                
                // Draw with error handling
                try {
                  pageCtx.drawImage(canvas, 0, yPosition, canvasWidth, pageCanvas.height, 0, 0, canvasWidth, pageCanvas.height);
                } catch (drawError) {
                  console.error(`Error drawing canvas for parallel page ${pageNumber}:`, drawError);
                  break;
                }

                const pageImageData = pageCanvas.toDataURL('image/jpeg', 0.75);
                
                if (pageNumber > 1) pdf.addPage();
                pdf.addImage(pageImageData, 'JPEG', 0, 0, pageWidth, pageCanvas.height * ratio);

                yPosition += pageCanvas.height;
                pageNumber++;
                
                // Safety check to prevent infinite loops
                if (pageNumber > 50) {
                  console.warn('Parallel PDF generation stopped after 50 pages to prevent infinite loop');
                  break;
                }
              }
            }

            // Get PDF bytes
            const pdfBytes = new Uint8Array(pdf.output('arraybuffer'));
            
            // Cache PDF in background (DISABLED)
            // if (checklist.userId) {
            //   this.cachePDFInBackground(checklist, pdfBytes);
            // }
            
            // Cleanup
            root.unmount();
            if (document.body.contains(container)) {
              document.body.removeChild(container);
            }
            
            resolve(pdfBytes);
            
          } catch (error) {
            // Cleanup on error
            root.unmount();
            if (document.body.contains(container)) {
              document.body.removeChild(container);
            }
            reject(error);
          }
        }, 1000); // Reduced timeout for faster processing
        
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Cache PDF in background (non-blocking)
   */
  private static async cachePDFInBackground(checklist: ChecklistData, pdfBytes: Uint8Array): Promise<void> {
    try {
      const { PDFStorageService } = await import('../storage/pdf-storage-service');
      const contentHash = await PDFStorageService.generateContentHash(checklist);
      
      await PDFStorageService.savePDF(
        pdfBytes,
        checklist.userId!,
        checklist.id,
        contentHash
      );
      
      console.log('PDF cached for future bulk exports:', {
        checklistId: checklist.id,
        size: pdfBytes.length
      });
    } catch (cacheError) {
      console.debug('Failed to cache PDF (non-critical):', {
        checklistId: checklist.id,
        error: cacheError instanceof Error ? cacheError.message : 'Unknown error'
      });
    }
  }

  /**
   * Utility functions
   */
  private static chunkArray<T>(array: T[], chunkSize: number): T[][] {
    const chunks: T[][] = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }

  private static async addCoverPage(
    mergedPdf: PDFDocument,
    checklists: ChecklistData[],
    result: BulkExportResult
  ): Promise<void> {
    try {
      const coverPageBytes = await this.generateCoverPageBytes(checklists, result);
      const coverPdf = await PDFDocument.load(coverPageBytes);
      const [coverPage] = await mergedPdf.copyPages(coverPdf, [0]);
      
      // Insert cover page at the beginning
      mergedPdf.insertPage(0, coverPage);
    } catch (error) {
      console.warn('Failed to add cover page:', error);
    }
  }

  private static async generateCoverPageBytes(
    checklists: ChecklistData[],
    result: BulkExportResult
  ): Promise<Uint8Array> {
    // Simplified cover page generation for performance
    return new Promise(async (resolve, reject) => {
      try {
        const logoDataUrl = await this.loadImageAsDataUrl('/images/logo.jpeg');
        
        const container = document.createElement('div');
        container.style.position = 'absolute';
        container.style.left = '-9999px';
        container.style.top = '-9999px';
        container.style.width = '210mm';
        container.style.backgroundColor = '#ffffff';
        document.body.appendChild(container);

        const [React, { createRoot }, { BulkPDFCover }, html2canvas, { jsPDF }] = await Promise.all([
          import('react'),
          import('react-dom/client'),
          import('@/components/bulk-pdf-cover'),
          import('html2canvas'),
          import('jspdf')
        ]);

        const root = createRoot(container);
        root.render(React.createElement(BulkPDFCover, { checklists, result, logoDataUrl }));
        
        setTimeout(async () => {
          try {
            const canvas = await html2canvas.default(container, {
              scale: 0.8,
              useCORS: true,
              logging: false,
              backgroundColor: '#ffffff',
              imageTimeout: 5000
            });

            const pdf = new jsPDF({
              orientation: 'portrait',
              unit: 'mm',
              format: 'a4',
              compress: true
            });

            const imageData = canvas.toDataURL('image/jpeg', 0.8);
            const pageWidth = 210;
            const pageHeight = 297;
            const ratio = pageWidth / canvas.width;
            const scaledHeight = canvas.height * ratio;

            if (scaledHeight > pageHeight) {
              const heightRatio = pageHeight / canvas.height;
              const scaledWidth = canvas.width * heightRatio;
              const xOffset = (pageWidth - scaledWidth) / 2;
              pdf.addImage(imageData, 'JPEG', xOffset, 0, scaledWidth, pageHeight);
            } else {
              const yOffset = (pageHeight - scaledHeight) / 2;
              pdf.addImage(imageData, 'JPEG', 0, yOffset, pageWidth, scaledHeight);
            }

            const pdfBytes = pdf.output('arraybuffer');
            
            root.unmount();
            document.body.removeChild(container);
            
            resolve(new Uint8Array(pdfBytes));
          } catch (error) {
            root.unmount();
            document.body.removeChild(container);
            reject(error);
          }
        }, 800);
        
      } catch (error) {
        reject(error);
      }
    });
  }

  private static generateBulkFileName(checklists: ChecklistData[], extension: 'pdf'): string {
    const timestamp = format(new Date(), 'yyyy-MM-dd_HH-mm');
    const count = checklists.length;
    
    const clients = Array.from(new Set(checklists.map(c => c.generalInfo.clientName)));
    const clientName = clients.length === 1 
      ? clients[0].replace(/[^a-zA-Z0-9]/g, '_').substring(0, 20)
      : 'Multi_Client';
    
    return `PPM_Checklist_Report_${clientName}_${count}_Items_${timestamp}.${extension}`;
  }

  private static downloadFile(data: Uint8Array, fileName: string, mimeType: string): void {
    const blob = new Blob([data], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  private static async loadImageAsDataUrl(imagePath: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.crossOrigin = 'anonymous';
      
      img.onload = () => {
        try {
          const canvas = document.createElement('canvas');
          const ctx = canvas.getContext('2d')!;
          
          canvas.width = img.width;
          canvas.height = img.height;
          
          ctx.drawImage(img, 0, 0);
          
          const dataUrl = canvas.toDataURL('image/jpeg', 0.8);
          resolve(dataUrl);
        } catch (error) {
          reject(error);
        }
      };
      
      img.onerror = () => {
        resolve(this.createFallbackImage());
      };
      
      img.src = imagePath;
    });
  }

  private static createFallbackImage(): string {
    const canvas = document.createElement('canvas');
    canvas.width = 200;
    canvas.height = 100;
    const ctx = canvas.getContext('2d')!;
    
    ctx.fillStyle = '#f0f0f0';
    ctx.fillRect(0, 0, 200, 100);
    ctx.fillStyle = '#666';
    ctx.font = '14px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('Logo', 100, 55);
    
    return canvas.toDataURL('image/png');
  }

  private static async loadChecklistImagesAsDataUrls(checklist: ChecklistData): Promise<{
    beforeImageDataUrl?: string;
    afterImageDataUrl?: string;
    signatureDataUrl?: string;
  }> {
    const loadImageWithFallback = async (url: string, imageType: string): Promise<string | undefined> => {
      if (!url) return undefined;
      
      try {
        return await this.loadImageAsDataUrl(url);
      } catch (error) {
        console.warn(`Failed to load ${imageType} image:`, error);
        return undefined;
      }
    };

    const [beforeImageDataUrl, afterImageDataUrl, signatureDataUrl] = await Promise.all([
      loadImageWithFallback(checklist.beforeImage || '', 'before'),
      loadImageWithFallback(checklist.afterImage || '', 'after'),
      loadImageWithFallback(checklist.inspectorSignature || '', 'signature')
    ]);

    return {
      beforeImageDataUrl,
      afterImageDataUrl,
      signatureDataUrl
    };
  }
} 
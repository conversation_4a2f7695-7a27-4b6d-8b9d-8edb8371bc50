import { ChecklistData } from '@/types/checklist';
import { exportToPDF, exportToExcel } from '@/lib/services/export/export';
import { PDFDocument } from 'pdf-lib';
import * as XLSX from 'xlsx';
import { format } from 'date-fns';
import { ALL_CHECKS } from '@/config/checklist-fields';
import { getSortedFields, getDisplayValue } from '@/lib/utils/field-sorting';
import { CompanyAssetsService } from '@/lib/services/assets/company-assets-service';
import { CloudPDFService } from './cloud-pdf-service';

export interface BulkExportProgress {
  currentIndex: number;
  totalCount: number;
  currentChecklist: string;
  stage: 'generating' | 'merging' | 'finalizing' | 'complete';
  percentage: number;
  estimatedTimeRemaining?: number;
}

export interface BulkExportOptions {
  onProgress?: (progress: BulkExportProgress) => void;
  onError?: (error: string, checklistId: string) => void;
  batchSize?: number;
}

export interface BulkExportResult {
  success: boolean;
  processedCount: number;
  failedCount: number;
  failedChecklists: string[];
  fileName?: string;
  fileSize?: number;
}

export class BulkExportService {
  private static readonly DEFAULT_BATCH_SIZE = 10; // Reduced for better image handling
  private static readonly GENERATION_WEIGHT = 0.8; // 80% for generation, 20% for merging
  private static readonly MERGE_WEIGHT = 0.2;

  /**
   * Export multiple checklists as a combined PDF
   */
  static async exportBulkPDF(
    checklists: ChecklistData[],
    options: BulkExportOptions = {}
  ): Promise<BulkExportResult> {
    const startTime = Date.now();
    const { onProgress, onError, batchSize = this.DEFAULT_BATCH_SIZE } = options;
    
    if (checklists.length === 0) {
      throw new Error('No checklists provided for export');
    }

    const result: BulkExportResult = {
      success: false,
      processedCount: 0,
      failedCount: 0,
      failedChecklists: []
    };

    try {
      // Create the main PDF document
      const mergedPdf = await PDFDocument.create();
      
      // Add metadata
      mergedPdf.setTitle(`PPM Checklist Report - ${checklists.length} Items`);
      mergedPdf.setAuthor('Auburn Engineering');
      mergedPdf.setCreationDate(new Date());

      // Process checklists in batches
      const totalChecklists = checklists.length;
      
      for (let i = 0; i < totalChecklists; i += batchSize) {
        const batch = checklists.slice(i, Math.min(i + batchSize, totalChecklists));
        
        for (const checklist of batch) {
          try {
            // Update progress for current checklist
            const currentIndex = result.processedCount + 1;
            const generationProgress = (currentIndex / totalChecklists) * this.GENERATION_WEIGHT;
            
            if (onProgress) {
              const elapsed = Date.now() - startTime;
              const estimatedTotal = (elapsed / currentIndex) * totalChecklists;
              const estimatedRemaining = Math.max(0, estimatedTotal - elapsed);
              
              onProgress({
                currentIndex,
                totalCount: totalChecklists,
                currentChecklist: `${checklist.generalInfo.tagNo} - ${checklist.generalInfo.equipmentName}`,
                stage: 'generating',
                percentage: Math.round(generationProgress * 100),
                estimatedTimeRemaining: estimatedRemaining
              });
            }

            // Generate PDF for individual checklist in memory
            const pdfBytes = await this.generateIndividualPDFBytes(checklist);
            
            // Load the generated PDF
            const individualPdf = await PDFDocument.load(pdfBytes);
            
            // Copy pages to merged document
            const pages = await mergedPdf.copyPages(individualPdf, individualPdf.getPageIndices());
            pages.forEach(page => mergedPdf.addPage(page));
            
            result.processedCount++;
            
          } catch (error) {
            console.error(`Failed to process checklist ${checklist.id}:`, error);
            result.failedChecklists.push(checklist.id);
            result.failedCount++;
            
            if (onError) {
              onError(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`, checklist.id);
            }
          }
        }
        
        // Small delay between batches to prevent UI blocking
        await new Promise(resolve => setTimeout(resolve, 50));
      }

      // Merging stage
      if (onProgress) {
        onProgress({
          currentIndex: totalChecklists,
          totalCount: totalChecklists,
          currentChecklist: 'Finalizing combined PDF...',
          stage: 'merging',
          percentage: Math.round((this.GENERATION_WEIGHT + this.MERGE_WEIGHT * 0.5) * 100)
        });
      }

      // Add cover page with summary
      await this.addCoverPage(mergedPdf, checklists, result);

      // Finalization stage
      if (onProgress) {
        onProgress({
          currentIndex: totalChecklists,
          totalCount: totalChecklists,
          currentChecklist: 'Preparing download...',
          stage: 'finalizing',
          percentage: 95
        });
      }

      // Generate final PDF bytes with optimization
      const finalPdfBytes = await mergedPdf.save({
        useObjectStreams: false,  // Disable object streams for better compatibility and smaller size
        objectsPerTick: 50        // Process in chunks to prevent UI blocking
      });
      
      // Create download
      const fileName = this.generateBulkFileName(checklists, 'pdf');
      this.downloadFile(finalPdfBytes, fileName, 'application/pdf');
      
      result.success = true;
      result.fileName = fileName;
      result.fileSize = finalPdfBytes.length;

      // Complete
      if (onProgress) {
        onProgress({
          currentIndex: totalChecklists,
          totalCount: totalChecklists,
          currentChecklist: 'Export complete!',
          stage: 'complete',
          percentage: 100
        });
      }

      return result;

    } catch (error) {
      console.error('Bulk PDF export failed:', error);
      throw new Error(`Bulk PDF export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Export multiple checklists as a combined Excel workbook
   */
  static async exportBulkExcel(
    checklists: ChecklistData[],
    options: BulkExportOptions = {}
  ): Promise<BulkExportResult> {
    const startTime = Date.now();
    const { onProgress, onError } = options;
    
    if (checklists.length === 0) {
      throw new Error('No checklists provided for export');
    }

    const result: BulkExportResult = {
      success: false,
      processedCount: 0,
      failedCount: 0,
      failedChecklists: []
    };

    try {
      // Create a new workbook
      const workbook = XLSX.utils.book_new();
      const totalChecklists = checklists.length;

      // Create summary sheet
      if (onProgress) {
        onProgress({
          currentIndex: 0,
          totalCount: totalChecklists,
          currentChecklist: 'Creating summary sheet...',
          stage: 'generating',
          percentage: 5
        });
      }

      const summarySheet = this.createSummarySheet(checklists);
      XLSX.utils.book_append_sheet(workbook, summarySheet, 'Summary');

      // Process individual checklists
      for (let i = 0; i < totalChecklists; i++) {
        const checklist = checklists[i];
        
        try {
          const currentIndex = i + 1;
          const progress = (currentIndex / totalChecklists) * 90 + 5; // 5% for summary, 90% for processing, 5% for finalization
          
          if (onProgress) {
            const elapsed = Date.now() - startTime;
            const estimatedTotal = (elapsed / currentIndex) * totalChecklists;
            const estimatedRemaining = Math.max(0, estimatedTotal - elapsed);
            
            onProgress({
              currentIndex,
              totalCount: totalChecklists,
              currentChecklist: `${checklist.generalInfo.tagNo} - ${checklist.generalInfo.equipmentName}`,
              stage: 'generating',
              percentage: Math.round(progress),
              estimatedTimeRemaining: estimatedRemaining
            });
          }

          // Create individual checklist sheet
          const checklistSheet = this.createChecklistSheet(checklist);
          const sheetName = this.sanitizeSheetName(`${checklist.generalInfo.tagNo.substring(0, 20)}`);
          
          XLSX.utils.book_append_sheet(workbook, checklistSheet, sheetName);
          result.processedCount++;

        } catch (error) {
          console.error(`Failed to process checklist ${checklist.id}:`, error);
          result.failedChecklists.push(checklist.id);
          result.failedCount++;
          
          if (onError) {
            onError(`Failed to create Excel sheet: ${error instanceof Error ? error.message : 'Unknown error'}`, checklist.id);
          }
        }
      }

      // Finalization
      if (onProgress) {
        onProgress({
          currentIndex: totalChecklists,
          totalCount: totalChecklists,
          currentChecklist: 'Preparing download...',
          stage: 'finalizing',
          percentage: 95
        });
      }

      // Generate and download
      const fileName = this.generateBulkFileName(checklists, 'xlsx');
      XLSX.writeFile(workbook, fileName);
      
      result.success = true;
      result.fileName = fileName;

      // Complete
      if (onProgress) {
        onProgress({
          currentIndex: totalChecklists,
          totalCount: totalChecklists,
          currentChecklist: 'Export complete!',
          stage: 'complete',
          percentage: 100
        });
      }

      return result;

    } catch (error) {
      console.error('Bulk Excel export failed:', error);
      throw new Error(`Bulk Excel export failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Generate PDF bytes for an individual checklist
   * Now uses cloud function instead of client-side rendering
   */
  private static async generateIndividualPDFBytes(checklist: ChecklistData): Promise<Uint8Array> {
    try {
      // Use cloud function for PDF generation
      const result = await CloudPDFService.generatePDF(checklist);
      
      // Fetch the PDF bytes from the cloud storage URL
      const response = await fetch(result.downloadUrl);
      if (!response.ok) {
        throw new Error(`Failed to fetch PDF from cloud storage: ${response.status}`);
      }
      
      const arrayBuffer = await response.arrayBuffer();
      return new Uint8Array(arrayBuffer);
      
    } catch (error) {
      console.error('Cloud PDF generation failed for checklist:', checklist.id, error);
      throw new Error(`Failed to generate PDF for checklist ${checklist.id}: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Add a professional cover page to the merged PDF
   */
  private static async addCoverPage(
    mergedPdf: PDFDocument,
    checklists: ChecklistData[],
    result: BulkExportResult
  ): Promise<void> {
    // Generate professional cover page using React component
    const coverPageBytes = await this.generateCoverPageBytes(checklists, result);
    
    // Load the generated cover page PDF
    const coverPdf = await PDFDocument.load(coverPageBytes);
    
    // Copy cover page to the beginning of merged document
    const coverPages = await mergedPdf.copyPages(coverPdf, coverPdf.getPageIndices());
    
    // Insert at the beginning
    coverPages.forEach((page, index) => mergedPdf.insertPage(index, page));
  }

  /**
   * Generate cover page PDF bytes using React component
   */
  private static async generateCoverPageBytes(
    checklists: ChecklistData[],
    result: BulkExportResult
  ): Promise<Uint8Array> {
    return new Promise(async (resolve, reject) => {
      try {
        // Load company logo
        const logoDataUrl = await CompanyAssetsService.loadLogoAsDataUrl();
        
        // Create temporary container
        const container = document.createElement('div');
        container.style.position = 'absolute';
        container.style.left = '-9999px';
        container.style.width = '210mm';
        container.style.height = '297mm';
        document.body.appendChild(container);

        // Dynamically import React and ReactDOM
        Promise.all([
          import('react'),
          import('react-dom/client'),
          import('html2canvas'),
          import('jspdf')
        ]).then(([React, { createRoot }, html2canvas, { jsPDF }]) => {
          // Dynamically import the cover component
          import('@/components/bulk-pdf-cover').then(({ BulkPDFCover }) => {
            const root = createRoot(container);
            
            // Render the cover page with logo data URL
            root.render(React.createElement(BulkPDFCover, { checklists, result, logoDataUrl }));
            
            // Wait for rendering
            setTimeout(async () => {
              try {
                // Convert to canvas
                const canvas = await html2canvas.default(container, {
                  scale: 1.0, // Reduced from 1.5 to decrease file size
                  useCORS: true,
                  logging: false,
                  backgroundColor: null, // Let the original background show through
                  allowTaint: true, // Allow tainted canvas for local images
                  removeContainer: false,
                  width: container.scrollWidth,
                  height: container.scrollHeight,
                  foreignObjectRendering: false,
                  imageTimeout: 10000, // Increased timeout
                  onclone: (clonedDoc) => {
                    // Replace logo images with data URLs in cloned document
                    const logoImages = clonedDoc.querySelectorAll('img[src="/images/logo.jpeg"], img[src="/logo.jpeg"]');
                    logoImages.forEach(img => {
                      (img as HTMLImageElement).src = logoDataUrl;
                    });
                    
                    // Add comprehensive styles to prevent grey backgrounds
                    const style = clonedDoc.createElement('style');
                    style.textContent = `
                      * { 
                        -webkit-print-color-adjust: exact !important;
                        color-adjust: exact !important;
                        print-color-adjust: exact !important;
                        box-shadow: none !important;
                      }
                      body, html, div, span {
                        background-color: white !important;
                        background: white !important;
                        background-image: none !important;
                      }
                      .bg-gray-50, .bg-gray-100, .bg-slate-50, .bg-slate-100 {
                        background-color: white !important;
                        background: white !important;
                      }
                      [style*="background"] {
                        background-color: white !important;
                        background: white !important;
                      }
                      [style*="gradient"] {
                        background: white !important;
                        background-image: none !important;
                      }
                      [style*="f8f9fa"], [style*="ddd"], [style*="dee2e6"] {
                        background-color: white !important;
                        border-color: #e9ecef !important;
                      }
                      [style*="#CC0000"] {
                        background-color: #CC0000 !important;
                        color: white !important;
                      }
                    `;
                    clonedDoc.head.appendChild(style);
                  }
                });

                // Create PDF with compression
                const pdf = new jsPDF({
                  orientation: 'portrait',
                  unit: 'mm',
                  format: 'a4',
                  compress: true // Enable compression
                });

                const pageWidth = 210;
                const pageHeight = 297;
                const canvasWidth = canvas.width;
                const canvasHeight = canvas.height;
                const ratio = pageWidth / canvasWidth;
                const scaledHeight = canvasHeight * ratio;

                // Smart format selection for cover page
                let imageData;
                let useJPEG = false;
                
                try {
                  imageData = canvas.toDataURL('image/png', 0.95);
                  if (imageData.length > 2.7 * 1024 * 1024) { // ~2MB base64 encoded
                    imageData = canvas.toDataURL('image/jpeg', 0.88);
                    useJPEG = true;
                  }
                } catch (canvasError) {
                  console.warn('PNG conversion failed for cover page, using JPEG:', canvasError);
                  imageData = canvas.toDataURL('image/jpeg', 0.88);
                  useJPEG = true;
                }

                if (scaledHeight > pageHeight) {
                  // If content is too tall, scale to fit height
                  const heightRatio = pageHeight / canvasHeight;
                  const scaledWidth = canvasWidth * heightRatio;
                  const xOffset = (pageWidth - scaledWidth) / 2;
                  
                  pdf.addImage(
                    imageData,
                    useJPEG ? 'JPEG' : 'PNG',
                    xOffset,
                    0,
                    scaledWidth,
                    pageHeight
                  );
                } else {
                  // Content fits, center it
                  const yOffset = (pageHeight - scaledHeight) / 2;
                  pdf.addImage(
                    imageData,
                    useJPEG ? 'JPEG' : 'PNG',
                    0,
                    yOffset,
                    pageWidth,
                    scaledHeight
                  );
                }

                // Get PDF bytes
                const pdfBytes = pdf.output('arraybuffer');
                
                // Cleanup
                root.unmount();
                document.body.removeChild(container);
                
                resolve(new Uint8Array(pdfBytes));
                
              } catch (error) {
                console.error('Error generating cover page canvas:', error);
                root.unmount();
                document.body.removeChild(container);
                reject(error);
              }
            }, 1000);
          }).catch(reject);
        }).catch(reject);
        
      } catch (error) {
        console.error('Error setting up cover page generation:', error);
        reject(error);
      }
    });
  }

  /**
   * Create summary sheet for Excel workbook
   */
  private static createSummarySheet(checklists: ChecklistData[]): XLSX.WorkSheet {
    const summaryData = [
      ['PPM Checklist Summary Report', ''],
      ['Generated on:', new Date().toLocaleString()],
      ['Total Checklists:', checklists.length],
      [''],
      ['Checklist Details', ''],
      ['Tag Number', 'Equipment Name', 'Location', 'Client', 'Inspector', 'Date', 'Status'],
      ...checklists.map(checklist => [
        checklist.generalInfo.tagNo,
        checklist.generalInfo.equipmentName,
        checklist.generalInfo.location,
        checklist.generalInfo.clientName,
        checklist.generalInfo.inspectedBy,
        checklist.generalInfo.date,
        checklist.isCompleted ? 'Completed' : 'Pending'
      ])
    ];

    const worksheet = XLSX.utils.aoa_to_sheet(summaryData);
    worksheet['!cols'] = [
      { width: 15 },
      { width: 25 },
      { width: 20 },
      { width: 20 },
      { width: 20 },
      { width: 12 },
      { width: 12 }
    ];

    return worksheet;
  }

  /**
   * Create individual checklist sheet for Excel
   */
  private static createChecklistSheet(checklist: ChecklistData): XLSX.WorkSheet {
    const generalInfoData = [
      ['General Information', ''],
      ['Client Name', checklist.generalInfo.clientName],
      ['Building', checklist.generalInfo.building],
      ['Equipment Name', checklist.generalInfo.equipmentName],
      ['Location', checklist.generalInfo.location],
      ['Tag Number', checklist.generalInfo.tagNo],
      ['Inspection Date', checklist.generalInfo.date],
      ['PPM Attempt', checklist.generalInfo.ppmAttempt],
      ['Inspected By', checklist.generalInfo.inspectedBy],
      ['Approved By', checklist.generalInfo.approvedBy],
      [''],
      ['Remarks', ''],
      ['Notes', checklist.remarks || 'No remarks provided'],
      [''],
      ['Mechanical Checks', ''],
      ['Field', 'Value/Status', 'Unit']
    ];

    // Add sorted mechanical checks with empty field handling
    const mechanicalFields = getSortedFields('mechanical');
    
    mechanicalFields.forEach(field => {
      const value = checklist.mechanicalChecks[field.key as keyof typeof checklist.mechanicalChecks];
      const displayValue = getDisplayValue(value, true); // Always include, use "N/A" for empty
      
      generalInfoData.push([
        field.label,
        displayValue,
        field.unit || ''
      ]);
    });

    // Add electrical checks with empty field handling
    generalInfoData.push([''], ['Electrical Checks', ''], ['Field', 'Value/Status', 'Unit']);
    const electricalFields = getSortedFields('electrical');
    
    electricalFields.forEach(field => {
      const value = checklist.electricalChecks[field.key as keyof typeof checklist.electricalChecks];
      const displayValue = getDisplayValue(value, true); // Always include, use "N/A" for empty
      
      generalInfoData.push([
        field.label,
        displayValue,
        field.unit || ''
      ]);
    });

    // Add sequence/controls checks with empty field handling
    generalInfoData.push([''], ['Sequence/Controls Checks', ''], ['Field', 'Value/Status', 'Unit']);
    const sequenceFields = getSortedFields('sequence');
    
    sequenceFields.forEach(field => {
      const value = checklist.sequenceControlsChecks[field.key as keyof typeof checklist.sequenceControlsChecks];
      const displayValue = getDisplayValue(value, true); // Always include, use "N/A" for empty
      
      generalInfoData.push([
        field.label,
        displayValue,
        field.unit || ''
      ]);
    });

    const worksheet = XLSX.utils.aoa_to_sheet(generalInfoData);
    worksheet['!cols'] = [{ width: 30 }, { width: 20 }, { width: 10 }];

    return worksheet;
  }

  /**
   * Generate filename for bulk export
   */
  private static generateBulkFileName(checklists: ChecklistData[], extension: 'pdf' | 'xlsx'): string {
    const timestamp = format(new Date(), 'yyyy-MM-dd_HH-mm');
    const count = checklists.length;
    
    // Get common client name if all checklists are from the same client
    const clients = Array.from(new Set(checklists.map(c => c.generalInfo.clientName)));
    const clientName = clients.length === 1 
      ? clients[0].replace(/[^a-zA-Z0-9]/g, '_').substring(0, 20)
      : 'Multi_Client';
    
    return `PPM_Checklist_Report_${clientName}_${count}_Items_${timestamp}.${extension}`;
  }

  /**
   * Sanitize sheet name for Excel
   */
  private static sanitizeSheetName(name: string): string {
    return name
      .replace(/[\\/:*?"<>|]/g, '_')
      .substring(0, 31); // Excel sheet name limit
  }

  /**
   * Download file helper
   */
  private static downloadFile(data: Uint8Array, fileName: string, mimeType: string): void {
    const blob = new Blob([data], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  }

  /**
   * Load an image as a data URL to avoid CORS issues during PDF generation (optimized for bulk operations)
   * Uses proxy API for Firebase Storage images to avoid CORS issues
   */
  private static async loadImageAsDataUrl(imagePath: string): Promise<string> {
    try {
      let imageUrl = imagePath;
      
      // For Firebase Storage URLs, use our Cloud Function proxy to avoid CORS issues
      if (imagePath.includes('firebasestorage.googleapis.com') || imagePath.includes('firebasestorage.app')) {
        const { createProxyImageUrl } = require('@/lib/config/cloud-functions');
        imageUrl = createProxyImageUrl(imagePath);
        console.log('Using Cloud Function proxy for Firebase image in bulk export:', imageUrl);
      }

      // For local images, use the direct approach
      if (imagePath.startsWith('/') || imagePath.startsWith('./')) {
        return new Promise((resolve, reject) => {
          const img = new Image();
          
          // Set a timeout for image loading
          const loadTimeout = setTimeout(() => {
            console.warn(`Image load timeout for: ${imagePath}`);
            const fallbackDataUrl = this.createFallbackImage();
            resolve(fallbackDataUrl);
          }, 8000);
          
          img.onload = () => {
            clearTimeout(loadTimeout);
            try {
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');
              
              if (!ctx) {
                reject(new Error('Failed to get canvas context'));
                return;
              }
              
              canvas.width = img.width;
              canvas.height = img.height;
              ctx.drawImage(img, 0, 0);
              
              const dataUrl = canvas.toDataURL('image/jpeg', 0.85); // Increased quality from 0.7 to 0.85 to prevent corruption
              resolve(dataUrl);
            } catch (error) {
              reject(error);
            }
          };
          
          img.onerror = () => {
            clearTimeout(loadTimeout);
            const fallbackDataUrl = this.createFallbackImage();
            resolve(fallbackDataUrl);
          };
          
          img.src = imagePath;
        });
      }

      // For all other URLs (including proxied Firebase URLs), use fetch
      const response = await fetch(imageUrl, {
        method: 'GET',
        mode: 'cors',
        credentials: 'omit',
        headers: {
          'Accept': 'image/*'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
      }

      const blob = await response.blob();
      
      return new Promise((resolve, reject) => {
        const img = new Image();
        const objectUrl = URL.createObjectURL(blob);
        
        // Set a timeout for image loading
        const loadTimeout = setTimeout(() => {
          URL.revokeObjectURL(objectUrl);
          console.warn(`Image processing timeout for: ${imagePath}`);
          const fallbackDataUrl = this.createFallbackImage();
          resolve(fallbackDataUrl);
        }, 8000);
        
        img.onload = () => {
          clearTimeout(loadTimeout);
          try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            
            if (!ctx) {
              URL.revokeObjectURL(objectUrl);
              reject(new Error('Failed to get canvas context'));
              return;
            }
            
            canvas.width = img.width;
            canvas.height = img.height;
            ctx.drawImage(img, 0, 0);
            
            // Use optimized quality for bulk operations while preventing corruption
            const dataUrl = canvas.toDataURL('image/jpeg', 0.85); // Increased quality from 0.7 to 0.85 to prevent corruption
            URL.revokeObjectURL(objectUrl);
            resolve(dataUrl);
          } catch (error) {
            URL.revokeObjectURL(objectUrl);
            reject(error);
          }
        };
        
        img.onerror = () => {
          clearTimeout(loadTimeout);
          URL.revokeObjectURL(objectUrl);
          console.error('Failed to process image blob:', imagePath);
          const fallbackDataUrl = this.createFallbackImage();
          resolve(fallbackDataUrl);
        };
        
        img.src = objectUrl;
      });

    } catch (error) {
      console.error('Error loading image as data URL:', error);
      return this.createFallbackImage();
    }
  }

  /**
   * Create a fallback image when loading fails
   */
  private static createFallbackImage(): string {
    const canvas = document.createElement('canvas');
    canvas.width = 200;
    canvas.height = 90;
    const ctx = canvas.getContext('2d');
    if (ctx) {
      ctx.fillStyle = '#f0f0f0';
      ctx.fillRect(0, 0, 200, 90);
      ctx.fillStyle = '#666';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('Image Unavailable', 100, 45);
    }
    return canvas.toDataURL('image/png');
  }

  /**
   * Load checklist images as data URLs to avoid CORS issues during PDF generation
   */
  private static async loadChecklistImagesAsDataUrls(checklist: ChecklistData): Promise<{
    beforeImageDataUrl?: string;
    afterImageDataUrl?: string;
    signatureDataUrl?: string;
  }> {
    const imageDataUrls: {
      beforeImageDataUrl?: string;
      afterImageDataUrl?: string;
      signatureDataUrl?: string;
    } = {};

    const loadImageWithFallback = async (url: string, imageType: string): Promise<string | undefined> => {
      try {
        console.log(`Loading ${imageType} image:`, url);
        const dataUrl = await this.loadImageAsDataUrl(url);
        console.log(`Successfully loaded ${imageType} image as data URL (${dataUrl.length} bytes)`);
        return dataUrl;
      } catch (error) {
        console.warn(`Failed to load ${imageType} image:`, error);
        
        // Try a simpler approach as fallback
        try {
          console.log(`Trying fallback approach for ${imageType} image...`);
          const img = new Image();
          img.crossOrigin = 'anonymous';
          
          const fallbackDataUrl = await new Promise<string>((resolve, reject) => {
            img.onload = () => {
              try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                if (!ctx) {
                  reject(new Error('No canvas context'));
                  return;
                }
                canvas.width = img.naturalWidth || img.width;
                canvas.height = img.naturalHeight || img.height;
                ctx.drawImage(img, 0, 0);
                resolve(canvas.toDataURL('image/jpeg', 0.8));
              } catch (canvasError) {
                reject(canvasError);
              }
            };
            img.onerror = () => reject(new Error('Image load failed'));
            
            // Add a timeout
            setTimeout(() => reject(new Error('Image load timeout')), 10000);
            img.src = url;
          });
          
          console.log(`Fallback approach succeeded for ${imageType} image`);
          return fallbackDataUrl;
        } catch (fallbackError) {
          console.error(`Both primary and fallback approaches failed for ${imageType} image:`, fallbackError);
          return undefined;
        }
      }
    };

    try {
      // Load images sequentially to avoid overwhelming the browser
      // This is crucial for bulk exports where multiple checklists are processed
      
      // Load before image
      if (checklist.beforeImage) {
        imageDataUrls.beforeImageDataUrl = await loadImageWithFallback(checklist.beforeImage, 'before');
        // Small delay between image loads to prevent rate limiting
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Load after image
      if (checklist.afterImage) {
        imageDataUrls.afterImageDataUrl = await loadImageWithFallback(checklist.afterImage, 'after');
        await new Promise(resolve => setTimeout(resolve, 100));
      }

      // Load signature
      if (checklist.inspectorSignature) {
        imageDataUrls.signatureDataUrl = await loadImageWithFallback(checklist.inspectorSignature, 'signature');
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    } catch (error) {
      console.error('Error loading checklist images:', error);
    }

    return imageDataUrls;
  }
} 
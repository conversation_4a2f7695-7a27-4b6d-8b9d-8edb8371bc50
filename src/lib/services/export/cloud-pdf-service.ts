import { getAuth } from 'firebase/auth';
import { ChecklistData } from '@/types/checklist';
import { CLOUD_FUNCTION_URLS } from '@/lib/config/cloud-functions';
import { log } from '@/lib/utils/logger';

export interface CloudPDFResult {
  downloadUrl: string;
  fileName: string;
  fileSize: number;
  expiresAt: string;
}

// Type for cloud function response
interface CloudPDFResponse {
  success: boolean;
  data?: CloudPDFResult;
  error?: string;
}

export class CloudPDFService {
  
  /**
   * Generate PDF using cloud function HTTP endpoint (replaces client-side generation)
   */
  static async generatePDF(checklist: ChecklistData): Promise<CloudPDFResult> {
    try {
      log.info('Starting cloud PDF generation', 'CLOUD_PDF', {
        checklistId: checklist.id
      });

      // Get authentication token
      const auth = getAuth();
      const user = auth.currentUser;
      
      if (!user) {
        throw new Error('User not authenticated');
      }

      const idToken = await user.getIdToken();

      // Determine the function URL
      const functionUrl = process.env.NODE_ENV === 'production' 
        ? `https://asia-east1-auburn-engineering.cloudfunctions.net/generateIndividualPDFHttp`
        : `http://127.0.0.1:5001/auburn-engineering/asia-east1/generateIndividualPDFHttp`;

      // Call HTTP cloud function
      const response = await fetch(functionUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${idToken}`,
        },
        body: JSON.stringify({
          checklistId: checklist.id
        })
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`HTTP ${response.status}: ${errorText}`);
      }

      const result: CloudPDFResponse = await response.json();

      if (!result.success || !result.data) {
        throw new Error(result.error || 'Cloud function returned failure status');
      }

      log.info('Cloud PDF generation completed', 'CLOUD_PDF', {
        checklistId: checklist.id,
        fileSize: result.data.fileSize,
        fileName: result.data.fileName
      });

      return result.data;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      log.error('Cloud PDF generation failed', 'CLOUD_PDF', {
        checklistId: checklist.id,
        error: errorMessage
      });

      throw new Error(`Failed to generate PDF: ${errorMessage}`);
    }
  }

  /**
   * Generate PDF and trigger download
   */
  static async generateAndDownloadPDF(checklist: ChecklistData): Promise<void> {
    try {
      // Generate PDF via cloud function
      const result = await this.generatePDF(checklist);
      
      // Create download link that opens in new tab
      const link = document.createElement('a');
      link.href = result.downloadUrl;
      link.download = result.fileName;
      link.target = '_blank'; // Open in new tab
      link.rel = 'noopener noreferrer'; // Security best practice
      link.style.display = 'none';
      
      // Trigger download
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      log.info('PDF download initiated', 'CLOUD_PDF', {
        checklistId: checklist.id,
        fileName: result.fileName
      });

    } catch (error) {
      log.error('PDF download failed', 'CLOUD_PDF', {
        checklistId: checklist.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Generate filename for PDF
   */
  static generateFileName(checklist: ChecklistData): string {
    const date = new Date().toISOString().slice(0, 10);
    const tagNo = checklist.generalInfo.tagNo?.replace(/[^a-zA-Z0-9]/g, '_') || 'checklist';
    const equipment = checklist.generalInfo.equipmentName?.slice(0, 20).replace(/[^a-zA-Z0-9]/g, '_') || 'equipment';
    
    return `PPM_${tagNo}_${equipment}_${date}.pdf`;
  }
} 
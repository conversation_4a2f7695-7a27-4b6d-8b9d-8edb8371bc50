import { 
  collection, 
  doc, 
  addDoc, 
  updateDoc, 
  deleteDoc,
  getDoc,
  query, 
  where, 
  orderBy, 
  onSnapshot,
  serverTimestamp,
  Timestamp,
  getDocs
} from 'firebase/firestore';
import { db } from '@/config/firebase-persistence';
import { ChecklistData } from '@/types/checklist';
import { ExportQueueItem, ExportQueueOptions, ExportMetadata } from '@/types/export-queue';
import { format } from 'date-fns';
import { log } from '@/lib/utils/logger';

export class ExportQueueService {
  private static readonly QUEUE_COLLECTION = 'export-queue';
  private static readonly MAX_QUEUE_SIZE = 50; // Per user limit

  /**
   * Add a new export request to the queue
   */
  static async addToQueue(
    userId: string,
    type: 'pdf' | 'excel',
    checklists: ChecklistData[],
    userDisplayName: string,
    options: ExportQueueOptions = {}
  ): Promise<string> {
    try {
      // Check queue size limit
      await this.enforceQueueLimit(userId);

      // Prepare metadata
      const clients = Array.from(new Set(checklists.map(c => c.generalInfo.clientName)));
      const dates = checklists.map(c => new Date(c.generalInfo.date)).sort((a, b) => a.getTime() - b.getTime());
      
      const metadata: ExportMetadata = {
        checklistCount: checklists.length,
        clientName: clients.length === 1 ? clients[0] : `${clients.length} clients`,
        dateRange: dates.length > 1 
          ? `${format(dates[0], 'MMM dd')} - ${format(dates[dates.length - 1], 'MMM dd, yyyy')}`
          : format(dates[0] || new Date(), 'MMM dd, yyyy'),
        requestedBy: userDisplayName
      };

      // Create queue item
      const queueItem: Omit<ExportQueueItem, 'id'> = {
        userId,
        type,
        status: 'pending',
        checklistIds: checklists.map(c => c.id),
        checklistCount: checklists.length,
        createdAt: serverTimestamp() as Timestamp,
        progress: {
          currentIndex: 0,
          totalCount: checklists.length,
          percentage: 0,
          stage: 'queued'
        },
        metadata: {
          ...metadata,
          exportMethod: 'cloud-function'
        }
      };

      // Add to Firestore
      const docRef = await addDoc(collection(db, this.QUEUE_COLLECTION), queueItem);

      log.info('Export added to queue', 'EXPORT_QUEUE', {
        queueId: docRef.id,
        userId,
        type,
        checklistCount: checklists.length
      });

      return docRef.id;

    } catch (error) {
      log.error('Failed to add export to queue', 'EXPORT_QUEUE', {
        userId,
        type,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw new Error(`Failed to add export to queue: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Update queue item progress
   */
  static async updateProgress(
    queueId: string,
    progress: Partial<ExportQueueItem['progress']>,
    status?: ExportQueueItem['status']
  ): Promise<void> {
    try {
      const docRef = doc(db, this.QUEUE_COLLECTION, queueId);
      const updateData: any = {
        progress,
        updatedAt: serverTimestamp()
      };

      if (status) {
        updateData.status = status;
        if (status === 'processing' && !updateData.startedAt) {
          updateData.startedAt = serverTimestamp();
        }
      }

      await updateDoc(docRef, updateData);

    } catch (error) {
      log.error('Failed to update queue progress', 'EXPORT_QUEUE', {
        queueId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Complete a queue item with results
   */
  static async completeQueueItem(
    queueId: string,
    result: ExportQueueItem['result']
  ): Promise<void> {
    try {
      const docRef = doc(db, this.QUEUE_COLLECTION, queueId);
      
      await updateDoc(docRef, {
        status: 'completed',
        completedAt: serverTimestamp(),
        result,
        progress: {
          currentIndex: result?.processedCount || 0,
          totalCount: result?.processedCount || 0,
          percentage: 100,
          stage: 'complete'
        }
      });

      log.info('Export queue item completed', 'EXPORT_QUEUE', {
        queueId,
        fileName: result?.fileName,
        fileSize: result?.fileSize
      });

    } catch (error) {
      log.error('Failed to complete queue item', 'EXPORT_QUEUE', {
        queueId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Mark a queue item as failed
   */
  static async failQueueItem(queueId: string, error: string): Promise<void> {
    try {
      const docRef = doc(db, this.QUEUE_COLLECTION, queueId);
      
      await updateDoc(docRef, {
        status: 'failed',
        completedAt: serverTimestamp(),
        error,
        progress: {
          currentIndex: 0,
          totalCount: 0,
          percentage: 0,
          stage: 'failed'
        }
      });

      log.error('Export queue item failed', 'EXPORT_QUEUE', {
        queueId,
        error
      });

    } catch (updateError) {
      log.error('Failed to update failed queue item', 'EXPORT_QUEUE', {
        queueId,
        originalError: error,
        updateError: updateError instanceof Error ? updateError.message : 'Unknown error'
      });
    }
  }

  /**
   * Cancel a pending queue item
   */
  static async cancelQueueItem(queueId: string, userId: string): Promise<void> {
    try {
      const docRef = doc(db, this.QUEUE_COLLECTION, queueId);
      
      // Only allow canceling own pending items
      await updateDoc(docRef, {
        status: 'failed',
        error: 'Cancelled by user',
        completedAt: serverTimestamp()
      });

      log.info('Export queue item cancelled', 'EXPORT_QUEUE', {
        queueId,
        userId
      });

    } catch (error) {
      log.error('Failed to cancel queue item', 'EXPORT_QUEUE', {
        queueId,
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Delete a completed or failed queue item
   */
  static async deleteQueueItem(queueId: string, userId: string): Promise<void> {
    try {
      const docRef = doc(db, this.QUEUE_COLLECTION, queueId);
      
      // Get the document first to access storage path
      const docSnapshot = await getDoc(docRef);
      const queueData = docSnapshot.data() as ExportQueueItem | undefined;
      
      // Delete from storage if storage path exists
      if (queueData?.result?.storagePath) {
        try {
          const { ExportStorageService } = await import('../storage/export-storage-service');
          await ExportStorageService.deleteExport(queueData.result.storagePath);
          
          log.info('Export file deleted from storage', 'EXPORT_QUEUE', {
            queueId,
            userId,
            storagePath: queueData.result.storagePath
          });
        } catch (storageError) {
          // Log storage deletion error but don't fail the whole operation
          log.warn('Failed to delete export from storage', 'EXPORT_QUEUE', {
            queueId,
            userId,
            storagePath: queueData.result.storagePath,
            error: storageError instanceof Error ? storageError.message : 'Unknown error'
          });
        }
      }
      
      // Delete the document
      await deleteDoc(docRef);

      log.info('Export queue item deleted', 'EXPORT_QUEUE', {
        queueId,
        userId
      });

    } catch (error) {
      log.error('Failed to delete queue item', 'EXPORT_QUEUE', {
        queueId,
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get user's queue items with real-time updates
   */
  static subscribeToUserQueue(
    userId: string,
    callback: (items: ExportQueueItem[]) => void
  ): () => void {
    const q = query(
      collection(db, this.QUEUE_COLLECTION),
      where('userId', '==', userId),
      orderBy('createdAt', 'desc')
    );

    return onSnapshot(q, (snapshot) => {
      const items = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ExportQueueItem[];

      callback(items);
    }, (error) => {
      log.error('Queue subscription error', 'EXPORT_QUEUE', {
        userId,
        error: error.message
      });
      callback([]);
    });
  }

  /**
   * Get all queue items with real-time updates (admin only)
   */
  static subscribeToAllQueue(
    callback: (items: ExportQueueItem[]) => void
  ): () => void {
    const q = query(
      collection(db, this.QUEUE_COLLECTION),
      orderBy('createdAt', 'desc')
    );

    return onSnapshot(q, (snapshot) => {
      const items = snapshot.docs.map(doc => ({
        id: doc.id,
        ...doc.data()
      })) as ExportQueueItem[];

      callback(items);
    }, (error) => {
      log.error('All queue subscription error', 'EXPORT_QUEUE', {
        error: error.message
      });
      callback([]);
    });
  }

  /**
   * Clean up old completed/failed queue items
   */
  static async cleanupOldQueueItems(userId: string, daysToKeep: number = 7): Promise<number> {
    try {
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const q = query(
        collection(db, this.QUEUE_COLLECTION),
        where('userId', '==', userId),
        where('completedAt', '<', Timestamp.fromDate(cutoffDate))
      );

      const snapshot = await getDocs(q);
      let cleanedCount = 0;

      for (const docSnapshot of snapshot.docs) {
        await deleteDoc(docSnapshot.ref);
        cleanedCount++;
      }

      log.info('Queue cleanup completed', 'EXPORT_QUEUE', {
        userId,
        cleanedCount
      });

      return cleanedCount;

    } catch (error) {
      log.error('Failed to cleanup old queue items', 'EXPORT_QUEUE', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return 0;
    }
  }

  /**
   * Enforce queue size limit per user
   */
  private static async enforceQueueLimit(userId: string): Promise<void> {
    try {
      const q = query(
        collection(db, this.QUEUE_COLLECTION),
        where('userId', '==', userId),
        where('status', 'in', ['pending', 'processing'])
      );

      const snapshot = await getDocs(q);
      
      if (snapshot.size >= this.MAX_QUEUE_SIZE) {
        throw new Error(`Queue limit reached. Maximum ${this.MAX_QUEUE_SIZE} pending exports allowed.`);
      }

    } catch (error) {
      if (error instanceof Error && error.message.includes('Queue limit')) {
        throw error;
      }
      log.warn('Failed to check queue limit', 'EXPORT_QUEUE', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get queue statistics for a user
   */
  static async getQueueStats(userId: string): Promise<{
    pending: number;
    processing: number;
    completed: number;
    failed: number;
    total: number;
  }> {
    try {
      const q = query(
        collection(db, this.QUEUE_COLLECTION),
        where('userId', '==', userId)
      );

      const snapshot = await getDocs(q);
      const stats = {
        pending: 0,
        processing: 0,
        completed: 0,
        failed: 0,
        total: snapshot.size
      };

      snapshot.docs.forEach(doc => {
        const data = doc.data() as ExportQueueItem;
        stats[data.status]++;
      });

      return stats;

    } catch (error) {
      log.error('Failed to get queue stats', 'EXPORT_QUEUE', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return { pending: 0, processing: 0, completed: 0, failed: 0, total: 0 };
    }
  }
} 
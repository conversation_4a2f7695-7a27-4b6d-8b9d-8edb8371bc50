import { 
  collection, 
  doc, 
  setDoc, 
  updateDoc, 
  deleteDoc, 
  getDoc, 
  getDocs, 
  query, 
  orderBy, 
  serverTimestamp,
  Timestamp,
  where
} from 'firebase/firestore';
import { db } from '@/config/firebase-persistence';
import { EquipmentTag, EquipmentTagFormData, QRCodeContent, formatQRCodeContent, generateQRCodeFilename } from '@/types/equipment-tag';
import QRCode from 'qrcode';

const EQUIPMENT_TAGS_COLLECTION = 'equipment-tags';

export class EquipmentTagService {
  /**
   * Create a new equipment tag
   */
  static async createEquipmentTag(
    formData: EquipmentTagFormData, 
    userId: string
  ): Promise<EquipmentTag> {
    try {
      // Generate unique ID for the equipment tag
      const tagRef = doc(collection(db, EQUIPMENT_TAGS_COLLECTION));
      const tagId = tagRef.id;
      
      // Prepare QR code content
      const qrContent: QRCodeContent = {
        contractor: 'Auburn Engineering WLL',
        clientName: formData.clientName,
        equipmentName: formData.equipmentName,
        tagNumber: formData.tagNumber,
        dateOfCreation: formData.dateOfCreation,
        building: formData.building,
        location: formData.location,
      };
      
      // Generate QR code as base64 string
      const qrCodeText = formatQRCodeContent(qrContent);
      const qrCodeData = await QRCode.toDataURL(qrCodeText, {
        errorCorrectionLevel: 'M',
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        width: 512
      });
      
      // Prepare equipment tag data
      const equipmentTag: Omit<EquipmentTag, 'id' | 'createdAt' | 'updatedAt'> = {
        contractor: 'Auburn Engineering WLL',
        clientName: formData.clientName,
        equipmentName: formData.equipmentName,
        tagNumber: formData.tagNumber,
        dateOfCreation: formData.dateOfCreation,
        building: formData.building,
        location: formData.location,
        qrCodeData,
        createdBy: userId,
      };
      
      // Save to Firestore
      await setDoc(tagRef, {
        ...equipmentTag,
        createdAt: serverTimestamp(),
        updatedAt: serverTimestamp(),
      });
      
      // Return the created equipment tag
      return {
        ...equipmentTag,
        id: tagId,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };
    } catch (error) {
      console.error('Error creating equipment tag:', error);
      throw new Error('Failed to create equipment tag');
    }
  }
  
  /**
   * Get all equipment tags
   */
  static async getAllEquipmentTags(): Promise<EquipmentTag[]> {
    try {
      const tagsRef = collection(db, EQUIPMENT_TAGS_COLLECTION);
      const q = query(tagsRef, orderBy('createdAt', 'desc'));
      
      const querySnapshot = await getDocs(q);
      const tags: EquipmentTag[] = [];
      
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        
        // Convert Firestore timestamps
        const createdAt = data.createdAt instanceof Timestamp 
          ? data.createdAt.toDate().toISOString()
          : data.createdAt;
        const updatedAt = data.updatedAt instanceof Timestamp 
          ? data.updatedAt.toDate().toISOString()
          : data.updatedAt;
        
        tags.push({
          ...data,
          id: doc.id,
          createdAt,
          updatedAt,
        } as EquipmentTag);
      });
      
      return tags;
    } catch (error) {
      console.error('Error fetching equipment tags:', error);
      throw new Error('Failed to fetch equipment tags');
    }
  }
  
  /**
   * Get equipment tag by ID
   */
  static async getEquipmentTagById(tagId: string): Promise<EquipmentTag | null> {
    try {
      const tagRef = doc(db, EQUIPMENT_TAGS_COLLECTION, tagId);
      const tagDoc = await getDoc(tagRef);
      
      if (!tagDoc.exists()) {
        return null;
      }
      
      const data = tagDoc.data();
      
      // Convert Firestore timestamps
      const createdAt = data.createdAt instanceof Timestamp 
        ? data.createdAt.toDate().toISOString()
        : data.createdAt;
      const updatedAt = data.updatedAt instanceof Timestamp 
        ? data.updatedAt.toDate().toISOString()
        : data.updatedAt;
      
      return {
        ...data,
        id: tagDoc.id,
        createdAt,
        updatedAt,
      } as EquipmentTag;
    } catch (error) {
      console.error('Error fetching equipment tag:', error);
      throw new Error('Failed to fetch equipment tag');
    }
  }
  
  /**
   * Check if equipment tag is referenced by any checklists
   */
  static async getChecklistReferences(tagId: string): Promise<{
    hasReferences: boolean;
    checklistCount: number;
    checklistIds: string[];
  }> {
    try {
      const checklistsRef = collection(db, 'checklists');
      const q = query(checklistsRef, where('equipmentTagId', '==', tagId));
      
      const querySnapshot = await getDocs(q);
      const checklistIds = querySnapshot.docs.map(doc => doc.id);
      
      return {
        hasReferences: !querySnapshot.empty,
        checklistCount: querySnapshot.size,
        checklistIds
      };
    } catch (error) {
      console.error('Error checking checklist references:', error);
      throw new Error('Failed to check checklist references');
    }
  }

  /**
   * Delete an equipment tag with referential integrity check
   */
  static async deleteEquipmentTag(tagId: string, options?: { force?: boolean }): Promise<void> {
    try {
      // Check for checklist references unless force delete is requested
      if (!options?.force) {
        const references = await this.getChecklistReferences(tagId);
        
        if (references.hasReferences) {
          throw new Error(
            `Cannot delete equipment tag. It is referenced by ${references.checklistCount} checklist(s). ` +
            `Please delete all associated checklists first, or use force delete option.`
          );
        }
      }

      const tagRef = doc(db, EQUIPMENT_TAGS_COLLECTION, tagId);
      await deleteDoc(tagRef);
    } catch (error) {
      console.error('Error deleting equipment tag:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to delete equipment tag');
    }
  }

  /**
   * Update an equipment tag with referential integrity validation
   */
  static async updateEquipmentTag(
    tagId: string, 
    formData: EquipmentTagFormData,
    options?: { skipReferenceValidation?: boolean }
  ): Promise<void> {
    try {
      const tagRef = doc(db, EQUIPMENT_TAGS_COLLECTION, tagId);
      
      // Get current tag data to preserve creation info
      const currentTag = await this.getEquipmentTagById(tagId);
      if (!currentTag) {
        throw new Error('Equipment tag not found');
      }

      // Check if critical fields are being changed and validate impact on checklists
      if (!options?.skipReferenceValidation) {
        const criticalFieldsChanged = 
          currentTag.tagNumber !== formData.tagNumber ||
          currentTag.clientName !== formData.clientName ||
          currentTag.equipmentName !== formData.equipmentName;

        if (criticalFieldsChanged) {
          const references = await this.getChecklistReferences(tagId);
          
          if (references.hasReferences) {
            throw new Error(
              `Cannot modify critical fields (tag number, client name, or equipment name) ` +
              `because this equipment tag is referenced by ${references.checklistCount} checklist(s). ` +
              `Please update or delete the associated checklists first.`
            );
          }
        }
      }
      
      // Prepare updated QR code content
      const qrContent: QRCodeContent = {
        contractor: 'Auburn Engineering WLL',
        clientName: formData.clientName,
        equipmentName: formData.equipmentName,
        tagNumber: formData.tagNumber,
        dateOfCreation: formData.dateOfCreation, // Use editable creation date
        building: formData.building,
        location: formData.location,
      };
      
      // Generate new QR code
      const qrCodeText = formatQRCodeContent(qrContent);
      const qrCodeData = await QRCode.toDataURL(qrCodeText, {
        errorCorrectionLevel: 'M',
        margin: 1,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        },
        width: 512
      });
      
      // Update document
      await updateDoc(tagRef, {
        clientName: formData.clientName,
        equipmentName: formData.equipmentName,
        tagNumber: formData.tagNumber,
        building: formData.building,
        location: formData.location,
        dateOfCreation: formData.dateOfCreation,
        qrCodeData,
        updatedAt: serverTimestamp(),
      });
    } catch (error) {
      console.error('Error updating equipment tag:', error);
      if (error instanceof Error) {
        throw error;
      }
      throw new Error('Failed to update equipment tag');
    }
  }

  /**
   * Delete all checklists associated with an equipment tag
   */
  static async deleteAssociatedChecklists(tagId: string): Promise<{
    deletedCount: number;
    failedCount: number;
    errors: string[];
  }> {
    try {
      const references = await this.getChecklistReferences(tagId);
      
      if (!references.hasReferences) {
        return { deletedCount: 0, failedCount: 0, errors: [] };
      }

      // Import AdminChecklistService dynamically to avoid circular dependencies
      const { AdminChecklistService } = await import('../checklist/admin-checklist-service');
      
      const results = await AdminChecklistService.bulkDeleteChecklists(references.checklistIds);
      
      return {
        deletedCount: results.success.length,
        failedCount: results.failed.length,
        errors: results.failed.map(id => `Failed to delete checklist ${id}`)
      };
    } catch (error) {
      console.error('Error deleting associated checklists:', error);
      throw new Error('Failed to delete associated checklists');
    }
  }

  /**
   * Check if tag number already exists for a specific client
   */
  static async isTagNumberUniqueForClient(
    tagNumber: string, 
    clientName: string, 
    excludeId?: string
  ): Promise<boolean> {
    try {
      const tagsRef = collection(db, EQUIPMENT_TAGS_COLLECTION);
      const q = query(
        tagsRef, 
        where('tagNumber', '==', tagNumber),
        where('clientName', '==', clientName)
      );
      
      const querySnapshot = await getDocs(q);
      
      // If excluding an ID (for updates), check if any other document has this tag number for this client
      if (excludeId) {
        return !querySnapshot.docs.some(doc => doc.id !== excludeId);
      }
      
      // For new tags, simply check if any document exists with this tag number for this client
      return querySnapshot.empty;
    } catch (error) {
      console.error('Error checking tag number uniqueness for client:', error);
      return false; // Assume not unique on error to be safe
    }
  }

  /**
   * @deprecated Use isTagNumberUniqueForClient instead
   * Check if tag number already exists (globally - deprecated)
   */
  static async isTagNumberUnique(tagNumber: string, excludeId?: string): Promise<boolean> {
    try {
      const tagsRef = collection(db, EQUIPMENT_TAGS_COLLECTION);
      const q = query(tagsRef, where('tagNumber', '==', tagNumber));
      
      const querySnapshot = await getDocs(q);
      
      // If excluding an ID (for updates), check if any other document has this tag number
      if (excludeId) {
        return !querySnapshot.docs.some(doc => doc.id !== excludeId);
      }
      
      // For new tags, simply check if any document exists with this tag number
      return querySnapshot.empty;
    } catch (error) {
      console.error('Error checking tag number uniqueness:', error);
      return false; // Assume not unique on error to be safe
    }
  }
  
  /**
   * Download QR code as PNG
   */
  static downloadQRCode(qrCodeData: string, tagNumber: string): void {
    try {
      const filename = generateQRCodeFilename(tagNumber);
      
      // Create download link
      const link = document.createElement('a');
      link.href = qrCodeData;
      link.download = `${filename}.png`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } catch (error) {
      console.error('Error downloading QR code:', error);
      throw new Error('Failed to download QR code');
    }
  }
  
  /**
   * Print QR code
   */
  static printQRCode(qrCodeData: string, equipmentTag: EquipmentTag): void {
    try {
      const printWindow = window.open('', '_blank');
      if (!printWindow) {
        throw new Error('Unable to open print window');
      }
      
      printWindow.document.write(`
        <!DOCTYPE html>
        <html>
        <head>
          <title>Equipment Tag - ${equipmentTag.tagNumber}</title>
          <style>
            body {
              font-family: Arial, sans-serif;
              text-align: center;
              margin: 20px;
              background: white;
            }
            .header {
              margin-bottom: 20px;
            }
            .qr-code {
              margin: 20px 0;
            }
            .details {
              margin-top: 20px;
              text-align: left;
              max-width: 400px;
              margin-left: auto;
              margin-right: auto;
            }
            .details div {
              margin: 5px 0;
              padding: 5px;
              border-bottom: 1px solid #eee;
            }
            .label {
              font-weight: bold;
              display: inline-block;
              width: 120px;
            }
            @media print {
              body { margin: 0; }
            }
          </style>
        </head>
        <body>
          <div class="header">
            <h1>Equipment Tag</h1>
            <h2>${equipmentTag.tagNumber}</h2>
          </div>
          
          <div class="qr-code">
            <img src="${qrCodeData}" alt="QR Code" style="max-width: 300px; height: auto;" />
          </div>
          
          <div class="details">
            <div><span class="label">Contractor:</span> ${equipmentTag.contractor}</div>
            <div><span class="label">Client:</span> ${equipmentTag.clientName}</div>
            <div><span class="label">Equipment:</span> ${equipmentTag.equipmentName}</div>
            <div><span class="label">Tag Number:</span> ${equipmentTag.tagNumber}</div>
            <div><span class="label">Date Created:</span> ${equipmentTag.dateOfCreation}</div>
            <div><span class="label">Building:</span> ${equipmentTag.building}</div>
            <div><span class="label">Location:</span> ${equipmentTag.location}</div>
          </div>
          
          <script>
            window.onload = function() {
              window.print();
              window.close();
            };
          </script>
        </body>
        </html>
      `);
      
      printWindow.document.close();
    } catch (error) {
      console.error('Error printing QR code:', error);
      throw new Error('Failed to print QR code');
    }
  }
  
  /**
   * Get equipment tag by tag number and client name
   */
  static async getEquipmentTagByTagNumberAndClient(
    tagNumber: string, 
    clientName: string
  ): Promise<EquipmentTag | null> {
    try {
      const tagsRef = collection(db, EQUIPMENT_TAGS_COLLECTION);
      const q = query(
        tagsRef, 
        where('tagNumber', '==', tagNumber),
        where('clientName', '==', clientName)
      );
      
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return null;
      }
      
      // Get the first matching document (should be unique due to client-specific constraint)
      const doc = querySnapshot.docs[0];
      const data = doc.data();
      
      // Convert Firestore timestamps
      const createdAt = data.createdAt instanceof Timestamp 
        ? data.createdAt.toDate().toISOString()
        : data.createdAt;
      const updatedAt = data.updatedAt instanceof Timestamp 
        ? data.updatedAt.toDate().toISOString()
        : data.updatedAt;
      
      return {
        ...data,
        id: doc.id,
        createdAt,
        updatedAt,
      } as EquipmentTag;
    } catch (error) {
      console.error('Error fetching equipment tag by tag number and client:', error);
      throw new Error('Failed to fetch equipment tag');
    }
  }

  /**
   * Get equipment tag by tag number (returns first match - may return unexpected results with duplicate tag numbers)
   * @deprecated Use getEquipmentTagByTagNumberAndClient for better accuracy when duplicate tag numbers exist across clients
   */
  static async getEquipmentTagByTagNumber(tagNumber: string): Promise<EquipmentTag | null> {
    try {
      const tagsRef = collection(db, EQUIPMENT_TAGS_COLLECTION);
      const q = query(tagsRef, where('tagNumber', '==', tagNumber));
      
      const querySnapshot = await getDocs(q);
      
      if (querySnapshot.empty) {
        return null;
      }
      
      // Get the first matching document
      const doc = querySnapshot.docs[0];
      const data = doc.data();
      
      // Convert Firestore timestamps
      const createdAt = data.createdAt instanceof Timestamp 
        ? data.createdAt.toDate().toISOString()
        : data.createdAt;
      const updatedAt = data.updatedAt instanceof Timestamp 
        ? data.updatedAt.toDate().toISOString()
        : data.updatedAt;
      
      return {
        ...data,
        id: doc.id,
        createdAt,
        updatedAt,
      } as EquipmentTag;
    } catch (error) {
      console.error('Error fetching equipment tag by tag number:', error);
      throw new Error('Failed to fetch equipment tag');
    }
  }

  /**
   * Get all equipment tags by tag number (useful when multiple clients use the same tag number)
   */
  static async getAllEquipmentTagsByTagNumber(tagNumber: string): Promise<EquipmentTag[]> {
    try {
      const tagsRef = collection(db, EQUIPMENT_TAGS_COLLECTION);
      const q = query(tagsRef, where('tagNumber', '==', tagNumber));
      
      const querySnapshot = await getDocs(q);
      const tags: EquipmentTag[] = [];
      
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        
        // Convert Firestore timestamps
        const createdAt = data.createdAt instanceof Timestamp 
          ? data.createdAt.toDate().toISOString()
          : data.createdAt;
        const updatedAt = data.updatedAt instanceof Timestamp 
          ? data.updatedAt.toDate().toISOString()
          : data.updatedAt;
        
        tags.push({
          ...data,
          id: doc.id,
          createdAt,
          updatedAt,
        } as EquipmentTag);
      });
      
      return tags;
    } catch (error) {
      console.error('Error fetching equipment tags by tag number:', error);
      throw new Error('Failed to fetch equipment tags');
    }
  }

  /**
   * Search equipment tags by partial tag number
   */
  static async searchEquipmentTagsByTagNumber(partialTagNumber: string, limit: number = 10): Promise<EquipmentTag[]> {
    try {
      if (!partialTagNumber.trim()) {
        return [];
      }

      const tagsRef = collection(db, EQUIPMENT_TAGS_COLLECTION);
      // Note: Firestore doesn't support case-insensitive partial text search natively
      // This implementation does a starts-with search
      const q = query(
        tagsRef, 
        where('tagNumber', '>=', partialTagNumber),
        where('tagNumber', '<=', partialTagNumber + '\uf8ff'),
        orderBy('tagNumber'),
        // Using orderBy with limit for better performance
      );
      
      const querySnapshot = await getDocs(q);
      const tags: EquipmentTag[] = [];
      
      querySnapshot.forEach((doc) => {
        if (tags.length < limit) {
          const data = doc.data();
          
          // Convert Firestore timestamps
          const createdAt = data.createdAt instanceof Timestamp 
            ? data.createdAt.toDate().toISOString()
            : data.createdAt;
          const updatedAt = data.updatedAt instanceof Timestamp 
            ? data.updatedAt.toDate().toISOString()
            : data.updatedAt;
          
          tags.push({
            ...data,
            id: doc.id,
            createdAt,
            updatedAt,
          } as EquipmentTag);
        }
      });
      
      return tags;
    } catch (error) {
      console.error('Error searching equipment tags:', error);
      
      // Provide more specific error messages based on the error type
      if (error instanceof Error) {
        // Check for permission errors
        if (error.message.includes('Missing or insufficient permissions') || 
            error.message.includes('permission-denied')) {
          throw new Error('You do not have permission to access equipment tags. Please contact an administrator to update your account permissions.');
        }
        
        // Check for authentication errors
        if (error.message.includes('unauthenticated')) {
          throw new Error('You must be signed in to search equipment tags. Please sign in and try again.');
        }
        
        // Check for network errors
        if (error.message.includes('network') || error.message.includes('offline')) {
          throw new Error('Network error: Please check your internet connection and try again.');
        }
      }
      
      // Generic fallback error message
      throw new Error('Failed to search equipment tags. Please try again or contact support if the problem persists.');
    }
  }

  /**
   * Parse QR code content to extract tag number
   * Expects QR content in the format generated by formatQRCodeContent
   */
  static parseQRCodeContent(qrContent: string): { tagNumber: string; isValid: boolean; data?: Partial<QRCodeContent> } {
    try {
      // Expected format:
      // Contractor: Auburn Engineering WLL
      // Client: [Client Name]
      // Equipment: [Equipment Name]
      // Tag: [Tag Number]
      // Date: [Date]
      // Building: [Building]
      // Location: [Location]
      
      const lines = qrContent.split('\n').map(line => line.trim()).filter(line => line);
      const data: Partial<QRCodeContent> = {};
      
      for (const line of lines) {
        const [key, ...valueParts] = line.split(':');
        const value = valueParts.join(':').trim();
        
        switch (key.trim()) {
          case 'Contractor':
            data.contractor = value;
            break;
          case 'Client':
            data.clientName = value;
            break;
          case 'Equipment':
            data.equipmentName = value;
            break;
          case 'Tag':
            data.tagNumber = value;
            break;
          case 'Date':
            data.dateOfCreation = value;
            break;
          case 'Building':
            data.building = value;
            break;
          case 'Location':
            data.location = value;
            break;
        }
      }
      
      // Validate required fields
      const isValid = !!(
        data.contractor === 'Auburn Engineering WLL' &&
        data.tagNumber &&
        data.clientName &&
        data.equipmentName
      );
      
      return {
        tagNumber: data.tagNumber || '',
        isValid,
        data: isValid ? data as QRCodeContent : undefined
      };
    } catch (error) {
      console.error('Error parsing QR code content:', error);
      return {
        tagNumber: '',
        isValid: false
      };
    }
  }

  /**
   * Validate if QR code content matches an existing equipment tag
   */
  static async validateQRCodeTag(qrContent: string): Promise<{
    isValid: boolean;
    equipmentTag?: EquipmentTag;
    error?: string;
  }> {
    try {
      const parsed = this.parseQRCodeContent(qrContent);
      
      if (!parsed.isValid || !parsed.data || !parsed.data.clientName) {
        return {
          isValid: false,
          error: 'Invalid QR code format'
        };
      }

      // Use client-specific lookup for more accurate validation
      const equipmentTag = await this.getEquipmentTagByTagNumberAndClient(
        parsed.tagNumber, 
        parsed.data.clientName
      );
      
      if (!equipmentTag) {
        return {
          isValid: false,
          error: 'Equipment tag not found in database'
        };
      }
      
      // Validate other fields match
      const dataMatches = 
        parsed.data?.clientName === equipmentTag.clientName &&
        parsed.data?.equipmentName === equipmentTag.equipmentName &&
        parsed.data?.building === equipmentTag.building &&
        parsed.data?.location === equipmentTag.location;
      
      if (!dataMatches) {
        return {
          isValid: false,
          error: 'QR code data does not match database record'
        };
      }
      
      return {
        isValid: true,
        equipmentTag
      };
    } catch (error) {
      console.error('Error validating QR code tag:', error);
      return {
        isValid: false,
        error: 'Failed to validate QR code'
      };
    }
  }

  /**
   * Get all equipment tags for a specific client
   */
  static async getEquipmentTagsByClient(clientName: string): Promise<EquipmentTag[]> {
    try {
      const tagsRef = collection(db, EQUIPMENT_TAGS_COLLECTION);
      const q = query(
        tagsRef, 
        where('clientName', '==', clientName),
        orderBy('createdAt', 'desc')
      );
      
      const querySnapshot = await getDocs(q);
      const tags: EquipmentTag[] = [];
      
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        
        // Convert Firestore timestamps
        const createdAt = data.createdAt instanceof Timestamp 
          ? data.createdAt.toDate().toISOString()
          : data.createdAt;
        const updatedAt = data.updatedAt instanceof Timestamp 
          ? data.updatedAt.toDate().toISOString()
          : data.updatedAt;
        
        tags.push({
          ...data,
          id: doc.id,
          createdAt,
          updatedAt,
        } as EquipmentTag);
      });
      
      return tags;
    } catch (error) {
      console.error('Error fetching equipment tags by client:', error);
      throw new Error('Failed to fetch equipment tags by client');
    }
  }

  /**
   * Get equipment tags with their associated checklist information including PPM attempts
   */
  static async getEquipmentTagsWithChecklistInfo(): Promise<Array<EquipmentTag & {
    hasChecklists: boolean;
    checklistCount: number;
    lastChecklistDate?: string;
    availableAttempts: number[];
    latestAttempt?: number;
  }>> {
    try {
      // Get all equipment tags
      const tags = await this.getAllEquipmentTags();
      
      // Get all checklists to map to equipment tags
      const checklistsRef = collection(db, 'checklists');
      const checklistsQuery = query(checklistsRef);
      const checklistsSnapshot = await getDocs(checklistsQuery);
      
      // Create a map of equipment tag ID to checklist information
      const checklistInfoMap = new Map<string, {
        count: number;
        lastDate?: string;
        attempts: Set<number>;
        latestAttempt?: number;
      }>();
      
      checklistsSnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.equipmentTagId) {
          const existing = checklistInfoMap.get(data.equipmentTagId) || {
            count: 0,
            attempts: new Set<number>()
          };
          
          existing.count++;
          
          // Track PPM attempts
          if (data.generalInfo?.ppmAttempt) {
            existing.attempts.add(data.generalInfo.ppmAttempt);
            if (!existing.latestAttempt || data.generalInfo.ppmAttempt > existing.latestAttempt) {
              existing.latestAttempt = data.generalInfo.ppmAttempt;
            }
          }
          
          // Track latest checklist date
          const checklistDate = data.createdAt instanceof Timestamp 
            ? data.createdAt.toDate().toISOString()
            : data.createdAt;
          
          if (!existing.lastDate || checklistDate > existing.lastDate) {
            existing.lastDate = checklistDate;
          }
          
          checklistInfoMap.set(data.equipmentTagId, existing);
        }
      });
      
      // Combine equipment tags with checklist information
      return tags.map(tag => {
        const checklistInfo = checklistInfoMap.get(tag.id);
        return {
          ...tag,
          hasChecklists: !!checklistInfo,
          checklistCount: checklistInfo?.count || 0,
          lastChecklistDate: checklistInfo?.lastDate,
          availableAttempts: Array.from(checklistInfo?.attempts || []).sort((a, b) => a - b),
          latestAttempt: checklistInfo?.latestAttempt
        };
      });
    } catch (error) {
      console.error('Error fetching equipment tags with checklist info:', error);
      throw new Error('Failed to fetch equipment tags with checklist information');
    }
  }

  /**
   * Get unique equipment names for filtering
   */
  static async getUniqueEquipmentNames(): Promise<string[]> {
    try {
      const tagsRef = collection(db, EQUIPMENT_TAGS_COLLECTION);
      const querySnapshot = await getDocs(tagsRef);
      
      const equipmentNames = new Set<string>();
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.equipmentName) {
          equipmentNames.add(data.equipmentName);
        }
      });
      
      return Array.from(equipmentNames).sort();
    } catch (error) {
      console.error('Error getting unique equipment names:', error);
      throw error;
    }
  }

  /**
   * Get unique buildings for filtering
   */
  static async getUniqueBuildings(): Promise<string[]> {
    try {
      const tagsRef = collection(db, EQUIPMENT_TAGS_COLLECTION);
      const querySnapshot = await getDocs(tagsRef);
      
      const buildings = new Set<string>();
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.building) {
          buildings.add(data.building);
        }
      });
      
      return Array.from(buildings).sort();
    } catch (error) {
      console.error('Error getting unique buildings:', error);
      throw error;
    }
  }

  /**
   * Get unique locations for filtering (optionally filtered by building)
   */
  static async getUniqueLocations(building?: string): Promise<string[]> {
    try {
      let tagsQuery = query(collection(db, EQUIPMENT_TAGS_COLLECTION));
      
      if (building) {
        tagsQuery = query(tagsQuery, where('building', '==', building));
      }
      
      const querySnapshot = await getDocs(tagsQuery);
      
      const locations = new Set<string>();
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.location) {
          locations.add(data.location);
        }
      });
      
      return Array.from(locations).sort();
    } catch (error) {
      console.error('Error getting unique locations:', error);
      throw error;
    }
  }

  /**
   * Get unique clients for filtering
   */
  static async getUniqueClients(): Promise<string[]> {
    try {
      const tagsRef = collection(db, EQUIPMENT_TAGS_COLLECTION);
      const querySnapshot = await getDocs(tagsRef);
      
      const clients = new Set<string>();
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        if (data.clientName) {
          clients.add(data.clientName);
        }
      });
      
      return Array.from(clients).sort();
    } catch (error) {
      console.error('Error getting unique clients:', error);
      throw error;
    }
  }

  /**
   * Validate equipment tag for checklist creation
   */
  static async validateEquipmentTagForChecklist(
    equipmentTagId: string,
    expectedTagNumber: string,
    expectedClientName: string
  ): Promise<{
    isValid: boolean;
    equipmentTag?: EquipmentTag;
    error?: string;
  }> {
    try {
      // Get the equipment tag by ID
      const equipmentTag = await this.getEquipmentTagById(equipmentTagId);
      
      if (!equipmentTag) {
        return {
          isValid: false,
          error: `Equipment tag not found. The tag may have been deleted or the ID is invalid.`
        };
      }

      // Validate that the tag details match what's expected
      if (equipmentTag.tagNumber !== expectedTagNumber) {
        return {
          isValid: false,
          error: `Tag number mismatch. Expected "${expectedTagNumber}" but found "${equipmentTag.tagNumber}". The equipment tag may have been modified.`
        };
      }

      if (equipmentTag.clientName !== expectedClientName) {
        return {
          isValid: false,
          error: `Client name mismatch. Expected "${expectedClientName}" but found "${equipmentTag.clientName}". The equipment tag may have been modified.`
        };
      }

      return {
        isValid: true,
        equipmentTag
      };
    } catch (error) {
      console.error('Error validating equipment tag for checklist:', error);
      return {
        isValid: false,
        error: 'Failed to validate equipment tag. Please try again.'
      };
    }
  }

  /**
   * Get equipment tag validation status for checklist creation
   */
  static async getEquipmentTagValidationStatus(equipmentTagId: string): Promise<{
    exists: boolean;
    isModified: boolean;
    equipmentTag?: EquipmentTag;
    lastModified?: string;
  }> {
    try {
      const equipmentTag = await this.getEquipmentTagById(equipmentTagId);
      
      if (!equipmentTag) {
        return { exists: false, isModified: false };
      }

      // Check if the tag was recently modified (within last 24 hours)
      const lastModified = equipmentTag.updatedAt instanceof Timestamp 
        ? equipmentTag.updatedAt.toDate() 
        : new Date(equipmentTag.updatedAt);
      const now = new Date();
      const hoursSinceModified = (now.getTime() - lastModified.getTime()) / (1000 * 60 * 60);
      const isRecentlyModified = hoursSinceModified < 24;

      return {
        exists: true,
        isModified: isRecentlyModified,
        equipmentTag,
        lastModified: equipmentTag.updatedAt instanceof Timestamp 
          ? equipmentTag.updatedAt.toDate().toISOString()
          : equipmentTag.updatedAt
      };
    } catch (error) {
      console.error('Error checking equipment tag validation status:', error);
      return { exists: false, isModified: false };
    }
  }
} 
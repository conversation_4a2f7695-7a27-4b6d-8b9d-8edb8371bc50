import { COMPANY_ASSETS, CompanyAssetKey } from '@/config/company-assets';
import { log } from '@/lib/utils/logger';

/**
 * Company Assets Service
 * Handles loading of company logo and signature with simplified logic for public assets
 * 
 * Note: This service is for PUBLIC company assets (logo, signature) that don't require proxy.
 * Checklist images (private user data) still use the proxy system via cloud functions.
 */
export class CompanyAssetsService {
  private static cache = new Map<string, string>();
  private static readonly CACHE_DURATION = 30 * 60 * 1000; // 30 minutes (longer since URLs are stable)
  private static cacheTimestamps = new Map<string, number>();

  /**
   * Load company logo as data URL
   */
  static async loadLogoAsDataUrl(): Promise<string> {
    return this.loadAssetAsDataUrl('logo');
  }

  /**
   * Load company signature as data URL
   */
  static async loadSignatureAsDataUrl(): Promise<string> {
    return this.loadAssetAsDataUrl('signature');
  }

  /**
   * Load any company asset as data URL with caching and fallback
   */
  static async loadAssetAsDataUrl(assetKey: CompanyAssetKey): Promise<string> {
    const cacheKey = `company-${assetKey}`;
    
    // Check cache first
    if (this.isCacheValid(cacheKey)) {
      const cachedData = this.cache.get(cacheKey);
      if (cachedData) {
        log.debug(`Using cached ${assetKey} asset`, 'ASSETS');
        return cachedData;
      }
    }

    const asset = COMPANY_ASSETS[assetKey];
    
    try {
      // Try public Firebase Storage URL first (no token needed)
      log.debug(`Loading ${assetKey} from public Firebase Storage`, 'ASSETS', { url: asset.url });
      const dataUrl = await this.loadImageAsDataUrl(asset.url);
      
      // Cache successful result
      this.cache.set(cacheKey, dataUrl);
      this.cacheTimestamps.set(cacheKey, Date.now());
      
      log.info(`Successfully loaded ${assetKey} from Firebase Storage`, 'ASSETS');
      return dataUrl;
      
    } catch (primaryError) {
      log.warn(`Failed to load ${assetKey} from Firebase Storage, trying fallback`, 'ASSETS', {
        error: primaryError instanceof Error ? primaryError.message : 'Unknown error',
        fallback: asset.fallback
      });
      
      try {
        // Try fallback local image
        const fallbackDataUrl = await this.loadImageAsDataUrl(asset.fallback);
        
        // Cache fallback result (shorter duration)
        this.cache.set(cacheKey, fallbackDataUrl);
        this.cacheTimestamps.set(cacheKey, Date.now());
        
        log.info(`Successfully loaded ${assetKey} from fallback`, 'ASSETS');
        return fallbackDataUrl;
        
      } catch (fallbackError) {
        log.error(`Failed to load ${assetKey} from both primary and fallback sources`, 'ASSETS', {
          primaryError: primaryError instanceof Error ? primaryError.message : 'Unknown error',
          fallbackError: fallbackError instanceof Error ? fallbackError.message : 'Unknown error'
        });
        
        // Return placeholder image as last resort
        return this.createPlaceholderImage(assetKey);
      }
    }
  }

  /**
   * Load an image as data URL with simplified logic for public assets
   */
  private static async loadImageAsDataUrl(imagePath: string): Promise<string> {
    try {
      // For local images, use direct canvas approach
      if (imagePath.startsWith('/') || imagePath.startsWith('./')) {
        return new Promise((resolve, reject) => {
          const img = new Image();
          
          const timeout = setTimeout(() => {
            reject(new Error(`Image load timeout: ${imagePath}`));
          }, 10000);
          
          img.onload = () => {
            clearTimeout(timeout);
            try {
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');
              
              if (!ctx) {
                reject(new Error('Failed to get canvas context'));
                return;
              }
              
              canvas.width = img.width;
              canvas.height = img.height;
              ctx.drawImage(img, 0, 0);
              
              const dataUrl = canvas.toDataURL('image/jpeg', 0.9);
              resolve(dataUrl);
            } catch (error) {
              reject(error);
            }
          };
          
          img.onerror = () => {
            clearTimeout(timeout);
            reject(new Error(`Failed to load image: ${imagePath}`));
          };
          
          img.src = imagePath;
        });
      }

      // For public Firebase Storage URLs, use direct fetch
      const maxRetries = 2;
      let lastError: Error | null = null;
      
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          const response = await fetch(imagePath, {
            method: 'GET',
            mode: 'cors',
            credentials: 'omit',
            headers: {
              'Accept': 'image/*'
            }
          });

          if (!response.ok) {
            throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
          }

          const blob = await response.blob();
          
          return new Promise((resolve, reject) => {
            const img = new Image();
            const objectUrl = URL.createObjectURL(blob);
            
            const timeout = setTimeout(() => {
              URL.revokeObjectURL(objectUrl);
              reject(new Error(`Image processing timeout: ${imagePath}`));
            }, 10000);
            
            img.onload = () => {
              clearTimeout(timeout);
              try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                if (!ctx) {
                  URL.revokeObjectURL(objectUrl);
                  reject(new Error('Failed to get canvas context'));
                  return;
                }
                
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);
                
                const dataUrl = canvas.toDataURL('image/jpeg', 0.9);
                URL.revokeObjectURL(objectUrl);
                resolve(dataUrl);
              } catch (error) {
                URL.revokeObjectURL(objectUrl);
                reject(error);
              }
            };
            
            img.onerror = () => {
              clearTimeout(timeout);
              URL.revokeObjectURL(objectUrl);
              reject(new Error(`Failed to process image blob: ${imagePath}`));
            };
            
            img.src = objectUrl;
          });
        } catch (error) {
          lastError = error instanceof Error ? error : new Error('Unknown error');
          log.warn(`Asset load attempt ${attempt} failed`, 'ASSETS', {
            imagePath,
            attempt,
            error: lastError.message
          });
          
          // Wait before retry (except on last attempt)
          if (attempt < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
          }
        }
      }
      
      // If all retries failed, throw the last error
      throw lastError || new Error('All retry attempts failed');

    } catch (error) {
      log.error('Error loading image as data URL', 'ASSETS', {
        imagePath,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Check if cached data is still valid
   */
  private static isCacheValid(cacheKey: string): boolean {
    const timestamp = this.cacheTimestamps.get(cacheKey);
    if (!timestamp) return false;
    
    return Date.now() - timestamp < this.CACHE_DURATION;
  }

  /**
   * Create a placeholder image when all loading methods fail
   */
  private static createPlaceholderImage(assetKey: CompanyAssetKey): string {
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');
    
    if (!ctx) {
      // Return a minimal data URL if canvas is not available
      return 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg==';
    }
    
    if (assetKey === 'logo') {
      canvas.width = 200;
      canvas.height = 90;
      ctx.fillStyle = '#f8f9fa';
      ctx.fillRect(0, 0, 200, 90);
      ctx.fillStyle = '#CC0000';
      ctx.font = 'bold 16px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('Auburn Engineering', 100, 35);
      ctx.font = '12px Arial';
      ctx.fillText('Logo Unavailable', 100, 55);
    } else {
      canvas.width = 150;
      canvas.height = 60;
      ctx.fillStyle = '#f8f9fa';
      ctx.fillRect(0, 0, 150, 60);
      ctx.strokeStyle = '#dee2e6';
      ctx.strokeRect(5, 5, 140, 50);
      ctx.fillStyle = '#6c757d';
      ctx.font = '12px Arial';
      ctx.textAlign = 'center';
      ctx.fillText('Signature Unavailable', 75, 35);
    }
    
    return canvas.toDataURL('image/png');
  }

  /**
   * Clear cache (useful for testing or manual refresh)
   */
  static clearCache(): void {
    this.cache.clear();
    this.cacheTimestamps.clear();
    log.info('Company assets cache cleared', 'ASSETS');
  }

  /**
   * Preload all company assets (useful for bulk operations)
   */
  static async preloadAssets(): Promise<{ logo: string; signature: string }> {
    log.info('Preloading company assets', 'ASSETS');
    
    const [logo, signature] = await Promise.all([
      this.loadLogoAsDataUrl(),
      this.loadSignatureAsDataUrl()
    ]);
    
    log.info('Company assets preloaded successfully', 'ASSETS');
    
    return { logo, signature };
  }

  /**
   * Get direct asset URL (useful for HTML img tags or when data URL conversion isn't needed)
   */
  static getAssetUrl(assetKey: CompanyAssetKey): string {
    return COMPANY_ASSETS[assetKey].url;
  }

  /**
   * Load high-quality uncompressed logo as data URL
   */
  static async loadLogoAsHighQualityDataUrl(): Promise<string> {
    return this.loadAssetAsHighQualityDataUrl('logo');
  }

  /**
   * Load high-quality uncompressed signature as data URL
   */
  static async loadSignatureAsHighQualityDataUrl(): Promise<string> {
    return this.loadAssetAsHighQualityDataUrl('signature');
  }

  /**
   * Load asset as high-quality uncompressed data URL (for PDF generation)
   */
  private static async loadAssetAsHighQualityDataUrl(assetKey: CompanyAssetKey): Promise<string> {
    const cacheKey = `${assetKey}_hq`;
    
    // Check cache first
    if (this.cache.has(cacheKey) && this.isCacheValid(cacheKey)) {
      return this.cache.get(cacheKey)!;
    }
    
    const asset = COMPANY_ASSETS[assetKey];
    
    try {
      // Try primary URL first
      const dataUrl = await this.loadImageAsHighQualityDataUrl(asset.url);
      
      // Cache successful result
      this.cache.set(cacheKey, dataUrl);
      this.cacheTimestamps.set(cacheKey, Date.now());
      
      log.info(`Successfully loaded high-quality ${assetKey} from Firebase Storage`, 'ASSETS');
      return dataUrl;
      
    } catch (primaryError) {
      log.warn(`Failed to load high-quality ${assetKey} from Firebase Storage, trying fallback`, 'ASSETS', {
        error: primaryError instanceof Error ? primaryError.message : 'Unknown error',
        fallback: asset.fallback
      });
      
      try {
        // Try fallback local image
        const fallbackDataUrl = await this.loadImageAsHighQualityDataUrl(asset.fallback);
        
        // Cache fallback result (shorter duration)
        this.cache.set(cacheKey, fallbackDataUrl);
        this.cacheTimestamps.set(cacheKey, Date.now());
        
        log.info(`Successfully loaded high-quality ${assetKey} from fallback`, 'ASSETS');
        return fallbackDataUrl;
        
      } catch (fallbackError) {
        log.error(`Failed to load high-quality ${assetKey} from both primary and fallback sources`, 'ASSETS', {
          primaryError: primaryError instanceof Error ? primaryError.message : 'Unknown error',
          fallbackError: fallbackError instanceof Error ? fallbackError.message : 'Unknown error'
        });
        
        // Return placeholder image as last resort
        return this.createPlaceholderImage(assetKey);
      }
    }
  }

  /**
   * Load an image as high-quality uncompressed data URL (preserves original quality)
   */
  private static async loadImageAsHighQualityDataUrl(imagePath: string): Promise<string> {
    try {
      // For local images, use direct canvas approach with PNG
      if (imagePath.startsWith('/') || imagePath.startsWith('./')) {
        return new Promise((resolve, reject) => {
          const img = new Image();
          
          const timeout = setTimeout(() => {
            reject(new Error(`Image load timeout: ${imagePath}`));
          }, 10000);
          
          img.onload = () => {
            clearTimeout(timeout);
            try {
              const canvas = document.createElement('canvas');
              const ctx = canvas.getContext('2d');
              
              if (!ctx) {
                reject(new Error('Failed to get canvas context'));
                return;
              }
              
              canvas.width = img.width;
              canvas.height = img.height;
              ctx.drawImage(img, 0, 0);
              
              // Use PNG for lossless compression or high-quality JPEG
              const dataUrl = imagePath.includes('signature') 
                ? canvas.toDataURL('image/png') // PNG for signatures to preserve transparency
                : canvas.toDataURL('image/jpeg', 1.0); // Max quality JPEG for logos
              resolve(dataUrl);
            } catch (error) {
              reject(error);
            }
          };
          
          img.onerror = () => {
            clearTimeout(timeout);
            reject(new Error(`Failed to load image: ${imagePath}`));
          };
          
          img.src = imagePath;
        });
      }

      // For public Firebase Storage URLs, use direct fetch
      const maxRetries = 2;
      let lastError: Error | null = null;
      
      for (let attempt = 1; attempt <= maxRetries; attempt++) {
        try {
          const response = await fetch(imagePath, {
            method: 'GET',
            mode: 'cors',
            credentials: 'omit',
            headers: {
              'Accept': 'image/*'
            }
          });

          if (!response.ok) {
            throw new Error(`Failed to fetch image: ${response.status} ${response.statusText}`);
          }

          const blob = await response.blob();
          
          return new Promise((resolve, reject) => {
            const img = new Image();
            const objectUrl = URL.createObjectURL(blob);
            
            const timeout = setTimeout(() => {
              URL.revokeObjectURL(objectUrl);
              reject(new Error(`Image processing timeout: ${imagePath}`));
            }, 10000);
            
            img.onload = () => {
              clearTimeout(timeout);
              try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                if (!ctx) {
                  URL.revokeObjectURL(objectUrl);
                  reject(new Error('Failed to get canvas context'));
                  return;
                }
                
                canvas.width = img.width;
                canvas.height = img.height;
                ctx.drawImage(img, 0, 0);
                
                // Use PNG for lossless compression or max quality JPEG
                const dataUrl = imagePath.includes('signature') 
                  ? canvas.toDataURL('image/png') // PNG for signatures to preserve transparency
                  : canvas.toDataURL('image/jpeg', 1.0); // Max quality JPEG for logos
                URL.revokeObjectURL(objectUrl);
                resolve(dataUrl);
              } catch (error) {
                URL.revokeObjectURL(objectUrl);
                reject(error);
              }
            };
            
            img.onerror = () => {
              clearTimeout(timeout);
              URL.revokeObjectURL(objectUrl);
              reject(new Error(`Failed to process image blob: ${imagePath}`));
            };
            
            img.src = objectUrl;
          });
        } catch (error) {
          lastError = error instanceof Error ? error : new Error('Unknown error');
          log.warn(`High-quality asset load attempt ${attempt} failed`, 'ASSETS', {
            imagePath,
            attempt,
            error: lastError.message
          });
          
          // Wait before retry (except on last attempt)
          if (attempt < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 1000 * attempt));
          }
        }
      }
      
      // If all retries failed, throw the last error
      throw lastError || new Error('All retry attempts failed');

    } catch (error) {
      log.error('Error loading high-quality image as data URL', 'ASSETS', {
        imagePath,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
} 
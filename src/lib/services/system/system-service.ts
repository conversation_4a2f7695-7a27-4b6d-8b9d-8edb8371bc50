import { 
  doc, 
  getDoc, 
  setDoc, 
  updateDoc, 
  onSnapshot,
  serverTimestamp,
  Timestamp,
  deleteField 
} from 'firebase/firestore';
import { db } from '@/config/firebase-persistence';
import { AppVersion, VersionInfo, APP_VERSION, VersionUtils } from '@/config/version';

export interface MaintenanceConfig {
  enabled: boolean;
  message: string;
  scheduledStart?: string;
  scheduledEnd?: string;
  allowedRoles: string[];
  bypassUsers: string[];
  lastUpdatedBy: string;
  lastUpdatedAt: string;
}

export interface SystemConfig {
  version: VersionInfo;
  maintenance: MaintenanceConfig;
  lastChecked: string;
  updateAvailable: boolean;
}

export class SystemService {
  private static readonly SYSTEM_CONFIG_DOC = 'system_config';
  private static readonly APP_CONFIG_COLLECTION = 'app_config';
  
  private static maintenanceListeners: Map<string, () => void> = new Map();
  private static versionListeners: Map<string, () => void> = new Map();

  /**
   * Initialize system configuration
   */
  static async initializeSystemConfig(): Promise<void> {
    try {
      const configRef = doc(db, this.APP_CONFIG_COLLECTION, this.SYSTEM_CONFIG_DOC);
      const configSnap = await getDoc(configRef);

      if (!configSnap.exists()) {
        // Create initial system configuration
        const initialConfig: SystemConfig = {
          version: {
            current: APP_VERSION,
            displayName: VersionUtils.formatFullVersion(APP_VERSION),
            releaseDate: new Date().toISOString(),
            releaseNotes: [
              'Initial system setup',
              'Version tracking enabled',
              'Maintenance mode functionality added'
            ]
          },
          maintenance: {
            enabled: false,
            message: 'The system is currently under maintenance. Please check back later.',
            allowedRoles: ['admin'],
            bypassUsers: [],
            lastUpdatedBy: 'system',
            lastUpdatedAt: new Date().toISOString()
          },
          lastChecked: new Date().toISOString(),
          updateAvailable: false
        };

        await setDoc(configRef, {
          ...initialConfig,
          createdAt: serverTimestamp(),
          updatedAt: serverTimestamp()
        });

        console.log('System configuration initialized');
      }
    } catch (error) {
      console.error('Failed to initialize system configuration:', error);
      throw error;
    }
  }

  /**
   * Get current system configuration
   */
  static async getSystemConfig(): Promise<SystemConfig | null> {
    try {
      const configRef = doc(db, this.APP_CONFIG_COLLECTION, this.SYSTEM_CONFIG_DOC);
      const configSnap = await getDoc(configRef);

      if (configSnap.exists()) {
        const data = configSnap.data();
        return {
          version: data.version,
          maintenance: data.maintenance,
          lastChecked: data.lastChecked,
          updateAvailable: data.updateAvailable || false
        };
      }

      return null;
    } catch (error) {
      console.error('Failed to get system configuration:', error);
      return null;
    }
  }

  /**
   * Update version information
   */
  static async updateVersion(versionInfo: VersionInfo, updatedBy: string): Promise<void> {
    try {
      const configRef = doc(db, this.APP_CONFIG_COLLECTION, this.SYSTEM_CONFIG_DOC);
      
      await updateDoc(configRef, {
        version: versionInfo,
        lastChecked: new Date().toISOString(),
        updatedAt: serverTimestamp(),
        lastUpdatedBy: updatedBy
      });

      console.log('Version information updated:', versionInfo.displayName);
    } catch (error) {
      console.error('Failed to update version information:', error);
      throw error;
    }
  }

  /**
   * Check for version updates
   */
  static async checkForUpdates(): Promise<boolean> {
    try {
      const config = await this.getSystemConfig();
      if (!config) return false;

      const currentVersion = APP_VERSION;
      const storedVersion = config.version.current;

      const hasUpdate = VersionUtils.isNewerVersion(currentVersion, storedVersion);
      
      if (hasUpdate) {
        const configRef = doc(db, this.APP_CONFIG_COLLECTION, this.SYSTEM_CONFIG_DOC);
        await updateDoc(configRef, {
          updateAvailable: true,
          lastChecked: new Date().toISOString(),
          updatedAt: serverTimestamp()
        });
      }

      return hasUpdate;
    } catch (error) {
      console.error('Failed to check for updates:', error);
      return false;
    }
  }

  /**
   * Get maintenance mode status
   */
  static async getMaintenanceStatus(): Promise<MaintenanceConfig | null> {
    try {
      const config = await this.getSystemConfig();
      return config?.maintenance || null;
    } catch (error) {
      console.error('Failed to get maintenance status:', error);
      return null;
    }
  }

  /**
   * Update maintenance mode
   */
  static async updateMaintenanceMode(
    maintenanceConfig: Partial<MaintenanceConfig>,
    updatedBy: string
  ): Promise<void> {
    try {
      const configRef = doc(db, this.APP_CONFIG_COLLECTION, this.SYSTEM_CONFIG_DOC);
      const currentConfig = await this.getSystemConfig();
      
      if (!currentConfig) {
        throw new Error('System configuration not found');
      }

      // Build the updated maintenance configuration
      const updatedMaintenance: any = {
        ...currentConfig.maintenance,
        lastUpdatedBy: updatedBy,
        lastUpdatedAt: new Date().toISOString()
      };

      // Build the update data with top-level fields for Firebase
      const updateData: any = {
        updatedAt: serverTimestamp()
      };

      // Process each field in the update, handling special cases
      Object.keys(maintenanceConfig).forEach(key => {
        const value = maintenanceConfig[key as keyof MaintenanceConfig];
        
        if (value !== undefined) {
          // Special handling for scheduled times - if they are empty strings, remove them
          if ((key === 'scheduledStart' || key === 'scheduledEnd') && 
              (value === '' || (typeof value === 'string' && value.trim() === ''))) {
            // Use top-level field path for deleteField()
            updateData[`maintenance.${key}`] = deleteField();
          } else {
            updatedMaintenance[key] = value;
          }
        }
      });

      // Set the updated maintenance object
      updateData.maintenance = updatedMaintenance;

      await updateDoc(configRef, updateData);

      console.log('Maintenance mode updated:', updatedMaintenance.enabled ? 'enabled' : 'disabled');
    } catch (error) {
      console.error('Failed to update maintenance mode:', error);
      throw error;
    }
  }

  /**
   * Check if user can bypass maintenance mode
   */
  static async canBypassMaintenance(userId: string, userRole: string): Promise<boolean> {
    try {
      const maintenance = await this.getMaintenanceStatus();
      if (!maintenance || !maintenance.enabled) return true;

      // Check if user role is allowed
      if (maintenance.allowedRoles.includes(userRole)) return true;

      // Check if user is specifically allowed
      if (maintenance.bypassUsers.includes(userId)) return true;

      return false;
    } catch (error) {
      console.error('Failed to check maintenance bypass:', error);
      return false;
    }
  }

  /**
   * Subscribe to maintenance mode changes
   */
  static subscribeToMaintenanceMode(
    callback: (maintenance: MaintenanceConfig | null) => void
  ): () => void {
    const configRef = doc(db, this.APP_CONFIG_COLLECTION, this.SYSTEM_CONFIG_DOC);
    
    const unsubscribe = onSnapshot(configRef, (doc) => {
      if (doc.exists()) {
        const data = doc.data();
        callback(data.maintenance || null);
      } else {
        callback(null);
      }
    }, (error) => {
      console.error('Error listening to maintenance mode changes:', error);
      callback(null);
    });

    return unsubscribe;
  }

  /**
   * Subscribe to version changes
   */
  static subscribeToVersionUpdates(
    callback: (versionInfo: VersionInfo | null, updateAvailable: boolean) => void
  ): () => void {
    const configRef = doc(db, this.APP_CONFIG_COLLECTION, this.SYSTEM_CONFIG_DOC);
    
    const unsubscribe = onSnapshot(configRef, (doc) => {
      if (doc.exists()) {
        const data = doc.data();
        callback(data.version || null, data.updateAvailable || false);
      } else {
        callback(null, false);
      }
    }, (error) => {
      console.error('Error listening to version changes:', error);
      callback(null, false);
    });

    return unsubscribe;
  }

  /**
   * Get system health status
   */
  static async getSystemHealth(): Promise<{
    status: 'healthy' | 'maintenance' | 'error';
    version: string;
    uptime: string;
    lastUpdate: string;
  }> {
    try {
      const config = await this.getSystemConfig();
      const maintenance = config?.maintenance;

      return {
        status: maintenance?.enabled ? 'maintenance' : 'healthy',
        version: VersionUtils.formatFullVersion(APP_VERSION),
        uptime: VersionUtils.formatBuildInfo(APP_VERSION),
        lastUpdate: config?.lastChecked || new Date().toISOString()
      };
    } catch (error) {
      console.error('Failed to get system health:', error);
      return {
        status: 'error',
        version: VersionUtils.formatFullVersion(APP_VERSION),
        uptime: 'Unknown',
        lastUpdate: 'Unknown'
      };
    }
  }
} 
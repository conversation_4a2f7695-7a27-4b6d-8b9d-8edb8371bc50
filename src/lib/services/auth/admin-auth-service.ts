'use client';

import { 
  createUserWithEmailAndPassword,
  updateProfile as firebaseUpdateProfile,
} from 'firebase/auth';
import { auth } from '@/config/firebase-persistence';
import { AuthUser, convertFirebaseUser } from '@/types/auth';
import { UserRole } from '@/types/user';
import { UserService } from './user-service';

/**
 * Admin-only authentication service for user management
 * This service should only be used within admin contexts
 */
export class AdminAuthService {
  /**
   * Create a new user account (Admin only)
   * This method should only be called from admin dashboard
   */
  static async createUser(
    email: string, 
    password: string, 
    displayName: string,
    role: UserRole = UserRole.USER
  ): Promise<AuthUser> {
    console.log('AdminAuthService.createUser: Starting user creation process', { 
      email, 
      hasDisplayName: !!displayName,
      role 
    });
    
    try {
      // Create the user with Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      console.log('AdminAuthService.createUser: User created', { 
        uid: userCredential.user.uid,
        initialDisplayName: userCredential.user.displayName 
      });
      
      // Update the display name if provided
      if (displayName) {
        console.log('AdminAuthService.createUser: Updating profile with displayName:', displayName);
        await firebaseUpdateProfile(userCredential.user, { displayName });
        console.log('AdminAuthService.createUser: Profile update completed');
        
        // Force reload the user to get the updated profile
        await userCredential.user.reload();
        console.log('AdminAuthService.createUser: User reloaded, displayName:', userCredential.user.displayName);
      }
      
      const convertedUser = convertFirebaseUser(userCredential.user);
      console.log('AdminAuthService.createUser: Converted user', { 
        uid: convertedUser.uid,
        displayName: convertedUser.displayName 
      });
      
      // Update user role if not default USER role
      if (role !== UserRole.USER) {
        console.log('AdminAuthService.createUser: Updating user role to:', role);
        await UserService.updateUserRole(convertedUser.uid, role);
      }
      
      console.log('AdminAuthService.createUser: User creation completed successfully');
      return convertedUser;
      
    } catch (error) {
      console.error('AdminAuthService.createUser: Error creating user:', error);
      throw error;
    }
  }

  /**
   * Validate that the current user has admin permissions
   * This should be called before any admin operations
   */
  static validateAdminPermissions(userRole: UserRole): boolean {
    return userRole === UserRole.ADMIN;
  }
} 
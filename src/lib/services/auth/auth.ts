import {
  signInWithEmailAndPassword,
  createUserWithEmailAndPassword,
  signOut as firebaseSignOut,
  sendPasswordResetEmail,
  updateProfile as firebaseUpdateProfile,
  GoogleAuthProvider,
  signInWithPopup,
  onAuthStateChanged,
  User
} from 'firebase/auth';
import { auth } from '@/config/firebase-persistence';
import { AuthUser, convertFirebaseUser } from '@/types/auth';

export class AuthService {
  static async signIn(email: string, password: string): Promise<AuthUser> {
    const userCredential = await signInWithEmailAndPassword(auth, email, password);
    return convertFirebaseUser(userCredential.user);
  }

  static async signUp(email: string, password: string, displayName?: string): Promise<AuthUser> {
    console.log('AuthService.signUp: Starting signup process', { email, hasDisplayName: !!displayName });
    
    const userCredential = await createUserWithEmailAndPassword(auth, email, password);
    console.log('AuthService.signUp: User created', { 
      uid: userCredential.user.uid,
      initialDisplayName: userCredential.user.displayName 
    });
    
    if (displayName) {
      console.log('AuthService.signUp: Updating profile with displayName:', displayName);
      await firebaseUpdateProfile(userCredential.user, { displayName });
      console.log('AuthService.signUp: Profile update completed');
      
      // Force reload the user to get the updated profile
      await userCredential.user.reload();
      console.log('AuthService.signUp: User reloaded, displayName:', userCredential.user.displayName);
    }
    
    const convertedUser = convertFirebaseUser(userCredential.user);
    console.log('AuthService.signUp: Converted user', { 
      uid: convertedUser.uid,
      displayName: convertedUser.displayName 
    });
    
    return convertedUser;
  }

  static async signInWithGoogle(): Promise<AuthUser> {
    const provider = new GoogleAuthProvider();
    const userCredential = await signInWithPopup(auth, provider);
    return convertFirebaseUser(userCredential.user);
  }

  static async signOut(): Promise<void> {
    await firebaseSignOut(auth);
  }

  static async resetPassword(email: string): Promise<void> {
    await sendPasswordResetEmail(auth, email);
  }

  static async updateProfile(displayName: string, photoURL?: string): Promise<void> {
    if (!auth.currentUser) {
      throw new Error('No user is currently signed in');
    }
    
    await firebaseUpdateProfile(auth.currentUser, {
      displayName,
      ...(photoURL && { photoURL })
    });
  }

  static onAuthStateChanged(callback: (user: AuthUser | null) => void): () => void {
    console.log('AuthService: onAuthStateChanged called, setting up listener');
    
    try {
      console.log('AuthService: Firebase auth object:', !!auth);
      
      const unsubscribe = onAuthStateChanged(auth, (user: User | null) => {
        console.log('AuthService: Firebase onAuthStateChanged event fired', { 
          hasUser: !!user,
          uid: user?.uid,
          email: user?.email,
          emailVerified: user?.emailVerified,
          timestamp: new Date().toISOString()
        });
        
        const convertedUser = user ? convertFirebaseUser(user) : null;
        console.log('AuthService: Converted user:', convertedUser);
        
        console.log('AuthService: Calling callback function');
        callback(convertedUser);
      });
      
      console.log('AuthService: Auth state listener registered successfully');
      return unsubscribe;
    } catch (error) {
      console.error('AuthService: Error setting up auth state listener:', error);
      // Call callback with null user if Firebase fails to initialize
      callback(null);
      // Return a no-op unsubscribe function
      return () => {};
    }
  }

  static getCurrentUser(): AuthUser | null {
    return auth.currentUser ? convertFirebaseUser(auth.currentUser) : null;
  }
} 
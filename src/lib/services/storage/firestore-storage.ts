import { ChecklistData, SyncMetadata, ChecklistSummary } from '@/types/checklist';
import { log } from '../../utils/logger';
import { 
  collection, 
  doc, 
  setDoc, 
  getDoc,
  getDocs, 
  deleteDoc,
  updateDoc,
  query, 
  where,
  orderBy,
  onSnapshot,
  writeBatch,
  serverTimestamp,
  Timestamp,
  enableNetwork,
  disableNetwork,
  clearIndexedDbPersistence
} from 'firebase/firestore';
import { db } from '@/config/firebase-persistence';

export interface StorageStatus {
  available: boolean;
  used: number;
  quota: number;
  type: 'firestore' | 'localStorage';
  isOnline: boolean;
  lastSync?: string;
}

// Define the SyncStatus interface for Firestore (simplified since sync is automatic)
export interface SyncStatusInfo {
  isOnline: boolean;
  lastSync: string | null;
  inProgress: boolean;
  hasChanges: boolean;
  queueSize: number;
}

export interface StorageSettings {
  cacheSize: number;
  compressionEnabled: boolean;
  showStorageWarnings?: boolean;
  daysToKeepCompleted?: number;
  autoSync?: boolean;
  userId: string;
}

/**
 * Firestore-based storage service for checklist metadata
 * Uses Firestore persistence for automatic real-time sync and offline support
 * Works in conjunction with Firebase Storage for images
 */
export class FirestoreStorageService {
  private static readonly CHECKLISTS_COLLECTION = 'checklists';
  private static readonly SETTINGS_COLLECTION = 'settings';
  private static readonly DEFAULT_SETTINGS: Partial<StorageSettings> = {
    cacheSize: 100 * 1024 * 1024, // 100MB cache (images now in Firebase Storage)
    compressionEnabled: true,
    showStorageWarnings: true,
    daysToKeepCompleted: 30,
    autoSync: true
  };

  private static listeners: Map<string, () => void> = new Map();
  private static isOnline = typeof window !== 'undefined' ? navigator.onLine : true;
  private static lastSyncTimestamp: string | null = null;
  private static initializedUsers: Set<string> = new Set();
  private static initializationPromises: Map<string, Promise<void>> = new Map();

  /**
   * Initialize the storage service
   */
  static async initialize(userId: string): Promise<void> {
    // Early return if already initialized for this user
    if (this.initializedUsers.has(userId)) {
      log.debug('Firestore storage service already initialized for user', 'STORAGE', { userId });
      return;
    }

    // If there's an ongoing initialization for this user, wait for it
    const existingPromise = this.initializationPromises.get(userId);
    if (existingPromise) {
      await existingPromise;
      return;
    }

    // Create and store initialization promise
    const initPromise = this.performInitialization(userId);
    this.initializationPromises.set(userId, initPromise);

    try {
      await initPromise;
    } finally {
      this.initializationPromises.delete(userId);
    }
  }

  /**
   * Internal initialization method
   */
  private static async performInitialization(userId: string): Promise<void> {
    log.info('Initializing Firestore storage service', 'STORAGE', { userId });

    try {
      // Initialize Firebase Storage proactively to prevent timing issues
      try {
        const { getFirebaseStorage, validateFirebaseStorageConfig } = await import('@/config/firebase-config');
        
        // First validate that Storage is properly configured
        if (!validateFirebaseStorageConfig()) {
          log.warn('Firebase Storage configuration incomplete - image features may not work', 'STORAGE', { userId });
        } else {
          // Proceed with initialization
          getFirebaseStorage(); // Initialize Firebase Storage early
          log.debug('Firebase Storage initialized proactively', 'STORAGE', { userId });
        }
      } catch (storageError) {
        log.warn('Failed to initialize Firebase Storage proactively', 'STORAGE', {
          userId,
          error: storageError instanceof Error ? storageError.message : 'Unknown error'
        });
        // Don't throw - app can continue without Firebase Storage for image features
      }

      // Initialize settings if they don't exist
      await this.initializeSettings(userId);

      // Set up network status monitoring only once
      if (this.initializedUsers.size === 0) {
        this.setupNetworkMonitoring();
      }

      // Mark user as initialized
      this.initializedUsers.add(userId);

      log.info('Firestore storage service initialized', 'STORAGE', {
        userId,
        isOnline: this.isOnline
      });
    } catch (error) {
      log.error('Failed to initialize Firestore storage service', 'STORAGE', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Reset initialization state for a specific user
   */
  static resetUserInitialization(userId: string): void {
    this.initializedUsers.delete(userId);
    this.initializationPromises.delete(userId);
  }

  /**
   * Reset all initialization state
   */
  static resetAllInitialization(): void {
    this.initializedUsers.clear();
    this.initializationPromises.clear();
  }

  /**
   * Sanitize data for Firestore by converting undefined values to null
   * and handling other Firestore compatibility issues
   */
  private static sanitizeForFirestore(obj: any): any {
    if (obj === null || obj === undefined) {
      return null;
    }
    
    // Handle functions (should not be saved to Firestore)
    if (typeof obj === 'function') {
      return null;
    }
    
    // Handle symbols (not supported by Firestore)
    if (typeof obj === 'symbol') {
      return null;
    }
    
    // Handle arrays
    if (Array.isArray(obj)) {
      return obj.map(item => this.sanitizeForFirestore(item));
    }
    
    // Handle objects
    if (typeof obj === 'object') {
      const sanitized: any = {};
      for (const [key, value] of Object.entries(obj)) {
        // Skip keys that might be problematic
        if (key.startsWith('__') || key.startsWith('$')) {
          continue;
        }
        sanitized[key] = this.sanitizeForFirestore(value);
      }
      return sanitized;
    }
    
    // Handle numbers that might be NaN or Infinity
    if (typeof obj === 'number') {
      if (isNaN(obj) || !isFinite(obj)) {
        return null;
      }
    }
    
    return obj;
  }

  /**
   * Save a checklist to Firestore
   */
  static async saveChecklist(checklist: ChecklistData, userId: string): Promise<void> {
    try {
      const docData = {
        ...checklist,
        userId,
        updatedAt: new Date().toISOString(),
        createdAt: checklist.createdAt || new Date().toISOString()
      };

      // Sanitize data to convert undefined values to null
      const sanitizedData = this.sanitizeForFirestore(docData);

      // Debug logging for production debugging
      log.debug('Saving checklist data to Firestore', 'STORAGE', {
        checklistId: checklist.id,
        userId,
        dataSize: JSON.stringify(sanitizedData).length,
        hasUndefinedValues: JSON.stringify(docData).includes('undefined'),
        mechanicalChecks: Object.keys(sanitizedData.mechanicalChecks || {}),
        electricalChecks: Object.keys(sanitizedData.electricalChecks || {}),
        sequenceChecks: Object.keys(sanitizedData.sequenceControlsChecks || {})
      });

      const docRef = doc(db, this.CHECKLISTS_COLLECTION, checklist.id);
      await setDoc(docRef, sanitizedData);

      this.lastSyncTimestamp = new Date().toISOString();
      
      log.debug('Checklist saved to Firestore', 'STORAGE', {
        checklistId: checklist.id,
        userId,
        isOnline: this.isOnline
      });

      // Phase 3: Queue background PDF generation
      try {
        const { BackgroundPDFGenerator } = await import('../pdf/background-pdf-generator');
        
        // Queue PDF generation with normal priority (non-blocking)
        BackgroundPDFGenerator.queueGeneration(checklist, 'normal')
          .catch(queueError => {
            log.warn('Failed to queue PDF generation (non-critical)', 'STORAGE', {
              checklistId: checklist.id,
              userId,
              error: queueError instanceof Error ? queueError.message : 'Unknown error'
            });
          });
          
        log.debug('PDF generation queued for background processing', 'STORAGE', {
          checklistId: checklist.id,
          userId
        });
        
      } catch (importError) {
        // PDF generation is optional, don't fail the save operation
        log.debug('PDF generation service not available', 'STORAGE', {
          checklistId: checklist.id,
          userId,
          error: importError instanceof Error ? importError.message : 'Unknown error'
        });
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      
      // Enhanced error logging for debugging
      log.error('Failed to save checklist to Firestore', 'STORAGE', {
        checklistId: checklist.id,
        userId,
        error: errorMessage,
        errorCode: (error as any)?.code,
        errorDetails: (error as any)?.details,
        checklistSize: JSON.stringify(checklist).length,
        hasUndefinedInOriginal: JSON.stringify(checklist).includes('undefined')
      });
      
      // Provide more specific error messages
      if (errorMessage.includes('invalid data')) {
        throw new Error(`Invalid data format detected in checklist. Please check all form fields are properly filled. Original error: ${errorMessage}`);
      } else if (errorMessage.includes('permission')) {
        throw new Error(`Permission denied. Please ensure you are signed in and have access. Original error: ${errorMessage}`);
      } else if (errorMessage.includes('network')) {
        throw new Error(`Network error occurred while saving. Please check your connection and try again. Original error: ${errorMessage}`);
      } else {
        throw new Error(`Failed to save checklist: ${errorMessage}`);
      }
    }
  }

  /**
   * Get a specific checklist by ID
   */
  static async getChecklist(checklistId: string, userId: string): Promise<ChecklistData | null> {
    try {
      const docRef = doc(db, this.CHECKLISTS_COLLECTION, checklistId);
      const docSnapshot = await getDoc(docRef);

      if (!docSnapshot.exists()) {
        return null;
      }

      const data = docSnapshot.data();
      
      // Verify user ownership
      if (data.userId !== userId) {
        log.warn('Attempted to access checklist from different user', 'STORAGE', {
          checklistId,
          requestedBy: userId,
          actualOwner: data.userId
        });
        return null;
      }

      return {
        ...data,
        id: docSnapshot.id,
        createdAt: data.createdAt instanceof Timestamp ? data.createdAt.toDate().toISOString() : data.createdAt,
        updatedAt: data.updatedAt instanceof Timestamp ? data.updatedAt.toDate().toISOString() : data.updatedAt,
        completedAt: data.completedAt instanceof Timestamp ? data.completedAt.toDate().toISOString() : data.completedAt
      } as ChecklistData;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to get checklist from Firestore', 'STORAGE', {
        checklistId,
        userId,
        error: errorMessage
      });
      throw new Error(`Failed to get checklist: ${errorMessage}`);
    }
  }

  /**
   * Get all checklists for a user
   */
  static async getAllChecklists(userId: string): Promise<ChecklistData[]> {
    try {
      const q = query(
        collection(db, this.CHECKLISTS_COLLECTION),
        where('userId', '==', userId),
        orderBy('updatedAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const checklists: ChecklistData[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        checklists.push({
          ...data,
          id: doc.id,
          createdAt: data.createdAt instanceof Timestamp ? data.createdAt.toDate().toISOString() : data.createdAt,
          updatedAt: data.updatedAt instanceof Timestamp ? data.updatedAt.toDate().toISOString() : data.updatedAt,
          completedAt: data.completedAt instanceof Timestamp ? data.completedAt.toDate().toISOString() : data.completedAt
        } as ChecklistData);
      });

      this.lastSyncTimestamp = new Date().toISOString();

      log.debug('Retrieved all checklists from Firestore', 'STORAGE', {
        userId,
        count: checklists.length,
        isOnline: this.isOnline
      });

      return checklists;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to get all checklists from Firestore', 'STORAGE', {
        userId,
        error: errorMessage
      });
      throw new Error(`Failed to get checklists: ${errorMessage}`);
    }
  }

  /**
   * Delete a checklist and associated images
   */
  static async deleteChecklist(checklistId: string, userId: string): Promise<void> {
    try {
      // Verify ownership before deletion
      const checklist = await this.getChecklist(checklistId, userId);
      if (!checklist) {
        throw new Error('Checklist not found or access denied');
      }

      // Delete the Firestore document
      const docRef = doc(db, this.CHECKLISTS_COLLECTION, checklistId);
      await deleteDoc(docRef);

      // Also cleanup associated images and PDFs in Firebase Storage
      // Note: This is handled asynchronously to avoid blocking checklist deletion
      try {
        const { FirebaseStorageService } = await import('./firebase-storage-service');
        await FirebaseStorageService.cleanupChecklistImages(userId, checklistId);
      } catch (imageError) {
        log.warn('Failed to cleanup associated images', 'STORAGE', {
          checklistId,
          userId,
          error: imageError instanceof Error ? imageError.message : 'Unknown error'
        });
        // Don't throw error for image cleanup failures
      }

      // Clean up cached PDFs for this checklist
      try {
        const { PDFStorageService } = await import('./pdf-storage-service');
        await PDFStorageService.deleteCachedPDF(userId, checklistId);
        log.info('Successfully cleaned up cached PDFs', 'STORAGE', {
          checklistId,
          userId
        });
      } catch (pdfError) {
        log.warn('Failed to cleanup cached PDFs', 'STORAGE', {
          checklistId,
          userId,
          error: pdfError instanceof Error ? pdfError.message : 'Unknown error'
        });
        // Don't throw error for PDF cleanup failures
      }

      log.info('Checklist deleted from Firestore', 'STORAGE', {
        checklistId,
        userId
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to delete checklist from Firestore', 'STORAGE', {
        checklistId,
        userId,
        error: errorMessage
      });
      throw new Error(`Failed to delete checklist: ${errorMessage}`);
    }
  }

  /**
   * Update checklist completion status
   */
  static async updateChecklistStatus(
    checklistId: string, 
    userId: string, 
    isCompleted: boolean,
    completedAt?: string
  ): Promise<void> {
    try {
      const docRef = doc(db, this.CHECKLISTS_COLLECTION, checklistId);
      const updateData: any = {
        isCompleted,
        updatedAt: new Date().toISOString(),
        lastModified: new Date().toISOString()
      };

      if (isCompleted && completedAt) {
        updateData.completedAt = completedAt;
      } else if (!isCompleted) {
        updateData.completedAt = null;
      }

      await updateDoc(docRef, updateData);

      log.debug('Checklist status updated in Firestore', 'STORAGE', {
        checklistId,
        userId,
        isCompleted
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to update checklist status in Firestore', 'STORAGE', {
        checklistId,
        userId,
        error: errorMessage
      });
      throw new Error(`Failed to update checklist status: ${errorMessage}`);
    }
  }

  /**
   * Bulk save multiple checklists
   */
  static async bulkSaveChecklists(checklists: ChecklistData[], userId: string): Promise<void> {
    try {
      const batch = writeBatch(db);
      const timestamp = new Date().toISOString();

      checklists.forEach((checklist) => {
        const docRef = doc(db, this.CHECKLISTS_COLLECTION, checklist.id);
        const docData = {
          ...checklist,
          userId,
          updatedAt: timestamp,
          createdAt: checklist.createdAt || timestamp
        };

        // Sanitize data to convert undefined values to null
        const sanitizedData = this.sanitizeForFirestore(docData);
        batch.set(docRef, sanitizedData);
      });

      await batch.commit();

      this.lastSyncTimestamp = timestamp;

      log.info('Bulk save completed in Firestore', 'STORAGE', {
        userId,
        count: checklists.length
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to bulk save checklists to Firestore', 'STORAGE', {
        userId,
        count: checklists.length,
        error: errorMessage
      });
      throw new Error(`Failed to bulk save checklists: ${errorMessage}`);
    }
  }

  /**
   * Subscribe to real-time updates for user's checklists
   */
  static subscribeToChecklists(
    userId: string, 
    callback: (checklists: ChecklistData[]) => void
  ): () => void {
    const q = query(
      collection(db, this.CHECKLISTS_COLLECTION),
      where('userId', '==', userId),
      orderBy('updatedAt', 'desc')
    );

    const unsubscribe = onSnapshot(q, (querySnapshot) => {
      const checklists: ChecklistData[] = [];
      
      querySnapshot.forEach((doc) => {
        const data = doc.data();
        checklists.push({
          ...data,
          id: doc.id,
          createdAt: data.createdAt instanceof Timestamp ? data.createdAt.toDate().toISOString() : data.createdAt,
          updatedAt: data.updatedAt instanceof Timestamp ? data.updatedAt.toDate().toISOString() : data.updatedAt,
          completedAt: data.completedAt instanceof Timestamp ? data.completedAt.toDate().toISOString() : data.completedAt
        } as ChecklistData);
      });

      this.lastSyncTimestamp = new Date().toISOString();

      callback(checklists);

      log.debug('Real-time update received', 'STORAGE', {
        userId,
        count: checklists.length
      });
    }, (error) => {
      log.error('Real-time subscription error', 'STORAGE', {
        userId,
        error: error.message
      });
    });

    // Store unsubscribe function
    this.listeners.set(userId, unsubscribe);

    return unsubscribe;
  }

  /**
   * Unsubscribe from real-time updates
   */
  static unsubscribeFromChecklists(userId: string): void {
    const unsubscribe = this.listeners.get(userId);
    if (unsubscribe) {
      unsubscribe();
      this.listeners.delete(userId);
      log.debug('Unsubscribed from real-time updates', 'STORAGE', { userId });
    }
  }

  /**
   * Get storage status and statistics
   */
  static async getStorageStatus(userId: string): Promise<StorageStatus> {
    try {
      const checklists = await this.getAllChecklists(userId);
      const dataSize = JSON.stringify(checklists).length;

      return {
        available: true,
        used: dataSize,
        quota: 100 * 1024 * 1024, // 100MB Firestore cache (images in Firebase Storage)
        type: 'firestore',
        isOnline: this.isOnline,
        lastSync: this.lastSyncTimestamp || undefined
      };

    } catch (error) {
      log.error('Failed to get storage status', 'STORAGE', { userId, error });
      return {
        available: false,
        used: 0,
        quota: 0,
        type: 'firestore',
        isOnline: this.isOnline
      };
    }
  }

  /**
   * Get storage settings
   */
  static async getSettings(userId: string): Promise<StorageSettings> {
    try {
      const docRef = doc(db, this.SETTINGS_COLLECTION, userId);
      const docSnapshot = await getDoc(docRef);

      if (docSnapshot.exists()) {
        return {
          ...this.DEFAULT_SETTINGS,
          ...docSnapshot.data(),
          userId
        } as StorageSettings;
      }

      // Return default settings if none exist
      return {
        ...this.DEFAULT_SETTINGS,
        userId
      } as StorageSettings;

    } catch (error) {
      log.error('Failed to get storage settings', 'STORAGE', { userId, error });
      return {
        ...this.DEFAULT_SETTINGS,
        userId
      } as StorageSettings;
    }
  }

  /**
   * Update storage settings
   */
  static async updateSettings(userId: string, settings: Partial<StorageSettings>): Promise<void> {
    try {
      const docRef = doc(db, this.SETTINGS_COLLECTION, userId);
      await setDoc(docRef, {
        ...settings,
        userId,
        updatedAt: new Date().toISOString()
      }, { merge: true });

      log.info('Storage settings updated', 'STORAGE', { userId, settings });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to update storage settings', 'STORAGE', {
        userId,
        error: errorMessage
      });
      throw new Error(`Failed to update settings: ${errorMessage}`);
    }
  }

  /**
   * Clear all user data (for logout/reset)
   */
  static async clearUserData(userId: string): Promise<void> {
    try {
      // Unsubscribe from real-time updates
      this.unsubscribeFromChecklists(userId);

      // Note: We don't delete data from Firestore on logout
      // This preserves data across sessions and devices
      
      log.info('User data cleared from memory', 'STORAGE', { userId });

    } catch (error) {
      log.error('Failed to clear user data', 'STORAGE', { userId, error });
      throw error;
    }
  }

  /**
   * Delete ALL checklists for a user (permanent deletion)
   */
  static async deleteAllUserChecklists(userId: string): Promise<void> {
    try {
      // Get all checklists for the user
      const q = query(
        collection(db, this.CHECKLISTS_COLLECTION),
        where('userId', '==', userId)
      );

      const querySnapshot = await getDocs(q);
      const batch = writeBatch(db);

      // Add all deletions to batch
      querySnapshot.forEach((doc) => {
        batch.delete(doc.ref);
      });

      // Execute batch deletion
      if (querySnapshot.size > 0) {
        await batch.commit();
        log.info('All user checklists deleted', 'STORAGE', { 
          userId, 
          deletedCount: querySnapshot.size 
        });
      } else {
        log.info('No checklists found to delete', 'STORAGE', { userId });
      }

    } catch (error) {
      log.error('Failed to delete all user checklists', 'STORAGE', { userId, error });
      throw error;
    }
  }

  /**
   * Check network connectivity and sync status
   */
  static getNetworkStatus(): { isOnline: boolean; lastSync?: string } {
    return {
      isOnline: this.isOnline,
      lastSync: this.lastSyncTimestamp || undefined
    };
  }

  /**
   * Manually enable/disable network for testing
   */
  static async setNetworkEnabled(enabled: boolean): Promise<void> {
    try {
      if (enabled) {
        await enableNetwork(db);
        log.info('Firestore network enabled', 'STORAGE', {});
      } else {
        await disableNetwork(db);
        log.info('Firestore network disabled', 'STORAGE', {});
      }
    } catch (error) {
      log.error('Failed to change network status', 'STORAGE', { enabled, error });
      throw error;
    }
  }

  /**
   * Initialize default settings for a user
   */
  private static async initializeSettings(userId: string): Promise<void> {
    try {
      const docRef = doc(db, this.SETTINGS_COLLECTION, userId);
      const docSnapshot = await getDoc(docRef);

      if (!docSnapshot.exists()) {
        await setDoc(docRef, {
          ...this.DEFAULT_SETTINGS,
          userId,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        });

        log.info('Default settings initialized', 'STORAGE', { userId });
      } else {
        // Migrate existing settings to remove redundant sync properties
        await this.migrateSettings(userId, docRef);
      }

    } catch (error) {
      log.error('Failed to initialize settings', 'STORAGE', { userId, error });
      // Don't throw error - app can continue without settings
    }
  }

  /**
   * Migrate old settings to remove redundant sync properties
   */
  private static async migrateSettings(userId: string, docRef: any): Promise<void> {
    try {
      const docSnapshot = await getDoc(docRef);
      if (!docSnapshot.exists()) return;

      const existingData = docSnapshot.data() as any;
      
      // Check if migration is needed (if old sync properties exist)
      if (existingData && ('autoSync' in existingData || 'daysToKeepSynced' in existingData)) {
        const { autoSync, daysToKeepSynced, ...cleanSettings } = existingData;
        
        await setDoc(docRef, {
          ...cleanSettings,
          updatedAt: new Date().toISOString()
        });

        log.info('Settings migrated to remove redundant sync properties', 'STORAGE', { 
          userId,
          removedProperties: ['autoSync', 'daysToKeepSynced']
        });
      }
    } catch (error) {
      log.error('Failed to migrate settings', 'STORAGE', { userId, error });
      // Don't throw error - app can continue with old settings
    }
  }

  /**
   * Set up network status monitoring
   */
  private static setupNetworkMonitoring(): void {
    // Only set up network monitoring in browser environment
    if (typeof window === 'undefined') {
      return;
    }

    const updateNetworkStatus = () => {
      const wasOnline = this.isOnline;
      this.isOnline = navigator.onLine;

      if (wasOnline !== this.isOnline) {
        log.info('Network status changed', 'STORAGE', {
          isOnline: this.isOnline,
          wasOnline
        });
      }
    };

    window.addEventListener('online', updateNetworkStatus);
    window.addEventListener('offline', updateNetworkStatus);

    // Initial status
    updateNetworkStatus();
  }

  /**
   * Get sync status for the UI (simplified for Firestore)
   */
  static getSyncStatus(): SyncStatusInfo {
    return {
      isOnline: this.isOnline,
      lastSync: this.lastSyncTimestamp || null,
      inProgress: false, // Firestore handles sync automatically
      hasChanges: false, // Firestore sync is immediate when online
      queueSize: 0 // Firestore doesn't have a queue
    };
  }

  /**
   * Refresh the local cache
   */
  static async refreshCache(): Promise<void> {
    try {
      // Force refresh by clearing and re-enabling the cache
      await this.clearLocalCache();
      log.info('Firestore cache refreshed', 'STORAGE', {});
    } catch (error) {
      log.error('Failed to refresh cache', 'STORAGE', { error });
      throw error;
    }
  }

  /**
   * Clear the local Firestore cache
   */
  static async clearLocalCache(): Promise<void> {
    try {
      await clearIndexedDbPersistence(db);
      log.info('Firestore local cache cleared', 'STORAGE', {});
    } catch (error) {
      log.error('Failed to clear local cache', 'STORAGE', { error });
      // If clearIndexedDbPersistence fails, we can try disabling and re-enabling network
      try {
        await disableNetwork(db);
        await enableNetwork(db);
        log.info('Network reset as fallback for cache clear', 'STORAGE', {});
      } catch (networkError) {
        log.error('Failed to reset network as cache clear fallback', 'STORAGE', { error: networkError });
        throw error; // Throw original error
      }
    }
  }

  /**
   * Get checklists by sync status
   */
  static async getChecklistsBySyncStatus(userId: string, status: string): Promise<ChecklistData[]> {
    try {
      const q = query(
        collection(db, this.CHECKLISTS_COLLECTION),
        where('userId', '==', userId),
        where('syncMetadata.status', '==', status),
        orderBy('updatedAt', 'desc')
      );

      const querySnapshot = await getDocs(q);
      const checklists: ChecklistData[] = [];

      querySnapshot.forEach((doc) => {
        const data = doc.data();
        checklists.push({
          ...data,
          id: doc.id,
          createdAt: data.createdAt instanceof Timestamp ? data.createdAt.toDate().toISOString() : data.createdAt,
          updatedAt: data.updatedAt instanceof Timestamp ? data.updatedAt.toDate().toISOString() : data.updatedAt,
          completedAt: data.completedAt instanceof Timestamp ? data.completedAt.toDate().toISOString() : data.completedAt
        } as ChecklistData);
      });

      return checklists;
    } catch (error) {
      log.error('Failed to get checklists by sync status', 'STORAGE', { 
        userId, 
        status, 
        error: error instanceof Error ? error.message : 'Unknown error' 
      });
      return [];
    }
  }

  /**
   * Perform cleanup of old completed checklists
   */
  static async performCleanup(userId: string): Promise<{ cleaned: number; errors: string[] }> {
    const result = { cleaned: 0, errors: [] as string[] };
    
    try {
      const settings = await this.getSettings(userId);
      const now = new Date();
      const cutoffDate = new Date(now.getTime() - (settings.daysToKeepCompleted || 30) * 24 * 60 * 60 * 1000);

      const q = query(
        collection(db, this.CHECKLISTS_COLLECTION),
        where('userId', '==', userId),
        where('isCompleted', '==', true),
        where('completedAt', '<', cutoffDate.toISOString())
      );

      const querySnapshot = await getDocs(q);
      const batch = writeBatch(db);

      querySnapshot.forEach((doc) => {
        batch.delete(doc.ref);
        result.cleaned++;
      });

      if (result.cleaned > 0) {
        await batch.commit();
        log.info('Cleanup completed', 'STORAGE', { 
          userId, 
          cleaned: result.cleaned 
        });
      }

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      result.errors.push(errorMessage);
      log.error('Cleanup failed', 'STORAGE', { userId, error: errorMessage });
    }

    return result;
  }
}

/**
 * Calculate checklist summary statistics
 */
export function calculateChecklistSummary(checklist: ChecklistData): ChecklistSummary {
  let totalOk = 0;
  let totalFaulty = 0;
  let totalNA = 0;
  let totalMissing = 0;
  let totalChecks = 0;

  // Helper function to count status values
  const countStatus = (obj: any) => {
    Object.values(obj).forEach((value) => {
      if (typeof value === 'string') {
        totalChecks++;
        switch (value) {
          case 'OK':
            totalOk++;
            break;
          case 'Faulty':
            totalFaulty++;
            break;
          case 'N/A':
            totalNA++;
            break;
          case 'Missing':
            totalMissing++;
            break;
        }
      }
    });
  };

  // Count mechanical checks
  countStatus(checklist.mechanicalChecks);
  
  // Count electrical checks
  countStatus(checklist.electricalChecks);
  
  // Count sequence controls checks
  countStatus(checklist.sequenceControlsChecks);

  return {
    totalOk,
    totalFaulty,
    totalNA,
    totalMissing,
    totalChecks
  };
}

 
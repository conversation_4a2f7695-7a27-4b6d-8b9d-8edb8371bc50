import { ChecklistData, ChecklistSummary, SyncMetadata } from '@/types/checklist';
import { UserRole } from '@/types/user';
import { log } from '../../utils/logger';
import { FirestoreStorageService, type SyncStatusInfo } from './firestore-storage';

/**
 * Storage Service - Firestore-based storage with offline persistence
 * Provides a simplified interface over FirestoreStorageService
 */

// Export storage utilities
export { calculateChecklistSummary } from './firestore-storage';

// Re-export types for compatibility
export type { StorageSettings, StorageStatus } from './firestore-storage';

interface StorageInfo {
  currentSize: number;
  maxSize: number;
  usagePercentage: number;
  itemCount: number;
  canSave: boolean;
}

export class StorageService {
  private static userId: string | null = null;
  private static initialized = false;
  private static initializationPromise: Promise<void> | null = null;

  /**
   * Initialize the storage service with user context
   */
  static async initialize(userId: string): Promise<void> {
    // Early return if already initialized for this user
    if (this.initialized && this.userId === userId) {
      log.info('Storage service already initialized for user', 'STORAGE', { userId });
      return;
    }

    // If there's an ongoing initialization for the same user, wait for it
    if (this.initializationPromise && this.userId === userId) {
      await this.initializationPromise;
      return;
    }

    // Reset state if switching users
    if (this.userId && this.userId !== userId) {
      log.info('Switching users, resetting storage service', 'STORAGE', { 
        oldUserId: this.userId, 
        newUserId: userId 
      });
      this.reset();
    }

    // Create initialization promise to prevent race conditions
    this.initializationPromise = this.performInitialization(userId);
    
    try {
      await this.initializationPromise;
    } finally {
      this.initializationPromise = null;
    }
  }

  /**
   * Internal initialization method
   */
  private static async performInitialization(userId: string): Promise<void> {
    this.userId = userId;
    
    try {
      // Initialize Firebase Storage first to ensure it's available
      try {
        const { getFirebaseStorage, validateFirebaseStorageConfig } = await import('@/config/firebase-config');
        
        if (!validateFirebaseStorageConfig()) {
          log.warn('Firebase Storage configuration incomplete - image features may not work', 'STORAGE', { userId });
        } else {
          getFirebaseStorage();
          log.debug('Firebase Storage confirmed initialized', 'STORAGE', { userId });
        }
      } catch (storageError) {
        log.warn('Firebase Storage initialization issue - continuing with Firestore only', 'STORAGE', {
          userId,
          error: storageError instanceof Error ? storageError.message : 'Unknown error'
        });
      }

      await FirestoreStorageService.initialize(userId);
      this.initialized = true;
      log.info('Storage service initialized with Firestore', 'STORAGE', { userId });
    } catch (error) {
      this.initialized = false;
      this.userId = null;
      throw error;
    }
  }

  /**
   * Reset the storage service state
   */
  static reset(): void {
    this.userId = null;
    this.initialized = false;
    this.initializationPromise = null;
  }

  /**
   * Check if storage service is properly initialized
   */
  static isInitialized(): boolean {
    return this.initialized && this.userId !== null;
  }

  /**
   * Get all checklists for the current user
   */
  static async getAllChecklists(): Promise<ChecklistData[]> {
    if (!this.ensureInitialized()) {
      throw new Error('Storage service not initialized');
    }
    return await FirestoreStorageService.getAllChecklists(this.userId!);
  }

  /**
   * Get a specific checklist by ID
   */
  static async getChecklistById(id: string): Promise<ChecklistData | null> {
    if (!this.ensureInitialized()) {
      throw new Error('Storage service not initialized');
    }
    return await FirestoreStorageService.getChecklist(id, this.userId!);
  }

  /**
   * Save a checklist
   */
  static async saveChecklist(checklist: ChecklistData, userRole?: UserRole): Promise<boolean> {
    if (!this.ensureInitialized()) {
      throw new Error('Storage service not initialized');
    }

    try {
      await FirestoreStorageService.saveChecklist(checklist, this.userId!);
      return true;
    } catch (error) {
      log.error('Failed to save checklist', 'STORAGE', {
        checklistId: checklist.id,
        userId: this.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * Update checklist completion status
   */
  static async updateCompletionStatus(
    id: string,
    isCompleted: boolean,
    completedBy: string,
    userRole: UserRole
  ): Promise<boolean> {
    if (!this.ensureInitialized()) {
      throw new Error('Storage service not initialized');
    }

    try {
      const completedAt = isCompleted ? new Date().toISOString() : undefined;
      await FirestoreStorageService.updateChecklistStatus(id, this.userId!, isCompleted, completedAt);
      return true;
    } catch (error) {
      log.error('Failed to update completion status', 'STORAGE', {
        checklistId: id,
        userId: this.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * Delete a checklist
   */
  static async deleteChecklist(id: string): Promise<boolean> {
    if (!this.ensureInitialized()) {
      throw new Error('Storage service not initialized');
    }

    try {
      await FirestoreStorageService.deleteChecklist(id, this.userId!);
      return true;
    } catch (error) {
      log.error('Failed to delete checklist', 'STORAGE', {
        checklistId: id,
        userId: this.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * Subscribe to real-time checklist updates
   */
  static subscribeToChecklists(callback: (checklists: ChecklistData[]) => void): () => void {
    if (!this.ensureInitialized()) {
      return () => {};
    }
    return FirestoreStorageService.subscribeToChecklists(this.userId!, callback);
  }

  /**
   * Clear all user data (permanent deletion)
   */
  static async clearAllChecklists(): Promise<boolean> {
    if (!this.ensureInitialized()) {
      throw new Error('Storage service not initialized');
    }

    try {
      await FirestoreStorageService.deleteAllUserChecklists(this.userId!);
      return true;
    } catch (error) {
      log.error('Failed to clear all checklists', 'STORAGE', {
        userId: this.userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return false;
    }
  }

  /**
   * Generate a new unique ID
   */
  static generateId(): string {
    return crypto.randomUUID?.() || 
           `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * Create default sync metadata
   */
  static createDefaultSyncMetadata(): SyncMetadata {
    return {
      status: 'local-only',
      lastLocalUpdateAt: new Date().toISOString(),
      localVersion: 1
    };
  }

  /**
   * Get storage information and statistics
   */
  static async getStorageInfo(): Promise<StorageInfo> {
    if (!this.ensureInitialized()) {
      throw new Error('Storage service not initialized');
    }

    const status = await FirestoreStorageService.getStorageStatus(this.userId!);
    return {
      currentSize: status.used,
      maxSize: status.quota,
      usagePercentage: (status.used / status.quota) * 100,
      itemCount: 0, // Would need separate query
      canSave: status.available
    };
  }

  /**
   * Get sync status
   */
  static getSyncStatus(): SyncStatusInfo {
    return FirestoreStorageService.getSyncStatus();
  }

  /**
   * Bulk operations
   */
  static async bulkSaveChecklists(checklists: ChecklistData[]): Promise<void> {
    if (!this.ensureInitialized()) {
      throw new Error('Storage service not initialized');
    }
    await FirestoreStorageService.bulkSaveChecklists(checklists, this.userId!);
  }

  /**
   * Bulk delete checklists
   */
  static async bulkDeleteChecklists(ids: string[]): Promise<{ success: string[]; failed: string[] }> {
    if (!this.ensureInitialized()) {
      throw new Error('Storage service not initialized');
    }

    const result = { success: [] as string[], failed: [] as string[] };
    
    for (const id of ids) {
      try {
        await FirestoreStorageService.deleteChecklist(id, this.userId!);
        result.success.push(id);
      } catch (error) {
        result.failed.push(id);
        log.error('Failed to delete checklist in bulk operation', 'STORAGE', {
          checklistId: id,
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      }
    }

    return result;
  }

  /**
   * Perform cleanup of old checklists
   */
  static async performCleanup(): Promise<{ cleaned: number; errors: string[] }> {
    if (!this.ensureInitialized()) {
      throw new Error('Storage service not initialized');
    }

    try {
      return await FirestoreStorageService.performCleanup(this.userId!);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      return { cleaned: 0, errors: [errorMessage] };
    }
  }

  /**
   * Get network status
   */
  static getNetworkStatus(): { isOnline: boolean; lastSync?: string } {
    return FirestoreStorageService.getNetworkStatus();
  }

  /**
   * Get checklists by sync status
   */
  static async getChecklistsBySyncStatus(status: string): Promise<ChecklistData[]> {
    if (!this.ensureInitialized()) {
      throw new Error('Storage service not initialized');
    }
    return await FirestoreStorageService.getChecklistsBySyncStatus(this.userId!, status);
  }

  /**
   * Get checklists by user ID (admin function)
   */
  static async getChecklistsByUserId(userId: string): Promise<ChecklistData[]> {
    if (!this.ensureInitialized()) {
      throw new Error('Storage service not initialized');
    }
    return await FirestoreStorageService.getAllChecklists(userId);
  }

  // Private helper methods

  private static ensureInitialized(): boolean {
    if (!this.initialized || !this.userId) {
      log.error('Storage service not initialized when attempting operation', 'STORAGE', {
        initialized: this.initialized,
        hasUserId: !!this.userId,
        initializationPromise: !!this.initializationPromise,
        timestamp: new Date().toISOString()
      });
      return false;
    }
    return true;
  }
}

 
import { 
  ref, 
  uploadBytes, 
  getDownloadURL, 
  deleteObject,
  getMetadata,
  listAll
} from 'firebase/storage';
import { getFirebaseStorage } from '@/config/firebase-config';
import { ExportUploadResult, ExportMetadata } from '@/types/export-queue';
import { log } from '@/lib/utils/logger';
import { format } from 'date-fns';

export class ExportStorageService {
  private static readonly EXPORTS_PATH = 'exports';
  private static readonly EXPIRATION_DAYS = 7;

  /**
   * Upload export file to Firebase Storage
   */
  static async uploadExport(
    fileBytes: Uint8Array,
    userId: string,
    queueId: string,
    type: 'pdf' | 'excel',
    metadata: ExportMetadata
  ): Promise<ExportUploadResult> {
    try {
      const storage = getFirebaseStorage();
      const fileName = this.generateFileName(metadata, type, queueId);
      const filePath = `${this.EXPORTS_PATH}/${userId}/${type}/${fileName}`;
      
      // Create storage reference
      const storageRef = ref(storage, filePath);
      
      // Calculate expiration date
      const expiresAt = new Date();
      expiresAt.setDate(expiresAt.getDate() + this.EXPIRATION_DAYS);
      
      // Upload file with metadata
      const snapshot = await uploadBytes(storageRef, fileBytes, {
        contentType: type === 'pdf' ? 'application/pdf' : 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        customMetadata: {
          userId,
          queueId,
          type,
          checklistCount: metadata.checklistCount.toString(),
          clientName: metadata.clientName || '',
          dateRange: metadata.dateRange || '',
          requestedBy: metadata.requestedBy,
          uploadedAt: new Date().toISOString(),
          expiresAt: expiresAt.toISOString(),
          version: '1.0'
        }
      });

      // Get download URL
      const downloadUrl = await getDownloadURL(snapshot.ref);

      const result: ExportUploadResult = {
        downloadUrl,
        storagePath: filePath,
        fileName,
        fileSize: fileBytes.length,
        expiresAt
      };

      log.info('Export uploaded to storage', 'EXPORT_STORAGE', {
        userId,
        queueId,
        type,
        fileName,
        fileSize: fileBytes.length,
        expiresAt: expiresAt.toISOString()
      });

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to upload export', 'EXPORT_STORAGE', {
        userId,
        queueId,
        type,
        error: errorMessage
      });
      throw new Error(`Failed to upload export: ${errorMessage}`);
    }
  }

  /**
   * Delete an export file from storage
   */
  static async deleteExport(storagePath: string): Promise<void> {
    try {
      const storage = getFirebaseStorage();
      const fileRef = ref(storage, storagePath);
      await deleteObject(fileRef);

      log.info('Export deleted from storage', 'EXPORT_STORAGE', { storagePath });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to delete export', 'EXPORT_STORAGE', {
        storagePath,
        error: errorMessage
      });
      throw new Error(`Failed to delete export: ${errorMessage}`);
    }
  }

  /**
   * Clean up expired exports for a user
   */
  static async cleanupExpiredExports(userId: string): Promise<{ cleaned: number; errors: string[] }> {
    const result = { cleaned: 0, errors: [] as string[] };
    
    try {
      const storage = getFirebaseStorage();
      const userExportsRef = ref(storage, `${this.EXPORTS_PATH}/${userId}`);
      
      // List all export types (pdf, excel)
      const typesList = await listAll(userExportsRef);
      
      for (const typeRef of typesList.prefixes) {
        const filesList = await listAll(typeRef);
        
        for (const fileRef of filesList.items) {
          try {
            const metadata = await getMetadata(fileRef);
            const expiresAt = metadata.customMetadata?.expiresAt;
            
            if (expiresAt && new Date(expiresAt) < new Date()) {
              await deleteObject(fileRef);
              result.cleaned++;
              log.info('Expired export cleaned up', 'EXPORT_STORAGE', {
                userId,
                fileName: fileRef.name,
                expiresAt
              });
            }
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            result.errors.push(`Failed to process ${fileRef.name}: ${errorMessage}`);
          }
        }
      }

      log.info('Export cleanup completed', 'EXPORT_STORAGE', {
        userId,
        cleaned: result.cleaned,
        errors: result.errors.length
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      result.errors.push(errorMessage);
      log.error('Failed to cleanup expired exports', 'EXPORT_STORAGE', {
        userId,
        error: errorMessage
      });
    }

    return result;
  }

  /**
   * Get all exports for a user
   */
  static async getUserExports(userId: string): Promise<Array<{
    name: string;
    fullPath: string;
    downloadUrl: string;
    size: number;
    uploadedAt: string;
    expiresAt: string;
    type: string;
    metadata: any;
  }>> {
    try {
      const storage = getFirebaseStorage();
      const userExportsRef = ref(storage, `${this.EXPORTS_PATH}/${userId}`);
      
      const typesList = await listAll(userExportsRef);
      const exports = [];
      
      for (const typeRef of typesList.prefixes) {
        const filesList = await listAll(typeRef);
        
        for (const fileRef of filesList.items) {
          try {
            const [downloadUrl, metadata] = await Promise.all([
              getDownloadURL(fileRef),
              getMetadata(fileRef)
            ]);

            exports.push({
              name: fileRef.name,
              fullPath: fileRef.fullPath,
              downloadUrl,
              size: metadata.size,
              uploadedAt: metadata.customMetadata?.uploadedAt || metadata.timeCreated,
              expiresAt: metadata.customMetadata?.expiresAt || '',
              type: metadata.customMetadata?.type || 'unknown',
              metadata: metadata.customMetadata || {}
            });
          } catch (error) {
            log.warn('Failed to get export metadata', 'EXPORT_STORAGE', {
              userId,
              fileName: fileRef.name,
              error: error instanceof Error ? error.message : 'Unknown error'
            });
          }
        }
      }

      return exports.sort((a, b) => 
        new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime()
      );

    } catch (error) {
      log.error('Failed to get user exports', 'EXPORT_STORAGE', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return [];
    }
  }

  /**
   * Get storage usage for user exports
   */
  static async getExportStorageUsage(userId: string): Promise<{ totalSize: number; fileCount: number }> {
    try {
      const exports = await this.getUserExports(userId);
      const totalSize = exports.reduce((sum, exp) => sum + exp.size, 0);
      
      return {
        totalSize,
        fileCount: exports.length
      };

    } catch (error) {
      log.error('Failed to get export storage usage', 'EXPORT_STORAGE', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return { totalSize: 0, fileCount: 0 };
    }
  }

  /**
   * Generate filename for export
   */
  private static generateFileName(
    metadata: ExportMetadata,
    type: 'pdf' | 'excel',
    queueId: string
  ): string {
    const timestamp = format(new Date(), 'yyyy-MM-dd_HH-mm');
    const clientName = metadata.clientName 
      ? metadata.clientName.replace(/[^a-zA-Z0-9]/g, '_').substring(0, 20)
      : 'Export';
    
    const extension = type === 'pdf' ? 'pdf' : 'xlsx';
    
    return `${clientName}_${metadata.checklistCount}_Items_${timestamp}_${queueId.substring(0, 8)}.${extension}`;
  }

  /**
   * Validate if export file exists and is accessible
   */
  static async validateExportUrl(downloadUrl: string): Promise<boolean> {
    try {
      const response = await fetch(downloadUrl, { method: 'HEAD' });
      return response.ok;
    } catch (error) {
      log.warn('Export URL validation failed', 'EXPORT_STORAGE', { downloadUrl });
      return false;
    }
  }

  /**
   * Refresh download URL for an export
   */
  static async refreshDownloadUrl(storagePath: string): Promise<string | null> {
    try {
      const storage = getFirebaseStorage();
      const fileRef = ref(storage, storagePath);
      const refreshedUrl = await getDownloadURL(fileRef);
      
      log.info('Export download URL refreshed', 'EXPORT_STORAGE', { storagePath });
      return refreshedUrl;
      
    } catch (error) {
      log.error('Failed to refresh export download URL', 'EXPORT_STORAGE', {
        storagePath,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return null;
    }
  }
} 
import { 
  ref, 
  uploadBytes, 
  getDownloadURL, 
  deleteObject,
  listAll,
  getMetadata
} from 'firebase/storage';
import { getFirebaseStorage } from '@/config/firebase-config';
import { log } from '../../utils/logger';
import { compressImage, CompressionOptions } from '../../utils/compression/image-compression';

export interface ImageUploadResult {
  url: string;
  path: string;
  originalName: string;
  size: number;
  compressedSize: number;
  compressionRatio: number;
}

export interface ImageMetadata {
  url: string;
  path: string;
  name: string;
  size: number;
  uploadedAt: string;
  contentType: string;
}

export class FirebaseStorageService {
  private static readonly IMAGES_PATH = 'checklist-images';
  
  /**
   * Upload an image file to Firebase Storage with compression
   */
  static async uploadImage(
    file: File,
    userId: string,
    checklistId: string,
    imageType: 'before' | 'after' | 'signature',
    compressionOptions?: CompressionOptions
  ): Promise<ImageUploadResult> {
    try {
      // Compress the image first
      const compressionResult = await compressImage(file, {
        profile: imageType === 'signature' ? 'signature' : 'standard',
        preserveTransparency: imageType === 'signature',
        ...compressionOptions
      });

      // Generate unique filename
      const timestamp = Date.now();
      const fileName = `${checklistId}_${imageType}_${timestamp}.${this.getFileExtension(compressionResult.compressedFile)}`;
      const filePath = `${this.IMAGES_PATH}/${userId}/${fileName}`;

      // Create storage reference
      const storage = getFirebaseStorage();
      const storageRef = ref(storage, filePath);

      // Upload file
      const snapshot = await uploadBytes(storageRef, compressionResult.compressedFile, {
        contentType: compressionResult.compressedFile.type,
        customMetadata: {
          originalName: file.name,
          originalSize: file.size.toString(),
          compressedSize: compressionResult.compressedFile.size.toString(),
          compressionRatio: compressionResult.compressionRatio.toString(),
          imageType,
          checklistId,
          userId,
          uploadedAt: new Date().toISOString()
        }
      });

      // Get download URL
      const downloadURL = await getDownloadURL(snapshot.ref);

      log.info('Image uploaded successfully', 'STORAGE', {
        userId,
        checklistId,
        imageType,
        originalSize: file.size,
        compressedSize: compressionResult.compressedSize,
        compressionRatio: compressionResult.compressionRatio
      });

      return {
        url: downloadURL,
        path: filePath,
        originalName: file.name,
        size: file.size,
        compressedSize: compressionResult.compressedSize,
        compressionRatio: compressionResult.compressionRatio
      };

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to upload image', 'STORAGE', {
        userId,
        checklistId,
        imageType,
        error: errorMessage
      });
      throw new Error(`Failed to upload image: ${errorMessage}`);
    }
  }

  /**
   * Delete an image from Firebase Storage
   */
  static async deleteImage(imagePath: string): Promise<void> {
    try {
      const storage = getFirebaseStorage();
      const imageRef = ref(storage, imagePath);
      await deleteObject(imageRef);

      log.info('Image deleted successfully', 'STORAGE', { imagePath });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to delete image', 'STORAGE', {
        imagePath,
        error: errorMessage
      });
      throw new Error(`Failed to delete image: ${errorMessage}`);
    }
  }

  /**
   * Get all images for a specific user
   */
  static async getUserImages(userId: string): Promise<ImageMetadata[]> {
    try {
      const storage = getFirebaseStorage();
      const userImagesRef = ref(storage, `${this.IMAGES_PATH}/${userId}`);
      
      const listResult = await listAll(userImagesRef);
      const images: ImageMetadata[] = [];

      for (const itemRef of listResult.items) {
        try {
          const [url, metadata] = await Promise.all([
            getDownloadURL(itemRef),
            getMetadata(itemRef)
          ]);

          images.push({
            url,
            path: itemRef.fullPath,
            name: itemRef.name,
            size: metadata.size,
            uploadedAt: metadata.customMetadata?.uploadedAt || metadata.timeCreated,
            contentType: metadata.contentType || 'image/jpeg'
          });
        } catch (error) {
          log.warn('Failed to get metadata for image', 'STORAGE', {
            path: itemRef.fullPath,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      return images.sort((a, b) => 
        new Date(b.uploadedAt).getTime() - new Date(a.uploadedAt).getTime()
      );

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to get user images', 'STORAGE', {
        userId,
        error: errorMessage
      });
      return [];
    }
  }

  /**
   * Get storage usage for a user
   */
  static async getUserStorageUsage(userId: string): Promise<{ totalSize: number; imageCount: number }> {
    try {
      const images = await this.getUserImages(userId);
      const totalSize = images.reduce((sum, image) => sum + image.size, 0);
      
      return {
        totalSize,
        imageCount: images.length
      };

    } catch (error) {
      log.error('Failed to get storage usage', 'STORAGE', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return { totalSize: 0, imageCount: 0 };
    }
  }

  /**
   * Cleanup orphaned images with legacy naming pattern
   */
  static async cleanupLegacyOrphanedImages(userId: string): Promise<{ cleaned: number; errors: string[] }> {
    const result = { cleaned: 0, errors: [] as string[] };
    
    try {
      const storage = getFirebaseStorage();
      const userImagesRef = ref(storage, `${this.IMAGES_PATH}/${userId}`);
      
      const listResult = await listAll(userImagesRef);
      
      // Find images with legacy pattern: checklist_{timestamp}_{random}_*
      const legacyPattern = /^checklist_\d+_[a-z0-9]+_/;
      
      for (const itemRef of listResult.items) {
        if (legacyPattern.test(itemRef.name)) {
          try {
            await deleteObject(itemRef);
            result.cleaned++;
            log.info('Cleaned up legacy orphaned image', 'STORAGE', {
              userId,
              imageName: itemRef.name
            });
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : 'Unknown error';
            result.errors.push(`Failed to delete ${itemRef.name}: ${errorMessage}`);
            log.warn('Failed to cleanup legacy image', 'STORAGE', {
              userId,
              imageName: itemRef.name,
              error: errorMessage
            });
          }
        }
      }

      log.info('Legacy orphaned images cleanup completed', 'STORAGE', {
        userId,
        cleaned: result.cleaned,
        errors: result.errors.length
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      result.errors.push(errorMessage);
      log.error('Failed to cleanup legacy orphaned images', 'STORAGE', {
        userId,
        error: errorMessage
      });
    }

    return result;
  }

  /**
   * Cleanup old images for a checklist
   */
  static async cleanupChecklistImages(userId: string, checklistId: string): Promise<void> {
    try {
      const storage = getFirebaseStorage();
      const userImagesRef = ref(storage, `${this.IMAGES_PATH}/${userId}`);
      
      const listResult = await listAll(userImagesRef);
      const deletionPromises: Promise<void>[] = [];
      const foundImages: string[] = [];
      const matchingImages: string[] = [];

      // Log all found images for debugging
      for (const itemRef of listResult.items) {
        foundImages.push(itemRef.name);
        if (itemRef.name.startsWith(`${checklistId}_`)) {
          matchingImages.push(itemRef.name);
          // Wrap deletion in individual try-catch to track which specific deletions fail
          deletionPromises.push(
            deleteObject(itemRef).catch(error => {
              log.error('Failed to delete individual image', 'STORAGE', {
                userId,
                checklistId,
                imageName: itemRef.name,
                imagePath: itemRef.fullPath,
                error: error instanceof Error ? error.message : 'Unknown error'
              });
              throw error; // Re-throw to maintain Promise.all behavior
            })
          );
        }
      }

      // Enhanced logging for debugging
      log.info('Checklist image cleanup analysis', 'STORAGE', {
        userId,
        checklistId,
        totalImagesFound: foundImages.length,
        foundImages: foundImages,
        matchingImages: matchingImages,
        matchingPattern: `${checklistId}_`,
        plannedDeletions: deletionPromises.length
      });

      // Use Promise.allSettled to get details on individual failures
      const results = await Promise.allSettled(deletionPromises);
      const failures = results.filter(result => result.status === 'rejected');
      
      if (failures.length > 0) {
        log.warn('Some image deletions failed', 'STORAGE', {
          userId,
          checklistId,
          totalAttempted: deletionPromises.length,
          failureCount: failures.length,
          successCount: results.length - failures.length
        });
      }

      log.info('Checklist images cleaned up', 'STORAGE', {
        userId,
        checklistId,
        deletedCount: deletionPromises.length,
        deletedImages: matchingImages
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to cleanup checklist images', 'STORAGE', {
        userId,
        checklistId,
        error: errorMessage
      });
      throw new Error(`Failed to cleanup images: ${errorMessage}`);
    }
  }

  /**
   * Cleanup old images older than specified days
   */
  static async cleanupOldImages(userId: string, daysToKeep: number = 30): Promise<{ cleaned: number; errors: string[] }> {
    const result = { cleaned: 0, errors: [] as string[] };
    
    try {
      const images = await this.getUserImages(userId);
      const cutoffDate = new Date();
      cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

      const imagesToDelete = images.filter(image => 
        new Date(image.uploadedAt) < cutoffDate
      );

      for (const image of imagesToDelete) {
        try {
          await this.deleteImage(image.path);
          result.cleaned++;
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          result.errors.push(`Failed to delete ${image.name}: ${errorMessage}`);
        }
      }

      log.info('Old images cleanup completed', 'STORAGE', {
        userId,
        cleaned: result.cleaned,
        errors: result.errors.length
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      result.errors.push(errorMessage);
      log.error('Failed to cleanup old images', 'STORAGE', {
        userId,
        error: errorMessage
      });
    }

    return result;
  }

  /**
   * Get file extension from file type
   */
  private static getFileExtension(file: File): string {
    const mimeTypeMap: Record<string, string> = {
      'image/jpeg': 'jpg',
      'image/jpg': 'jpg',
      'image/png': 'png',
      'image/webp': 'webp',
      'image/gif': 'gif'
    };
    
    return mimeTypeMap[file.type] || 'jpg';
  }

  /**
   * Refresh an expired download URL for an image
   */
  static async refreshImageUrl(imagePath: string): Promise<string | null> {
    try {
      const storage = getFirebaseStorage();
      const imageRef = ref(storage, imagePath);
      const refreshedUrl = await getDownloadURL(imageRef);
      
      log.info('Image URL refreshed successfully', 'STORAGE', { imagePath });
      return refreshedUrl;
      
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to refresh image URL', 'STORAGE', {
        imagePath,
        error: errorMessage
      });
      return null;
    }
  }

  /**
   * Validate if an image URL is accessible
   */
  static async validateImageUrl(url: string): Promise<boolean> {
    try {
      const response = await fetch(url, { method: 'HEAD' });
      return response.ok;
    } catch (error) {
      log.warn('Image URL validation failed', 'STORAGE', { url });
      return false;
    }
  }
} 
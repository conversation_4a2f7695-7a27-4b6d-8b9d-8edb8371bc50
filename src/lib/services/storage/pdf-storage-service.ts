import { 
  ref, 
  uploadBytes, 
  getDownloadURL, 
  deleteObject,
  getMetadata
} from 'firebase/storage';
import { getFirebaseStorage } from '@/config/firebase-config';
import { ChecklistData } from '@/types/checklist';
import { log } from '../../utils/logger';

export interface PDFCacheResult {
  url: string;
  path: string;
  size: number;
  contentHash: string;
  generatedAt: string;
}

export interface PDFCacheCheck {
  exists: boolean;
  isValid: boolean;
  metadata?: PDFCacheResult;
  reason?: string;
}

export class PDFStorageService {
  private static readonly PDF_PATH = 'checklist-pdfs';
  private static readonly MAX_CACHE_AGE_DAYS = 120;
  
  /**
   * Generate content hash for checklist to detect changes
   */
  static async generateContentHash(checklist: ChecklistData): Promise<string> {
    // Only hash fields that affect PDF content
    const relevantData = {
      generalInfo: checklist.generalInfo,
      mechanicalChecks: checklist.mechanicalChecks,
      electricalChecks: checklist.electricalChecks,
      sequenceControlsChecks: checklist.sequenceControlsChecks,
      remarks: checklist.remarks,
      beforeImage: checklist.beforeImage,
      afterImage: checklist.afterImage,
      inspectorSignature: checklist.inspectorSignature,
      clientSignature: checklist.clientSignature,
    };
    
    // Create deterministic hash using Web Crypto API (browser compatible)
    const dataString = JSON.stringify(relevantData, Object.keys(relevantData).sort());
    const encoder = new TextEncoder();
    const data = encoder.encode(dataString);
    const hashBuffer = await crypto.subtle.digest('SHA-256', data);
    const hashArray = Array.from(new Uint8Array(hashBuffer));
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
  }

  /**
   * Save PDF to Firebase Storage and return metadata
   */
  static async savePDF(
    pdfBytes: Uint8Array,
    userId: string,
    checklistId: string,
    contentHash: string
  ): Promise<PDFCacheResult> {
    try {
      const storage = getFirebaseStorage();
      const timestamp = Date.now();
      const fileName = `${checklistId}_${timestamp}.pdf`;
      const filePath = `${this.PDF_PATH}/${userId}/${fileName}`;
      
      // Create storage reference
      const storageRef = ref(storage, filePath);
      
      // Upload PDF with metadata
      const snapshot = await uploadBytes(storageRef, pdfBytes, {
        contentType: 'application/pdf',
        customMetadata: {
          checklistId,
          userId,
          contentHash,
          generatedAt: new Date().toISOString(),
          version: '1.0'
        }
      });

      // Get download URL
      const downloadURL = await getDownloadURL(snapshot.ref);

      const result: PDFCacheResult = {
        url: downloadURL,
        path: filePath,
        size: pdfBytes.length,
        contentHash,
        generatedAt: new Date().toISOString()
      };

      log.info('PDF cached successfully', 'PDF_STORAGE', {
        userId,
        checklistId,
        size: pdfBytes.length,
        path: filePath
      });

      return result;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('Failed to cache PDF', 'PDF_STORAGE', {
        userId,
        checklistId,
        error: errorMessage
      });
      throw new Error(`Failed to cache PDF: ${errorMessage}`);
    }
  }

  /**
   * Check if a valid cached PDF exists for the checklist
   */
  static async checkCachedPDF(
    userId: string,
    checklistId: string,
    checklist?: ChecklistData
  ): Promise<PDFCacheCheck> {
    try {
      const storage = getFirebaseStorage();
      const folderPath = `${this.PDF_PATH}/${userId}/`;
      
      // Look for PDF files matching the checklist ID pattern
      const folderRef = ref(storage, folderPath);
      
      try {
        // Try to get the most recent PDF for this checklist
        // Note: This is a simplified approach - in production you might want to
        // implement a more sophisticated file discovery mechanism
        const files = await this.listUserPDFs(userId);
        const checklistPDFs = files.filter(file => 
          file.name.startsWith(`${checklistId}_`) && file.name.endsWith('.pdf')
        );
        
        if (checklistPDFs.length === 0) {
          return { exists: false, isValid: false, reason: 'No cached PDF found' };
        }
        
        // Get the most recent PDF
        const mostRecent = checklistPDFs.sort((a, b) => 
          new Date(b.metadata.timeCreated).getTime() - new Date(a.metadata.timeCreated).getTime()
        )[0];
        
        // Check if content hash matches (if available and checklist provided)
        const cachedHash = mostRecent.metadata.customMetadata?.contentHash;
        if (cachedHash && checklist) {
          const currentContentHash = await this.generateContentHash(checklist);
          if (cachedHash !== currentContentHash) {
            return { 
              exists: true, 
              isValid: false, 
              reason: 'Content hash mismatch - checklist has been modified' 
            };
          }
        }
        
        // Check if PDF is not too old
        const generatedAt = new Date(mostRecent.metadata.customMetadata?.generatedAt || mostRecent.metadata.timeCreated);
        const ageInDays = (Date.now() - generatedAt.getTime()) / (1000 * 60 * 60 * 24);
        
        if (ageInDays > this.MAX_CACHE_AGE_DAYS) {
          return { 
            exists: true, 
            isValid: false, 
            reason: `PDF too old (${Math.round(ageInDays)} days)` 
          };
        }
        
        // PDF is valid - return metadata
        const downloadURL = await getDownloadURL(ref(storage, mostRecent.fullPath));
        
        return {
          exists: true,
          isValid: true,
          metadata: {
            url: downloadURL,
            path: mostRecent.fullPath,
            size: mostRecent.metadata.size,
            contentHash: cachedHash || (checklist ? await this.generateContentHash(checklist) : ''),
            generatedAt: generatedAt.toISOString()
          }
        };
        
      } catch (listError) {
        log.debug('Could not list PDFs for cache check', 'PDF_STORAGE', {
          userId,
          checklistId,
          error: listError instanceof Error ? listError.message : 'Unknown error'
        });
        return { exists: false, isValid: false, reason: 'Cache check failed' };
      }
      
    } catch (error) {
      log.error('Error checking cached PDF', 'PDF_STORAGE', {
        userId,
        checklistId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return { exists: false, isValid: false, reason: 'Cache check error' };
    }
  }

  /**
   * Get cached PDF bytes if available and valid
   */
  static async getCachedPDFBytes(
    userId: string,
    checklistId: string,
    checklist?: ChecklistData
  ): Promise<Uint8Array | null> {
    try {
      const cacheCheck = await this.checkCachedPDF(userId, checklistId, checklist);
      
      if (!cacheCheck.exists || !cacheCheck.isValid || !cacheCheck.metadata) {
        return null;
      }
      
      // Use fetch with proxy for Firebase Storage URLs to avoid CORS issues
      let pdfUrl = cacheCheck.metadata.url;
      
      // For Firebase Storage URLs, use our proxy API
      if (pdfUrl.includes('firebasestorage.googleapis.com') || pdfUrl.includes('firebasestorage.app')) {
        const { createProxyPdfUrl } = require('@/lib/config/cloud-functions');
        pdfUrl = createProxyPdfUrl(pdfUrl);
        log.debug('Using proxy for Firebase Storage PDF download', 'PDF_STORAGE', {
          userId,
          checklistId,
          originalUrl: cacheCheck.metadata.url,
          proxyUrl: pdfUrl
        });
      }
      
      const response = await fetch(pdfUrl, {
        method: 'GET',
        mode: 'cors',
        credentials: 'omit',
        headers: {
          'Accept': 'application/pdf'
        }
      });
      
      if (!response.ok) {
        throw new Error(`Failed to fetch PDF: ${response.status} ${response.statusText}`);
      }
      
      const arrayBuffer = await response.arrayBuffer();
      const uint8Array = new Uint8Array(arrayBuffer);
      
      log.debug('Retrieved cached PDF', 'PDF_STORAGE', {
        userId,
        checklistId,
        size: uint8Array.length
      });
      
      return uint8Array;
      
    } catch (error) {
      log.warn('Failed to retrieve cached PDF bytes', 'PDF_STORAGE', {
        userId,
        checklistId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return null;
    }
  }

  /**
   * Delete cached PDF
   */
  static async deleteCachedPDF(userId: string, checklistId: string): Promise<void> {
    try {
      const files = await this.listUserPDFs(userId);
      const checklistPDFs = files.filter(file => 
        file.name.startsWith(`${checklistId}_`) && file.name.endsWith('.pdf')
      );
      
      const storage = getFirebaseStorage();
      
      // Delete all PDFs for this checklist
      for (const file of checklistPDFs) {
        try {
          const fileRef = ref(storage, file.fullPath);
          await deleteObject(fileRef);
          log.debug('Deleted cached PDF', 'PDF_STORAGE', {
            userId,
            checklistId,
            path: file.fullPath
          });
        } catch (deleteError) {
          log.warn('Failed to delete individual PDF file', 'PDF_STORAGE', {
            userId,
            checklistId,
            path: file.fullPath,
            error: deleteError instanceof Error ? deleteError.message : 'Unknown error'
          });
        }
      }
      
    } catch (error) {
      log.error('Failed to delete cached PDF', 'PDF_STORAGE', {
        userId,
        checklistId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw new Error(`Failed to delete cached PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Clean up old or stale PDFs for a user
   */
  static async cleanupStalePDFs(userId: string): Promise<{
    deleted: number;
    errors: string[];
  }> {
    const result = { deleted: 0, errors: [] as string[] };
    
    try {
      const files = await this.listUserPDFs(userId);
      const storage = getFirebaseStorage();
      
      for (const file of files) {
        try {
          // Check if file is too old
          const createdAt = new Date(file.metadata.timeCreated);
          const ageInDays = (Date.now() - createdAt.getTime()) / (1000 * 60 * 60 * 24);
          
          if (ageInDays > this.MAX_CACHE_AGE_DAYS) {
            const fileRef = ref(storage, file.fullPath);
            await deleteObject(fileRef);
            result.deleted++;
            
            log.debug('Cleaned up old PDF', 'PDF_STORAGE', {
              userId,
              path: file.fullPath,
              ageInDays: Math.round(ageInDays)
            });
          }
          
        } catch (deleteError) {
          const errorMsg = `Failed to delete ${file.fullPath}: ${deleteError instanceof Error ? deleteError.message : 'Unknown error'}`;
          result.errors.push(errorMsg);
        }
      }
      
      log.info('PDF cleanup completed', 'PDF_STORAGE', {
        userId,
        deleted: result.deleted,
        errors: result.errors.length
      });
      
    } catch (error) {
      const errorMsg = `Cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      result.errors.push(errorMsg);
      log.error('PDF cleanup failed', 'PDF_STORAGE', {
        userId,
        error: errorMsg
      });
    }
    
    return result;
  }

  /**
   * Get storage usage for user's PDFs
   */
  static async getPDFStorageUsage(userId: string): Promise<{
    totalSize: number;
    fileCount: number;
    oldestFile?: string;
    newestFile?: string;
  }> {
    try {
      const files = await this.listUserPDFs(userId);
      
      const totalSize = files.reduce((sum, file) => sum + file.metadata.size, 0);
      const sortedByDate = files.sort((a, b) => 
        new Date(a.metadata.timeCreated).getTime() - new Date(b.metadata.timeCreated).getTime()
      );
      
      return {
        totalSize,
        fileCount: files.length,
        oldestFile: sortedByDate[0]?.metadata.timeCreated,
        newestFile: sortedByDate[sortedByDate.length - 1]?.metadata.timeCreated
      };
      
    } catch (error) {
      log.error('Failed to get PDF storage usage', 'PDF_STORAGE', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return { totalSize: 0, fileCount: 0 };
    }
  }

  /**
   * List all PDF files for a user
   */
  static async listUserPDFs(userId: string): Promise<Array<{
    name: string;
    fullPath: string;
    metadata: any;
  }>> {
    try {
      const storage = getFirebaseStorage();
      const { listAll } = await import('firebase/storage');
      const folderRef = ref(storage, `${this.PDF_PATH}/${userId}`);
      
      const listResult = await listAll(folderRef);
      const files = [];
      
      for (const itemRef of listResult.items) {
        if (itemRef.name.endsWith('.pdf')) {
          try {
            const metadata = await getMetadata(itemRef);
            files.push({
              name: itemRef.name,
              fullPath: itemRef.fullPath,
              metadata
            });
          } catch (metaError) {
            log.warn('Failed to get metadata for PDF file', 'PDF_STORAGE', {
              userId,
              fileName: itemRef.name,
              error: metaError instanceof Error ? metaError.message : 'Unknown error'
            });
          }
        }
      }
      
      return files;
      
    } catch (error) {
      log.error('Failed to list user PDFs', 'PDF_STORAGE', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return [];
    }
  }

  /**
   * Update checklist with PDF metadata (helper for integration)
   */
  static createPDFMetadata(cacheResult: PDFCacheResult) {
    return {
      url: cacheResult.url,
      path: cacheResult.path,
      generatedAt: cacheResult.generatedAt,
      contentHash: cacheResult.contentHash,
      isStale: false,
      size: cacheResult.size
    };
  }

  /**
   * Check if PDF metadata indicates stale content
   */
  static async isPDFStale(checklist: ChecklistData): Promise<boolean> {
    if (!checklist.pdfMetadata) {
      return true; // No PDF metadata means no cache
    }
    
    const currentHash = await this.generateContentHash(checklist);
    return checklist.pdfMetadata.contentHash !== currentHash || 
           checklist.pdfMetadata.isStale === true;
  }
} 
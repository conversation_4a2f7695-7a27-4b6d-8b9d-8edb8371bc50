import { 
  collection, 
  getDocs, 
  query, 
  orderBy, 
  doc,
  updateDoc,
  deleteDoc,
  getDoc,
  where,
  Timestamp,
  deleteField,
  documentId,
  limit,
  startAfter,
  QueryDocumentSnapshot
} from 'firebase/firestore';
import { db } from '@/config/firebase-persistence';
import { ChecklistData, SyncStatus } from '@/types/checklist';
import { EquipmentTag } from '@/types/equipment-tag';
import { FirebaseStorageService } from '../storage/firebase-storage-service';
import { log } from '../../utils/logger';

const CHECKLISTS_COLLECTION = 'checklists';
const EQUIPMENT_TAGS_COLLECTION = 'equipment-tags';
const USERS_COLLECTION = 'users';

export interface ChecklistWithUser extends ChecklistData {
  userDisplayName?: string;
  userEmail?: string;
  equipmentTag?: EquipmentTag;
}

export interface PaginatedResult<T> {
  items: T[];
  nextCursor?: QueryDocumentSnapshot;
  hasMore: boolean;
  total?: number;
}

export interface ChecklistStats {
  total: number;
  completed: number;
  pending: number;
}

export class OptimizedAdminChecklistService {
  private static readonly DEFAULT_PAGE_SIZE = 50;
  private static readonly MAX_BATCH_SIZE = 10; // Firestore 'in' query limit

  /**
   * Get checklists with pagination and optimized loading
   */
  static async getChecklistsPaginated(
    pageSize: number = this.DEFAULT_PAGE_SIZE,
    cursor?: QueryDocumentSnapshot
  ): Promise<PaginatedResult<ChecklistWithUser>> {
    try {
      // Build base query
      let q = query(
        collection(db, CHECKLISTS_COLLECTION),
        orderBy('createdAt', 'desc'),
        limit(pageSize + 1) // +1 to check if there are more items
      );

      // Add cursor for pagination
      if (cursor) {
        q = query(q, startAfter(cursor));
      }

      const querySnapshot = await getDocs(q);
      const docs = querySnapshot.docs;
      
      // Check if there are more items
      const hasMore = docs.length > pageSize;
      const items = hasMore ? docs.slice(0, pageSize) : docs;
      const nextCursor = hasMore ? docs[pageSize - 1] : undefined;

      // Extract unique user IDs and equipment tag IDs
      const userIds = new Set<string>();
      const equipmentTagIds = new Set<string>();
      
      items.forEach(doc => {
        const data = doc.data();
        if (data.userId) userIds.add(data.userId);
        if (data.equipmentTagId) equipmentTagIds.add(data.equipmentTagId);
      });

      // Batch load users and equipment tags
      const [usersMap, equipmentTagsMap] = await Promise.all([
        this.batchLoadUsers(Array.from(userIds)),
        this.batchLoadEquipmentTags(Array.from(equipmentTagIds))
      ]);

      // Build checklists with user and equipment tag data
      const checklists: ChecklistWithUser[] = items.map(docSnapshot => {
        const data = docSnapshot.data();
        
        // Convert Firestore timestamps
        const createdAt = data.createdAt instanceof Timestamp 
          ? data.createdAt.toDate().toISOString()
          : data.createdAt;
        const updatedAt = data.updatedAt instanceof Timestamp 
          ? data.updatedAt.toDate().toISOString()
          : data.updatedAt;

        // Get user data from map
        const userData = usersMap.get(data.userId);
        
        // Get equipment tag data from map
        const equipmentTag = equipmentTagsMap.get(data.equipmentTagId);

        return {
          ...data,
          id: docSnapshot.id,
          createdAt,
          updatedAt,
          userDisplayName: userData?.displayName,
          userEmail: userData?.email,
          equipmentTag,
          isCompleted: data.isCompleted || false,
          completedAt: data.completedAt,
          completedBy: data.completedBy,
        } as ChecklistWithUser;
      });

      return {
        items: checklists,
        nextCursor,
        hasMore
      };

    } catch (error) {
      console.error('Error fetching paginated checklists:', error);
      throw error;
    }
  }

  /**
   * Batch load users to avoid N+1 queries
   */
  private static async batchLoadUsers(userIds: string[]): Promise<Map<string, any>> {
    const usersMap = new Map();
    
    if (userIds.length === 0) return usersMap;

    // Process in batches due to Firestore 'in' query limit
    for (let i = 0; i < userIds.length; i += this.MAX_BATCH_SIZE) {
      const batch = userIds.slice(i, i + this.MAX_BATCH_SIZE);
      
      try {
        const userQuery = query(
          collection(db, USERS_COLLECTION),
          where(documentId(), 'in', batch)
        );
        
        const userSnapshot = await getDocs(userQuery);
        userSnapshot.forEach(doc => {
          usersMap.set(doc.id, doc.data());
        });
      } catch (error) {
        console.warn('Failed to load user batch:', error);
      }
    }

    return usersMap;
  }

  /**
   * Batch load equipment tags to avoid N+1 queries
   */
  private static async batchLoadEquipmentTags(equipmentTagIds: string[]): Promise<Map<string, EquipmentTag>> {
    const equipmentTagsMap = new Map();
    
    if (equipmentTagIds.length === 0) return equipmentTagsMap;

    // Process in batches due to Firestore 'in' query limit
    for (let i = 0; i < equipmentTagIds.length; i += this.MAX_BATCH_SIZE) {
      const batch = equipmentTagIds.slice(i, i + this.MAX_BATCH_SIZE);
      
      try {
        const tagQuery = query(
          collection(db, EQUIPMENT_TAGS_COLLECTION),
          where(documentId(), 'in', batch)
        );
        
        const tagSnapshot = await getDocs(tagQuery);
        tagSnapshot.forEach(doc => {
          const tagData = doc.data();
          // Convert Firestore timestamps
          const tagCreatedAt = tagData.createdAt instanceof Timestamp 
            ? tagData.createdAt.toDate().toISOString()
            : tagData.createdAt;
          const tagUpdatedAt = tagData.updatedAt instanceof Timestamp 
            ? tagData.updatedAt.toDate().toISOString()
            : tagData.updatedAt;
          
          equipmentTagsMap.set(doc.id, {
            ...tagData,
            id: doc.id,
            createdAt: tagCreatedAt,
            updatedAt: tagUpdatedAt,
          } as EquipmentTag);
        });
      } catch (error) {
        console.warn('Failed to load equipment tag batch:', error);
      }
    }

    return equipmentTagsMap;
  }

  /**
   * Get optimized checklist stats without loading all data
   */
  static async getOptimizedChecklistStats(): Promise<ChecklistStats> {
    try {
      // Use aggregation queries for better performance
      const [totalQuery, completedQuery] = await Promise.all([
        getDocs(collection(db, CHECKLISTS_COLLECTION)),
        getDocs(query(collection(db, CHECKLISTS_COLLECTION), where('isCompleted', '==', true)))
      ]);

      const total = totalQuery.size;
      const completed = completedQuery.size;
      const pending = total - completed;

      return { total, completed, pending };
    } catch (error) {
      console.error('Error getting optimized checklist stats:', error);
      // Fallback to basic stats
      return { total: 0, completed: 0, pending: 0 };
    }
  }

  /**
   * Search checklists with optimized filtering
   */
  static async searchChecklistsOptimized(
    criteria: {
      searchTerm?: string;
      statusFilter?: 'all' | 'completed' | 'pending';
      buildingFilter?: string;
      locationFilter?: string;
      clientFilter?: string;
    },
    pageSize: number = this.DEFAULT_PAGE_SIZE,
    cursor?: QueryDocumentSnapshot
  ): Promise<PaginatedResult<ChecklistWithUser>> {
    try {
      // Build query with server-side filters where possible
      let q = query(collection(db, CHECKLISTS_COLLECTION), orderBy('createdAt', 'desc'));

      // Apply server-side filters
      if (criteria.statusFilter === 'completed') {
        q = query(q, where('isCompleted', '==', true));
      } else if (criteria.statusFilter === 'pending') {
        q = query(q, where('isCompleted', '==', false));
      }

      // Add pagination
      if (cursor) {
        q = query(q, startAfter(cursor));
      }
      q = query(q, limit(pageSize + 1));

      const querySnapshot = await getDocs(q);
      const docs = querySnapshot.docs;

      // Check if there are more items
      const hasMore = docs.length > pageSize;
      const items = hasMore ? docs.slice(0, pageSize) : docs;
      const nextCursor = hasMore ? docs[pageSize - 1] : undefined;

      // Client-side filtering for complex criteria
      let filteredItems = items;
      if (criteria.searchTerm || criteria.buildingFilter !== 'all' || 
          criteria.locationFilter !== 'all' || criteria.clientFilter !== 'all') {
        
        // Load related data for filtering
        const equipmentTagIds = items.map(doc => doc.data().equipmentTagId).filter(Boolean);
        const equipmentTagsMap = await this.batchLoadEquipmentTags(equipmentTagIds);

        filteredItems = items.filter(docSnapshot => {
          const data = docSnapshot.data();
          const equipmentTag = equipmentTagsMap.get(data.equipmentTagId);

          // Apply client-side filters
          if (criteria.searchTerm) {
            const searchLower = criteria.searchTerm.toLowerCase();
            const matchesSearch = 
              data.generalInfo?.tagNo?.toLowerCase().includes(searchLower) ||
              data.generalInfo?.clientName?.toLowerCase().includes(searchLower) ||
              equipmentTag?.tagNumber?.toLowerCase().includes(searchLower) ||
              equipmentTag?.equipmentName?.toLowerCase().includes(searchLower);
            
            if (!matchesSearch) return false;
          }

          if (criteria.buildingFilter !== 'all' && equipmentTag?.building !== criteria.buildingFilter) {
            return false;
          }

          if (criteria.locationFilter !== 'all' && equipmentTag?.location !== criteria.locationFilter) {
            return false;
          }

          if (criteria.clientFilter !== 'all' && equipmentTag?.clientName !== criteria.clientFilter) {
            return false;
          }

          return true;
        });
      }

      // Build final results with user and equipment tag data
      const userIds = filteredItems.map(doc => doc.data().userId).filter(Boolean);
      const equipmentTagIds = filteredItems.map(doc => doc.data().equipmentTagId).filter(Boolean);

      const [usersMap, equipmentTagsMap] = await Promise.all([
        this.batchLoadUsers(userIds),
        this.batchLoadEquipmentTags(equipmentTagIds)
      ]);

      const checklists: ChecklistWithUser[] = filteredItems.map(docSnapshot => {
        const data = docSnapshot.data();
        
        const createdAt = data.createdAt instanceof Timestamp 
          ? data.createdAt.toDate().toISOString()
          : data.createdAt;
        const updatedAt = data.updatedAt instanceof Timestamp 
          ? data.updatedAt.toDate().toISOString()
          : data.updatedAt;

        const userData = usersMap.get(data.userId);
        const equipmentTag = equipmentTagsMap.get(data.equipmentTagId);

        return {
          ...data,
          id: docSnapshot.id,
          createdAt,
          updatedAt,
          userDisplayName: userData?.displayName,
          userEmail: userData?.email,
          equipmentTag,
          isCompleted: data.isCompleted || false,
          completedAt: data.completedAt,
          completedBy: data.completedBy,
        } as ChecklistWithUser;
      });

      return {
        items: checklists,
        nextCursor: filteredItems.length === items.length ? nextCursor : undefined,
        hasMore: filteredItems.length === items.length ? hasMore : false
      };

    } catch (error) {
      console.error('Error searching checklists:', error);
      throw error;
    }
  }
} 
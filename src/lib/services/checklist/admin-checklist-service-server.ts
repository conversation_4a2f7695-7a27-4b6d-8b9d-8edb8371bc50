import * as admin from 'firebase-admin';
import { ChecklistData } from '@/types/checklist';
import { EquipmentTag } from '@/types/equipment-tag';
import { log } from '../../utils/logger';

const CHECKLISTS_COLLECTION = 'checklists';
const EQUIPMENT_TAGS_COLLECTION = 'equipment-tags';
const USERS_COLLECTION = 'users';

export interface ChecklistWithUser extends ChecklistData {
  userDisplayName?: string;
  userEmail?: string;
  equipmentTag?: EquipmentTag;
}

/**
 * Server-side AdminChecklistService using Firebase Admin SDK
 * This bypasses Firestore security rules and has full database access
 */
export class AdminChecklistServiceServer {
  private static get db() {
    return admin.firestore();
  }

  /**
   * Get a specific checklist by ID (server-side with admin privileges)
   */
  static async getChecklistById(checklistId: string): Promise<ChecklistWithUser | null> {
    try {
      const docRef = this.db.collection(CHECKLISTS_COLLECTION).doc(checklistId);
      const docSnap = await docRef.get();
      
      if (!docSnap.exists) {
        return null;
      }

      const data = docSnap.data()!;
      
      // Convert Firestore timestamps
      const createdAt = data.createdAt?.toDate?.() 
        ? data.createdAt.toDate().toISOString()
        : data.createdAt;
      const updatedAt = data.updatedAt?.toDate?.() 
        ? data.updatedAt.toDate().toISOString()
        : data.updatedAt;

      // Get user information
      let userDisplayName: string | undefined;
      let userEmail: string | undefined;
      
      if (data.userId) {
        try {
          const userDoc = await this.db.collection(USERS_COLLECTION).doc(data.userId).get();
          if (userDoc.exists) {
            const userData = userDoc.data()!;
            userDisplayName = userData.displayName;
            userEmail = userData.email;
          }
        } catch (error) {
          log.warn('Failed to fetch user data', 'ADMIN_SERVER', {
            userId: data.userId,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      // Get equipment tag information
      let equipmentTag: EquipmentTag | undefined;
      if (data.equipmentTagId) {
        try {
          const tagDoc = await this.db.collection(EQUIPMENT_TAGS_COLLECTION).doc(data.equipmentTagId).get();
          if (tagDoc.exists) {
            const tagData = tagDoc.data()!;
            // Convert Firestore timestamps for equipment tag
            const tagCreatedAt = tagData.createdAt?.toDate?.() 
              ? tagData.createdAt.toDate().toISOString()
              : tagData.createdAt;
            const tagUpdatedAt = tagData.updatedAt?.toDate?.() 
              ? tagData.updatedAt.toDate().toISOString()
              : tagData.updatedAt;
            
            equipmentTag = {
              ...tagData,
              id: tagDoc.id,
              createdAt: tagCreatedAt,
              updatedAt: tagUpdatedAt,
            } as EquipmentTag;
          }
        } catch (error) {
          log.warn('Failed to fetch equipment tag data', 'ADMIN_SERVER', {
            equipmentTagId: data.equipmentTagId,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      return {
        ...data,
        id: docSnap.id,
        createdAt,
        updatedAt,
        userDisplayName,
        userEmail,
        equipmentTag,
        isCompleted: data.isCompleted || false,
        completedAt: data.completedAt,
        completedBy: data.completedBy,
      } as ChecklistWithUser;
    } catch (error) {
      log.error('Error fetching checklist with admin SDK', 'ADMIN_SERVER', {
        checklistId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get multiple checklists by IDs (server-side with admin privileges)
   */
  static async getChecklistsByIds(checklistIds: string[]): Promise<ChecklistWithUser[]> {
    try {
      if (checklistIds.length === 0) {
        return [];
      }

      // Batch get checklists
      const checklistPromises = checklistIds.map(id => 
        this.db.collection(CHECKLISTS_COLLECTION).doc(id).get()
      );
      
      const checklistDocs = await Promise.all(checklistPromises);
      const validChecklists: ChecklistWithUser[] = [];

      // Collect user IDs and equipment tag IDs for batch fetching
      const userIds = new Set<string>();
      const equipmentTagIds = new Set<string>();

      const checklistsData = checklistDocs
        .filter(doc => doc.exists)
        .map(doc => {
          const data = doc.data()!;
          if (data.userId) userIds.add(data.userId);
          if (data.equipmentTagId) equipmentTagIds.add(data.equipmentTagId);
          return { id: doc.id, data };
        });

      // Batch fetch users
      const usersMap = new Map<string, any>();
      if (userIds.size > 0) {
        const userPromises = Array.from(userIds).map(userId =>
          this.db.collection(USERS_COLLECTION).doc(userId).get()
        );
        const userDocs = await Promise.all(userPromises);
        userDocs.forEach(doc => {
          if (doc.exists) {
            usersMap.set(doc.id, doc.data());
          }
        });
      }

      // Batch fetch equipment tags
      const equipmentTagsMap = new Map<string, EquipmentTag>();
      if (equipmentTagIds.size > 0) {
        const tagPromises = Array.from(equipmentTagIds).map(tagId =>
          this.db.collection(EQUIPMENT_TAGS_COLLECTION).doc(tagId).get()
        );
        const tagDocs = await Promise.all(tagPromises);
        tagDocs.forEach(doc => {
          if (doc.exists) {
            const tagData = doc.data()!;
            const tagCreatedAt = tagData.createdAt?.toDate?.() 
              ? tagData.createdAt.toDate().toISOString()
              : tagData.createdAt;
            const tagUpdatedAt = tagData.updatedAt?.toDate?.() 
              ? tagData.updatedAt.toDate().toISOString()
              : tagData.updatedAt;
            
            equipmentTagsMap.set(doc.id, {
              ...tagData,
              id: doc.id,
              createdAt: tagCreatedAt,
              updatedAt: tagUpdatedAt,
            } as EquipmentTag);
          }
        });
      }

      // Build final checklist objects
      for (const { id, data } of checklistsData) {
        // Convert Firestore timestamps
        const createdAt = data.createdAt?.toDate?.() 
          ? data.createdAt.toDate().toISOString()
          : data.createdAt;
        const updatedAt = data.updatedAt?.toDate?.() 
          ? data.updatedAt.toDate().toISOString()
          : data.updatedAt;

        // Get user data from map
        const userData = usersMap.get(data.userId);
        
        // Get equipment tag data from map
        const equipmentTag = equipmentTagsMap.get(data.equipmentTagId);

        validChecklists.push({
          ...data,
          id,
          createdAt,
          updatedAt,
          userDisplayName: userData?.displayName,
          userEmail: userData?.email,
          equipmentTag,
          isCompleted: data.isCompleted || false,
          completedAt: data.completedAt,
          completedBy: data.completedBy,
        } as ChecklistWithUser);
      }

      return validChecklists;
    } catch (error) {
      log.error('Error fetching checklists by IDs with admin SDK', 'ADMIN_SERVER', {
        checklistCount: checklistIds.length,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  /**
   * Get all checklists (server-side with admin privileges)
   */
  static async getAllChecklists(): Promise<ChecklistWithUser[]> {
    try {
      const checklistsRef = this.db.collection(CHECKLISTS_COLLECTION);
      const querySnapshot = await checklistsRef.orderBy('createdAt', 'desc').get();
      
      const checklists: ChecklistWithUser[] = [];

      for (const docSnapshot of querySnapshot.docs) {
        const data = docSnapshot.data();
        
        // Convert Firestore timestamps
        const createdAt = data.createdAt?.toDate?.() 
          ? data.createdAt.toDate().toISOString()
          : data.createdAt;
        const updatedAt = data.updatedAt?.toDate?.() 
          ? data.updatedAt.toDate().toISOString()
          : data.updatedAt;

        // Get user information
        let userDisplayName: string | undefined;
        let userEmail: string | undefined;
        
        if (data.userId) {
          try {
            const userDoc = await this.db.collection(USERS_COLLECTION).doc(data.userId).get();
            if (userDoc.exists) {
              const userData = userDoc.data()!;
              userDisplayName = userData.displayName;
              userEmail = userData.email;
            }
          } catch (error) {
            log.warn('Failed to fetch user data', 'ADMIN_SERVER', {
              userId: data.userId,
              error: error instanceof Error ? error.message : 'Unknown error'
            });
          }
        }

        // Get equipment tag information
        let equipmentTag: EquipmentTag | undefined;
        if (data.equipmentTagId) {
          try {
            const tagDoc = await this.db.collection(EQUIPMENT_TAGS_COLLECTION).doc(data.equipmentTagId).get();
            if (tagDoc.exists) {
              const tagData = tagDoc.data()!;
              const tagCreatedAt = tagData.createdAt?.toDate?.() 
                ? tagData.createdAt.toDate().toISOString()
                : tagData.createdAt;
              const tagUpdatedAt = tagData.updatedAt?.toDate?.() 
                ? tagData.updatedAt.toDate().toISOString()
                : tagData.updatedAt;
              
              equipmentTag = {
                ...tagData,
                id: tagDoc.id,
                createdAt: tagCreatedAt,
                updatedAt: tagUpdatedAt,
              } as EquipmentTag;
            }
          } catch (error) {
            log.warn('Failed to fetch equipment tag data', 'ADMIN_SERVER', {
              equipmentTagId: data.equipmentTagId,
              error: error instanceof Error ? error.message : 'Unknown error'
            });
          }
        }

        checklists.push({
          ...data,
          id: docSnapshot.id,
          createdAt,
          updatedAt,
          userDisplayName,
          userEmail,
          equipmentTag,
          isCompleted: data.isCompleted || false,
          completedAt: data.completedAt,
          completedBy: data.completedBy,
        } as ChecklistWithUser);
      }

      return checklists;
    } catch (error) {
      log.error('Error fetching all checklists with admin SDK', 'ADMIN_SERVER', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
} 
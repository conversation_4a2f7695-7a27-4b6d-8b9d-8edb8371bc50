import { initializeApp } from "firebase/app";
import { getAI, getGenerativeModel, GoogleAIBackend } from "firebase/ai";
import { ChecklistFormData } from "../../utils/validation";
import { AI_CONFIG, isFeatureEnabled, getErrorMessage } from "@/config/ai-config";

// Firebase configuration - using existing config
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
  measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
};

// Initialize Firebase AI
let ai: any = null;
let model: any = null;

const initializeAI = () => {
  if (!ai) {
    const firebaseApp = initializeApp(firebaseConfig, 'ai-app');
    ai = getAI(firebaseApp, { backend: new GoogleAIBackend() });
    model = getGenerativeModel(ai, { model: AI_CONFIG.model.name });
  }
  return { ai, model };
};

export interface RemarkGenerationOptions {
  tone?: 'professional' | 'technical' | 'detailed';
  includeRecommendations?: boolean;
  focusAreas?: string[];
  maxLength?: number; // Maximum number of characters (not words)
}

export class AIService {
  private static instance: AIService;
  private model: any;
  private requestCount: { minute: number; hour: number; lastReset: number } = {
    minute: 0,
    hour: 0,
    lastReset: Date.now()
  };

  private constructor() {
    const { model: aiModel } = initializeAI();
    this.model = aiModel;
  }

  public static getInstance(): AIService {
    if (!AIService.instance) {
      AIService.instance = new AIService();
    }
    return AIService.instance;
  }

  private checkRateLimit(): boolean {
    const now = Date.now();
    const timeDiff = now - this.requestCount.lastReset;
    
    // Reset counters every minute
    if (timeDiff >= 60000) {
      this.requestCount.minute = 0;
      this.requestCount.lastReset = now;
    }
    
    // Reset hour counter every hour
    if (timeDiff >= 3600000) {
      this.requestCount.hour = 0;
    }
    
    return (
      this.requestCount.minute < AI_CONFIG.rateLimits.requestsPerMinute &&
      this.requestCount.hour < AI_CONFIG.rateLimits.requestsPerHour
    );
  }

  private incrementRequestCount(): void {
    this.requestCount.minute++;
    this.requestCount.hour++;
  }

  /**
   * Generate professional remarks based on checklist data
   */
  async generateRemarks(
    checklistData: Partial<ChecklistFormData>,
    currentRemarks: string = '',
    options: RemarkGenerationOptions = {}
  ): Promise<string> {
    if (!isFeatureEnabled('remarkGeneration')) {
      throw new Error('Remark generation feature is currently disabled.');
    }

    if (!this.checkRateLimit()) {
      throw new Error(getErrorMessage('rateLimitExceeded'));
    }

    try {
      const {
        tone = AI_CONFIG.defaultOptions.tone,
        includeRecommendations = AI_CONFIG.defaultOptions.includeRecommendations,
        focusAreas = [],
        maxLength = AI_CONFIG.defaultOptions.maxLength
      } = options;

      const prompt = this.buildRemarkPrompt(checklistData, currentRemarks, {
        tone,
        includeRecommendations,
        focusAreas,
        maxLength
      });

      this.incrementRequestCount();
      const result = await this.model.generateContent(prompt);
      const response = result.response;
      const generatedText = response.text();

      return this.postProcessRemarks(generatedText.trim(), maxLength);
    } catch (error) {
      if (error instanceof Error) {
        if (error.message.includes('network') || error.message.includes('fetch')) {
          throw new Error(getErrorMessage('networkError'));
        }
        if (error.message.includes('quota') || error.message.includes('limit')) {
          throw new Error(getErrorMessage('rateLimitExceeded'));
        }
      }
      throw new Error(getErrorMessage('generic'));
    }
  }

  /**
   * Enhance existing remarks with AI suggestions
   */
  async enhanceRemarks(
    currentRemarks: string,
    checklistData: Partial<ChecklistFormData>
  ): Promise<string> {
    if (!isFeatureEnabled('remarkEnhancement')) {
      throw new Error('Remark enhancement feature is currently disabled.');
    }

    if (!this.checkRateLimit()) {
      throw new Error(getErrorMessage('rateLimitExceeded'));
    }

    try {
      const prompt = `You are a senior ACMV technical writer specializing in inspection report enhancement.

TASK: Enhance the following inspection remarks while maintaining their original meaning and technical accuracy.

CRITICAL REQUIREMENTS:
- Output must be EXACTLY ${AI_CONFIG.defaultOptions.maxLength} characters or less (including all spaces and punctuation)
- Write in professional, polished language suitable for official reports
- Maintain all original observations and findings
- Use proper ACMV technical terminology
- Create beautifully structured, clear, and concise remarks

ORIGINAL REMARKS: "${currentRemarks}"

INSPECTION CONTEXT:
${this.formatChecklistContext(checklistData)}

Enhance these remarks to be more professional and well-structured within the ${AI_CONFIG.defaultOptions.maxLength}-character limit. Output only the enhanced remark with no prefixes, explanations, or additional text.`;

      this.incrementRequestCount();
      const result = await this.model.generateContent(prompt);
      const response = result.response;
      return this.postProcessRemarks(response.text().trim(), AI_CONFIG.defaultOptions.maxLength);
    } catch (error) {
      throw new Error(getErrorMessage('generic'));
    }
  }

  /**
   * Generate summary insights from checklist data
   */
  async generateSummaryInsights(checklistData: ChecklistFormData): Promise<string> {
    if (!isFeatureEnabled('summaryInsights')) {
      throw new Error('Summary insights feature is currently disabled.');
    }

    if (!this.checkRateLimit()) {
      throw new Error(getErrorMessage('rateLimitExceeded'));
    }

    try {
      const prompt = `You are a senior ACMV systems analyst providing strategic insights for maintenance management.

TASK: Analyze the inspection data and provide actionable insights and recommendations.

CRITICAL REQUIREMENTS:
- Output must be EXACTLY ${AI_CONFIG.defaultOptions.maxLength} characters or less (including all spaces and punctuation)
- Write in executive summary style for management review
- Focus on system health, critical issues, and priority actions
- Use professional, authoritative language
- Structure insights clearly and concisely

INSPECTION DATA:
${this.formatChecklistContext(checklistData)}

Provide a comprehensive yet concise analysis covering system health, critical findings, and recommended actions within the ${AI_CONFIG.defaultOptions.maxLength}-character limit. Output only the analysis with no prefixes, explanations, or additional text.`;

      this.incrementRequestCount();
      const result = await this.model.generateContent(prompt);
      const response = result.response;
      return this.postProcessRemarks(response.text().trim(), AI_CONFIG.defaultOptions.maxLength);
    } catch (error) {
      throw new Error(getErrorMessage('generic'));
    }
  }

  private buildRemarkPrompt(
    checklistData: Partial<ChecklistFormData>,
    currentRemarks: string,
    options: RemarkGenerationOptions
  ): string {
    const { tone, includeRecommendations, focusAreas = [], maxLength } = options;

    const prompt = `You are a senior ACMV inspection engineer writing professional inspection remarks for official reports.

CRITICAL REQUIREMENTS:
- Output must be EXACTLY ${maxLength} characters or less (including all spaces and punctuation)
- Write in a ${tone}, structured, and beautifully formatted style
- Create clean, concise, and technically accurate remarks
- Use proper grammar and professional language
- ${includeRecommendations ? 'Include actionable recommendations when needed' : 'Focus purely on observations and findings'}

EQUIPMENT DETAILS:
Equipment: ${checklistData.generalInfo?.equipmentName || 'Unknown'} (Tag: ${checklistData.generalInfo?.tagNo || 'N/A'})
Location: ${checklistData.generalInfo?.location || 'N/A'}

INSPECTION FINDINGS:
${this.formatChecklistContext(checklistData)}

${focusAreas && focusAreas.length > 0 ? `FOCUS AREAS: ${focusAreas.join(', ')}` : ''}

Write a polished inspection remark that maintenance professionals will read. Make it structured, clear, and informative within the ${maxLength}-character limit. Output only the final remark with no prefixes, explanations, or additional text.`;

    return prompt;
  }

  private formatChecklistContext(checklistData: Partial<ChecklistFormData>): string {
    let context = '';
    let hasIssues = false;
    let okCount = 0;
    let issueCount = 0;

    // Mechanical Checks
    if (checklistData.mechanicalChecks) {
      const mechanicalIssues: string[] = [];
      Object.entries(checklistData.mechanicalChecks).forEach(([key, value]) => {
        // Skip numeric values (measurements)
        if (typeof value === 'number') return;
        
        if (value === 'OK') {
          okCount++;
        } else if (value === 'Faulty' || value === 'N/A' || value === 'Missing') {
          mechanicalIssues.push(`${this.formatFieldName(key)}: ${value}`);
          issueCount++;
          hasIssues = true;
        }
      });
      
      if (mechanicalIssues.length > 0) {
        context += '\nMechanical Issues:\n';
        mechanicalIssues.forEach(issue => context += `- ${issue}\n`);
      }
    }

    // Electrical Checks
    if (checklistData.electricalChecks) {
      const electricalIssues: string[] = [];
      Object.entries(checklistData.electricalChecks).forEach(([key, value]) => {
        // Skip numeric values (measurements)
        if (typeof value === 'number') return;
        
        if (value === 'OK') {
          okCount++;
        } else if (value === 'Faulty' || value === 'N/A' || value === 'Missing') {
          electricalIssues.push(`${this.formatFieldName(key)}: ${value}`);
          issueCount++;
          hasIssues = true;
        }
      });
      
      if (electricalIssues.length > 0) {
        context += '\nElectrical Issues:\n';
        electricalIssues.forEach(issue => context += `- ${issue}\n`);
      }
    }

    // Sequence Controls Checks
    if (checklistData.sequenceControlsChecks) {
      const sequenceIssues: string[] = [];
      Object.entries(checklistData.sequenceControlsChecks).forEach(([key, value]) => {
        // All sequence control values are strings, but let's be safe
        if (typeof value === 'number') return;
        
        if (value === 'OK') {
          okCount++;
        } else if (value === 'Faulty' || value === 'N/A' || value === 'Missing') {
          sequenceIssues.push(`${this.formatFieldName(key)}: ${value}`);
          issueCount++;
          hasIssues = true;
        }
      });
      
      if (sequenceIssues.length > 0) {
        context += '\nSequence Controls Issues:\n';
        sequenceIssues.forEach(issue => context += `- ${issue}\n`);
      }
    }

    // Summary
    const summary = `\nINSPECTION SUMMARY: ${okCount} items OK, ${issueCount} issues found.`;
    
    if (hasIssues) {
      return summary + context;
    } else {
      return summary + '\nAll systems operational - no issues detected.';
    }
  }

  private formatFieldName(fieldName: string): string {
    return fieldName
      .replace(/([A-Z])/g, ' $1')
      .replace(/^./, str => str.toUpperCase())
      .trim();
  }

  private postProcessRemarks(text: string, maxLength: number): string {
    // Minimal post-processing - let AI handle most formatting
    let processed = text
      .replace(/^["']|["']$/g, '') // Remove surrounding quotes if any
      .replace(/\n+/g, ' ') // Replace newlines with spaces
      .trim();

    // Only provide fallback if response is clearly invalid or conversational
    if (processed.toLowerCase().includes('give me') || 
        processed.toLowerCase().includes('i need') ||
        processed.length < 15) {
      processed = 'System inspection completed. All major components operational and functioning within normal parameters.';
    }

    // Final safety check for character limit
    if (processed.length > maxLength) {
      const truncated = processed.substring(0, maxLength - 3);
      const lastSpace = truncated.lastIndexOf(' ');
      processed = lastSpace > maxLength * 0.8 ? truncated.substring(0, lastSpace) + '...' : truncated + '...';
    }

    return processed;
  }
}

// Export singleton instance
export const aiService = AIService.getInstance(); 
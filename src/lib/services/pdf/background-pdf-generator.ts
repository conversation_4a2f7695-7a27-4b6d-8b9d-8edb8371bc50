import { ChecklistData } from '@/types/checklist';
import { PDFStorageService } from '../storage/pdf-storage-service';
import { log } from '../../utils/logger';
import { CompanyAssetsService } from '../assets/company-assets-service';

export interface PDFGenerationTask {
  checklist: ChecklistData;
  priority: 'high' | 'normal' | 'low';
  retryCount: number;
  scheduledAt: number;
}

export class BackgroundPDFGenerator {
  private static queue: PDFGenerationTask[] = [];
  private static processing = false;
  private static readonly MAX_RETRIES = 3;
  private static readonly RETRY_DELAY = 5000; // 5 seconds
  private static readonly BATCH_SIZE = 3; // Process 3 PDFs at a time
  private static readonly RATE_LIMIT_DELAY = 2000; // 2 seconds between batches
  
  /**
   * Add checklist to PDF generation queue
   */
  static async queueGeneration(
    checklist: ChecklistData, 
    priority: 'high' | 'normal' | 'low' = 'normal'
  ): Promise<void> {
    if (!checklist.userId) {
      log.debug('Skipping PDF generation - no user ID', 'PDF_GENERATOR', {
        checklistId: checklist.id
      });
      return;
    }

    try {
      // Check if PDF needs regeneration
      const isStale = await PDFStorageService.isPDFStale(checklist);
      if (!isStale) {
        log.debug('PDF is current, skipping generation', 'PDF_GENERATOR', {
          checklistId: checklist.id
        });
        return;
      }

      // Remove any existing tasks for this checklist
      this.queue = this.queue.filter(task => task.checklist.id !== checklist.id);

      // Add new task
      const task: PDFGenerationTask = {
        checklist,
        priority,
        retryCount: 0,
        scheduledAt: Date.now()
      };

      // Insert based on priority
      if (priority === 'high') {
        this.queue.unshift(task);
      } else {
        this.queue.push(task);
      }

      log.debug('PDF generation task queued', 'PDF_GENERATOR', {
        checklistId: checklist.id,
        priority,
        queueLength: this.queue.length
      });

      // Start processing if not already running
      this.processQueue();

    } catch (error) {
      log.error('Failed to queue PDF generation', 'PDF_GENERATOR', {
        checklistId: checklist.id,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Process the PDF generation queue
   */
  private static async processQueue(): Promise<void> {
    if (this.processing || this.queue.length === 0) {
      return;
    }

    this.processing = true;
    log.debug('Starting PDF generation queue processing', 'PDF_GENERATOR', {
      queueLength: this.queue.length
    });

    try {
      while (this.queue.length > 0) {
        // Process batch
        const batch = this.queue.splice(0, this.BATCH_SIZE);
        
        log.debug('Processing PDF generation batch', 'PDF_GENERATOR', {
          batchSize: batch.length,
          remaining: this.queue.length
        });

        // Process tasks in parallel within the batch
        const batchPromises = batch.map(task => this.processTask(task));
        const results = await Promise.allSettled(batchPromises);

        // Handle failed tasks
        results.forEach((result, index) => {
          if (result.status === 'rejected') {
            const task = batch[index];
            this.handleTaskFailure(task, result.reason);
          }
        });

        // Rate limiting - wait between batches
        if (this.queue.length > 0) {
          await new Promise(resolve => setTimeout(resolve, this.RATE_LIMIT_DELAY));
        }
      }

    } catch (error) {
      log.error('PDF generation queue processing failed', 'PDF_GENERATOR', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } finally {
      this.processing = false;
      log.debug('PDF generation queue processing completed', 'PDF_GENERATOR');
    }
  }

  /**
   * Process a single PDF generation task
   */
  private static async processTask(task: PDFGenerationTask): Promise<void> {
    const { checklist } = task;
    
    try {
      log.debug('Processing PDF generation task', 'PDF_GENERATOR', {
        checklistId: checklist.id,
        attempt: task.retryCount + 1
      });

      // Check if running in browser environment
      if (typeof window === 'undefined') {
        // Server-side generation
        await this.generatePDFServerSide(checklist);
      } else {
        // Client-side generation (background)
        await this.generatePDFClientSide(checklist);
      }

      log.info('PDF generated successfully in background', 'PDF_GENERATOR', {
        checklistId: checklist.id,
        userId: checklist.userId,
        attempts: task.retryCount + 1
      });

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      log.error('PDF generation task failed', 'PDF_GENERATOR', {
        checklistId: checklist.id,
        attempt: task.retryCount + 1,
        error: errorMessage
      });
      throw error;
    }
  }

  /**
   * Generate PDF on server-side (fallback to client-side)
   */
  private static async generatePDFServerSide(checklist: ChecklistData): Promise<void> {
    // SSR PDF generation has been removed - fallback to client-side generation
    await this.generatePDFClientSide(checklist);
  }

  /**
   * Generate PDF on client-side in background
   */
  private static async generatePDFClientSide(checklist: ChecklistData): Promise<void> {
    try {
      // This is a simplified version - in production you might want to use
      // a more sophisticated approach like Web Workers or offscreen canvas
      
      // Import required modules
      const [
        React,
        { createRoot },
        { PDFTemplate },
        html2canvas,
        { jsPDF }
      ] = await Promise.all([
        import('react'),
        import('react-dom/client'),
        import('@/components/pdf-template'),
        import('html2canvas'),
        import('jspdf')
      ]);

      // Create hidden container
      const container = document.createElement('div');
      container.style.position = 'absolute';
      container.style.left = '-10000px';
      container.style.top = '-10000px';
      container.style.width = '210mm';
      container.style.backgroundColor = '#ffffff';
      container.style.fontFamily = 'Arial, sans-serif';
      container.style.visibility = 'hidden';
      document.body.appendChild(container);

      try {
        // Load company assets and checklist images in high quality
        const [logoDataUrl, companySignatureDataUrl, imageDataUrls] = await Promise.all([
          CompanyAssetsService.loadLogoAsHighQualityDataUrl(),
          CompanyAssetsService.loadSignatureAsHighQualityDataUrl(),
          this.loadChecklistImages(checklist)
        ]);

        // Render PDF template with company signature
        const root = createRoot(container);
        
        await new Promise<void>((resolve) => {
          root.render(React.createElement(PDFTemplate, {
            checklist,
            logoDataUrl,
            beforeImageDataUrl: imageDataUrls.beforeImageDataUrl,
            afterImageDataUrl: imageDataUrls.afterImageDataUrl,
            signatureDataUrl: companySignatureDataUrl // Use company signature
          }));
          
          // Wait for rendering
          setTimeout(resolve, 2000);
        });

        // Generate canvas
        const canvas = await html2canvas.default(container, {
          scale: 1.0,
          useCORS: true,
          logging: false,
          backgroundColor: '#ffffff',
          allowTaint: false,
          removeContainer: false,
          width: container.scrollWidth,
          height: container.scrollHeight,
          foreignObjectRendering: false,
          imageTimeout: 10000
        });

        // Create PDF with compression
        const pdf = new jsPDF({
          orientation: 'portrait',
          unit: 'mm',
          format: 'a4',
          compress: true
        });

        const pageWidth = 210;
        const pageHeight = 297;
        const canvasWidth = canvas.width;
        const canvasHeight = canvas.height;
        const ratio = pageWidth / canvasWidth;
        const scaledHeight = canvasHeight * ratio;

        if (scaledHeight <= pageHeight) {
          const imageData = canvas.toDataURL('image/jpeg', 0.85);
          pdf.addImage(imageData, 'JPEG', 0, 0, pageWidth, scaledHeight);
        } else {
          // Multi-page handling
          const pageCanvasHeight = pageHeight / ratio;
          let yPosition = 0;
          let pageNumber = 1;

          while (yPosition < canvasHeight) {
            const pageCanvas = document.createElement('canvas');
            const pageCtx = pageCanvas.getContext('2d')!;
            
            pageCanvas.width = canvasWidth;
            pageCanvas.height = Math.min(pageCanvasHeight, canvasHeight - yPosition);
            
            pageCtx.fillStyle = '#ffffff';
            pageCtx.fillRect(0, 0, pageCanvas.width, pageCanvas.height);
            pageCtx.drawImage(canvas, 0, yPosition, canvasWidth, pageCanvas.height, 0, 0, canvasWidth, pageCanvas.height);

            const pageImageData = pageCanvas.toDataURL('image/jpeg', 0.85);
            
            if (pageNumber > 1) pdf.addPage();
            pdf.addImage(pageImageData, 'JPEG', 0, 0, pageWidth, pageCanvas.height * ratio);

            yPosition += pageCanvasHeight;
            pageNumber++;
          }
        }

        // Get PDF bytes and cache
        const pdfBytes = new Uint8Array(pdf.output('arraybuffer'));
        const contentHash = await PDFStorageService.generateContentHash(checklist);
        
        await PDFStorageService.savePDF(
          pdfBytes,
          checklist.userId!,
          checklist.id,
          contentHash
        );

        // Cleanup
        root.unmount();

      } finally {
        if (document.body.contains(container)) {
          document.body.removeChild(container);
        }
      }

    } catch (error) {
      throw new Error(`Client-side PDF generation failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  /**
   * Handle task failure and retry logic
   */
  private static handleTaskFailure(task: PDFGenerationTask, error: any): void {
    task.retryCount++;
    
    if (task.retryCount < this.MAX_RETRIES) {
      // Schedule retry
      task.scheduledAt = Date.now() + this.RETRY_DELAY * task.retryCount;
      
      // Re-add to queue with lower priority
      this.queue.push({
        ...task,
        priority: 'low'
      });
      
      log.warn('PDF generation task failed, scheduling retry', 'PDF_GENERATOR', {
        checklistId: task.checklist.id,
        attempt: task.retryCount,
        nextRetryIn: this.RETRY_DELAY * task.retryCount,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    } else {
      log.error('PDF generation task failed permanently', 'PDF_GENERATOR', {
        checklistId: task.checklist.id,
        totalAttempts: task.retryCount,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    }
  }

  /**
   * Get queue status for monitoring
   */
  static getQueueStatus(): {
    queueLength: number;
    processing: boolean;
    nextTask?: { checklistId: string; priority: string; scheduledAt: number };
  } {
    return {
      queueLength: this.queue.length,
      processing: this.processing,
      nextTask: this.queue[0] ? {
        checklistId: this.queue[0].checklist.id,
        priority: this.queue[0].priority,
        scheduledAt: this.queue[0].scheduledAt
      } : undefined
    };
  }

  /**
   * Clear the queue (for testing or emergency)
   */
  static clearQueue(): void {
    this.queue = [];
    log.info('PDF generation queue cleared', 'PDF_GENERATOR');
  }

  /**
   * Helper method to load image as data URL
   */
  private static async loadImageAsDataUrl(imagePath: string): Promise<string> {
    try {
      const response = await fetch(imagePath);
      const blob = await response.blob();
      
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
    } catch (error) {
      log.warn('Failed to load image as data URL', 'PDF_GENERATOR', {
        imagePath,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return '';
    }
  }

  /**
   * Helper method to load checklist images
   */
  private static async loadChecklistImages(checklist: ChecklistData) {
    const loadImageWithFallback = async (url: string): Promise<string | undefined> => {
      if (!url) return undefined;
      
      try {
        const response = await fetch(url);
        if (!response.ok) return undefined;
        
        const blob = await response.blob();
        return new Promise((resolve) => {
          const reader = new FileReader();
          reader.onload = () => resolve(reader.result as string);
          reader.onerror = () => resolve(undefined);
          reader.readAsDataURL(blob);
        });
      } catch {
        return undefined;
      }
    };

    const [beforeImageDataUrl, afterImageDataUrl, signatureDataUrl] = await Promise.all([
      loadImageWithFallback(checklist.beforeImage || ''),
      loadImageWithFallback(checklist.afterImage || ''),
      loadImageWithFallback(checklist.inspectorSignature || '')
    ]);

    return {
      beforeImageDataUrl,
      afterImageDataUrl,
      signatureDataUrl
    };
  }
} 
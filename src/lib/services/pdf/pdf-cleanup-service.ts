import { PDFStorageService } from '../storage/pdf-storage-service';
import { log } from '../../utils/logger';

export interface CleanupOptions {
  maxAgeInDays?: number;
  maxSizePerUserMB?: number;
  dryRun?: boolean;
}

export interface CleanupResult {
  usersProcessed: number;
  totalFilesFound: number;
  filesDeleted: number;
  spaceClearedMB: number;
  errors: string[];
  dryRun: boolean;
}

export class PDFCleanupService {
  private static readonly DEFAULT_MAX_AGE_DAYS = 120;
  private static readonly DEFAULT_MAX_SIZE_PER_USER_MB = 100; // 100MB per user
  private static readonly CLEANUP_INTERVAL_MS = 24 * 60 * 60 * 1000; // 24 hours
  
  private static cleanupTimer: NodeJS.Timeout | null = null;
  private static isRunning = false;

  /**
   * Start automatic cleanup service
   */
  static startAutomaticCleanup(options: CleanupOptions = {}): void {
    if (this.cleanupTimer) {
      log.info('PDF cleanup service already running', 'PDF_CLEANUP');
      return;
    }

    const cleanupOptions = {
      maxAgeInDays: options.maxAgeInDays || this.DEFAULT_MAX_AGE_DAYS,
      maxSizePerUserMB: options.maxSizePerUserMB || this.DEFAULT_MAX_SIZE_PER_USER_MB,
      dryRun: options.dryRun || false
    };

    // Run initial cleanup
    this.performCleanup(cleanupOptions).catch(error => {
      log.error('Initial PDF cleanup failed', 'PDF_CLEANUP', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
    });

    // Schedule regular cleanup
    this.cleanupTimer = setInterval(() => {
      this.performCleanup(cleanupOptions).catch(error => {
        log.error('Scheduled PDF cleanup failed', 'PDF_CLEANUP', {
          error: error instanceof Error ? error.message : 'Unknown error'
        });
      });
    }, this.CLEANUP_INTERVAL_MS);

    log.info('PDF cleanup service started', 'PDF_CLEANUP', {
      interval: this.CLEANUP_INTERVAL_MS / 1000 / 60 / 60, // hours
      maxAgeInDays: cleanupOptions.maxAgeInDays,
      maxSizePerUserMB: cleanupOptions.maxSizePerUserMB,
      dryRun: cleanupOptions.dryRun
    });
  }

  /**
   * Stop automatic cleanup service
   */
  static stopAutomaticCleanup(): void {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
      this.cleanupTimer = null;
      log.info('PDF cleanup service stopped', 'PDF_CLEANUP');
    }
  }

  /**
   * Perform manual cleanup
   */
  static async performCleanup(options: CleanupOptions = {}): Promise<CleanupResult> {
    if (this.isRunning) {
      throw new Error('Cleanup already in progress');
    }

    this.isRunning = true;
    const startTime = Date.now();

    const result: CleanupResult = {
      usersProcessed: 0,
      totalFilesFound: 0,
      filesDeleted: 0,
      spaceClearedMB: 0,
      errors: [],
      dryRun: options.dryRun || false
    };

    try {
      log.info('Starting PDF cleanup', 'PDF_CLEANUP', {
        options,
        dryRun: result.dryRun
      });

      // For now, we'll need to get user IDs from somewhere
      // In a real implementation, you might want to:
      // 1. Query Firestore for all unique userIds
      // 2. Or maintain a separate index of users with PDFs
      // 3. Or iterate through Firebase Storage structure
      
      // This is a simplified version that would need to be enhanced
      // based on your user management system
      const userIds = await this.getAllUserIds();

      for (const userId of userIds) {
        try {
          const userResult = await this.cleanupUserPDFs(userId, options);
          
          result.usersProcessed++;
          result.totalFilesFound += userResult.totalFiles;
          result.filesDeleted += userResult.deletedFiles;
          result.spaceClearedMB += userResult.spaceClearedMB;

          log.debug('User PDF cleanup completed', 'PDF_CLEANUP', {
            userId,
            totalFiles: userResult.totalFiles,
            deletedFiles: userResult.deletedFiles,
            spaceClearedMB: userResult.spaceClearedMB
          });

        } catch (userError) {
          const errorMsg = `User ${userId}: ${userError instanceof Error ? userError.message : 'Unknown error'}`;
          result.errors.push(errorMsg);
          log.warn('User PDF cleanup failed', 'PDF_CLEANUP', {
            userId,
            error: errorMsg
          });
        }
      }

      const duration = Date.now() - startTime;
      log.info('PDF cleanup completed', 'PDF_CLEANUP', {
        duration,
        result,
        averageTimePerUser: result.usersProcessed > 0 ? duration / result.usersProcessed : 0
      });

    } catch (error) {
      const errorMsg = `Cleanup failed: ${error instanceof Error ? error.message : 'Unknown error'}`;
      result.errors.push(errorMsg);
      log.error('PDF cleanup failed', 'PDF_CLEANUP', {
        error: errorMsg
      });
    } finally {
      this.isRunning = false;
    }

    return result;
  }

  /**
   * Clean up PDFs for a specific user
   */
  static async cleanupUserPDFs(
    userId: string, 
    options: CleanupOptions = {}
  ): Promise<{
    totalFiles: number;
    deletedFiles: number;
    spaceClearedMB: number;
  }> {
    const maxAgeMs = (options.maxAgeInDays || this.DEFAULT_MAX_AGE_DAYS) * 24 * 60 * 60 * 1000;
    const maxSizeBytes = (options.maxSizePerUserMB || this.DEFAULT_MAX_SIZE_PER_USER_MB) * 1024 * 1024;
    
    // Get current storage usage
    const storageUsage = await PDFStorageService.getPDFStorageUsage(userId);
    
    const result = {
      totalFiles: storageUsage.fileCount,
      deletedFiles: 0,
      spaceClearedMB: 0
    };

    // Clean up old PDFs
    if (!options.dryRun) {
      const cleanupResult = await PDFStorageService.cleanupStalePDFs(userId);
      result.deletedFiles += cleanupResult.deleted;
      
      // Calculate approximate space cleared (we don't have exact sizes for deleted files)
      // This is an estimation
      const avgFileSize = result.totalFiles > 0 ? storageUsage.totalSize / result.totalFiles : 0;
      result.spaceClearedMB = (cleanupResult.deleted * avgFileSize) / (1024 * 1024);
    }

    // Check if user is still over size limit
    const updatedUsage = await PDFStorageService.getPDFStorageUsage(userId);
    if (updatedUsage.totalSize > maxSizeBytes) {
      log.warn('User still over PDF storage limit after cleanup', 'PDF_CLEANUP', {
        userId,
        currentSizeMB: updatedUsage.totalSize / (1024 * 1024),
        limitMB: maxSizeBytes / (1024 * 1024),
        fileCount: updatedUsage.fileCount
      });
    }

    return result;
  }

  /**
   * Get cleanup statistics for monitoring
   */
  static getCleanupStatus(): {
    isRunning: boolean;
    nextCleanup?: number;
    lastCleanup?: number;
  } {
    return {
      isRunning: this.isRunning,
      nextCleanup: this.cleanupTimer ? Date.now() + this.CLEANUP_INTERVAL_MS : undefined,
      lastCleanup: undefined // Would need to track this separately
    };
  }

  /**
   * Clean up PDFs for deleted checklists
   */
  static async cleanupOrphanedPDFs(userId: string, existingChecklistIds: string[]): Promise<number> {
    try {
      let cleanedCount = 0;
      
      // Get current storage usage to list all PDFs for this user
      const userPDFs = await PDFStorageService.getPDFStorageUsage(userId);
      
      if (userPDFs.fileCount === 0) {
        log.debug('No PDFs found for user', 'PDF_CLEANUP', { userId });
        return 0;
      }

      // Get all PDF files for the user
      const userPDFFiles = await PDFStorageService.listUserPDFs(userId);
      const existingChecklistSet = new Set(existingChecklistIds);
      
      // Check each PDF file to see if its checklist still exists
      for (const pdfFile of userPDFFiles) {
        try {
          // Extract checklist ID from filename (format: {checklistId}_{timestamp}.pdf)
          const fileNameParts = pdfFile.name.split('_');
          if (fileNameParts.length >= 2) {
            const checklistId = fileNameParts[0];
            
            // If this checklist no longer exists, delete the PDF
            if (!existingChecklistSet.has(checklistId)) {
              await PDFStorageService.deleteCachedPDF(userId, checklistId);
              cleanedCount++;
              
              log.debug('Cleaned up orphaned PDF', 'PDF_CLEANUP', {
                userId,
                checklistId,
                fileName: pdfFile.name
              });
            }
          }
        } catch (cleanupError) {
          log.warn('Failed to cleanup individual orphaned PDF', 'PDF_CLEANUP', {
            userId,
            fileName: pdfFile.name,
            error: cleanupError instanceof Error ? cleanupError.message : 'Unknown error'
          });
        }
      }
      
      log.info('Orphaned PDF cleanup completed', 'PDF_CLEANUP', {
        userId,
        existingChecklists: existingChecklistIds.length,
        cleanedCount
      });
      
      return cleanedCount;
      
    } catch (error) {
      log.error('Failed to cleanup orphaned PDFs', 'PDF_CLEANUP', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return 0;
    }
  }

  /**
   * Get all user IDs that have PDFs (placeholder implementation)
   */
  private static async getAllUserIds(): Promise<string[]> {
    // This is a simplified implementation
    // In a real system, you would:
    // 1. Query your user management system
    // 2. Or scan Firebase Storage structure
    // 3. Or maintain an index of users with PDFs
    
    try {
      // For now, return empty array - this needs to be implemented
      // based on your specific user management approach
      log.debug('Getting all user IDs for PDF cleanup', 'PDF_CLEANUP');
      return [];
      
    } catch (error) {
      log.error('Failed to get user IDs for cleanup', 'PDF_CLEANUP', {
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      return [];
    }
  }

  /**
   * Force cleanup for a specific user (emergency use)
   */
  static async forceUserCleanup(userId: string): Promise<void> {
    log.warn('Force cleaning all PDFs for user', 'PDF_CLEANUP', { userId });
    
    try {
      const result = await PDFStorageService.cleanupStalePDFs(userId);
      log.info('Force cleanup completed', 'PDF_CLEANUP', {
        userId,
        deleted: result.deleted,
        errors: result.errors.length
      });
    } catch (error) {
      log.error('Force cleanup failed', 'PDF_CLEANUP', {
        userId,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }
} 
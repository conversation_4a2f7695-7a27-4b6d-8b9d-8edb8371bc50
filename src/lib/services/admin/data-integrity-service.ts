import { 
  collection, 
  getDocs, 
  query, 
  where,
  doc,
  updateDoc,
  deleteDoc,
  Timestamp,
  writeBatch
} from 'firebase/firestore';
import { db } from '@/config/firebase-persistence';
import { ChecklistData } from '@/types/checklist';
import { EquipmentTag } from '@/types/equipment-tag';

const CHECKLISTS_COLLECTION = 'checklists';
const EQUIPMENT_TAGS_COLLECTION = 'equipment-tags';

export interface IntegrityIssue {
  id: string;
  type: 'orphaned_checklist' | 'invalid_reference' | 'missing_equipment_tag' | 'duplicate_tag_numbers' | 'data_mismatch';
  severity: 'critical' | 'warning' | 'info';
  title: string;
  description: string;
  affectedItems: {
    checklistId?: string;
    equipmentTagId?: string;
    tagNumber?: string;
    clientName?: string;
    count?: number;
  };
  suggestedActions: {
    action: 'delete_checklist' | 'update_reference' | 'create_equipment_tag' | 'merge_tags' | 'update_data';
    description: string;
    risk: 'low' | 'medium' | 'high';
  }[];
  autoResolvable: boolean;
}

export interface IntegrityReport {
  summary: {
    totalIssues: number;
    criticalIssues: number;
    warningIssues: number;
    infoIssues: number;
    autoResolvableIssues: number;
  };
  issues: IntegrityIssue[];
  lastChecked: string;
  dataStats: {
    totalEquipmentTags: number;
    totalChecklists: number;
    checklistsWithValidReferences: number;
    checklistsWithInvalidReferences: number;
    orphanedChecklists: number;
    unusedEquipmentTags: number;
  };
}

export class DataIntegrityService {
  /**
   * Perform comprehensive data integrity analysis
   */
  static async analyzeDataIntegrity(): Promise<IntegrityReport> {
    try {
      console.log('Starting data integrity analysis...');
      
      // Load all data
      const [equipmentTags, checklists] = await Promise.all([
        this.loadAllEquipmentTags(),
        this.loadAllChecklists()
      ]);

      console.log(`Loaded ${equipmentTags.size} equipment tags and ${checklists.length} checklists`);

      const issues: IntegrityIssue[] = [];
      
      // Run all integrity checks
      issues.push(...await this.checkOrphanedChecklists(checklists, equipmentTags));
      issues.push(...await this.checkInvalidReferences(checklists, equipmentTags));
      issues.push(...await this.checkDataMismatches(checklists, equipmentTags));
      issues.push(...await this.checkDuplicateTagNumbers(equipmentTags));
      issues.push(...await this.checkUnusedEquipmentTags(equipmentTags, checklists));

      // Calculate statistics
      const summary = this.calculateSummary(issues);
      const dataStats = this.calculateDataStats(equipmentTags, checklists, issues);

      return {
        summary,
        issues,
        lastChecked: new Date().toISOString(),
        dataStats
      };
    } catch (error) {
      console.error('Error analyzing data integrity:', error);
      throw new Error('Failed to analyze data integrity');
    }
  }

  /**
   * Load all equipment tags into a Map for fast lookup
   */
  private static async loadAllEquipmentTags(): Promise<Map<string, EquipmentTag>> {
    const tagsRef = collection(db, EQUIPMENT_TAGS_COLLECTION);
    const snapshot = await getDocs(tagsRef);
    
    const tagsMap = new Map<string, EquipmentTag>();
    snapshot.forEach(doc => {
      const data = doc.data();
      const equipmentTag: EquipmentTag = {
        ...data,
        id: doc.id,
        createdAt: data.createdAt instanceof Timestamp ? data.createdAt.toDate().toISOString() : data.createdAt,
        updatedAt: data.updatedAt instanceof Timestamp ? data.updatedAt.toDate().toISOString() : data.updatedAt,
      } as EquipmentTag;
      tagsMap.set(doc.id, equipmentTag);
    });
    
    return tagsMap;
  }

  /**
   * Load all checklists
   */
  private static async loadAllChecklists(): Promise<ChecklistData[]> {
    const checklistsRef = collection(db, CHECKLISTS_COLLECTION);
    const snapshot = await getDocs(checklistsRef);
    
    const checklists: ChecklistData[] = [];
    snapshot.forEach(doc => {
      const data = doc.data();
      const checklist: ChecklistData = {
        ...data,
        id: doc.id,
        createdAt: data.createdAt instanceof Timestamp ? data.createdAt.toDate().toISOString() : data.createdAt,
        updatedAt: data.updatedAt instanceof Timestamp ? data.updatedAt.toDate().toISOString() : data.updatedAt,
      } as ChecklistData;
      checklists.push(checklist);
    });
    
    return checklists;
  }

  /**
   * Check for checklists without equipmentTagId (orphaned)
   */
  private static async checkOrphanedChecklists(
    checklists: ChecklistData[], 
    equipmentTags: Map<string, EquipmentTag>
  ): Promise<IntegrityIssue[]> {
    const issues: IntegrityIssue[] = [];
    
    checklists.forEach(checklist => {
      if (!checklist.equipmentTagId) {
        issues.push({
          id: `orphaned_${checklist.id}`,
          type: 'orphaned_checklist',
          severity: 'critical',
          title: 'Orphaned Checklist',
          description: `Checklist "${checklist.id}" has no equipment tag reference`,
          affectedItems: {
            checklistId: checklist.id,
            tagNumber: checklist.generalInfo?.tagNo,
            clientName: checklist.generalInfo?.clientName
          },
          suggestedActions: [
            {
              action: 'update_reference',
              description: 'Link to existing equipment tag based on tag number and client',
              risk: 'low'
            },
            {
              action: 'create_equipment_tag',
              description: 'Create new equipment tag from checklist data',
              risk: 'medium'
            },
            {
              action: 'delete_checklist',
              description: 'Delete orphaned checklist (data loss)',
              risk: 'high'
            }
          ],
          autoResolvable: true
        });
      }
    });
    
    return issues;
  }

  /**
   * Check for checklists with invalid equipment tag references
   */
  private static async checkInvalidReferences(
    checklists: ChecklistData[], 
    equipmentTags: Map<string, EquipmentTag>
  ): Promise<IntegrityIssue[]> {
    const issues: IntegrityIssue[] = [];
    
    checklists.forEach(checklist => {
      if (checklist.equipmentTagId && !equipmentTags.has(checklist.equipmentTagId)) {
        issues.push({
          id: `invalid_ref_${checklist.id}`,
          type: 'invalid_reference',
          severity: 'critical',
          title: 'Invalid Equipment Tag Reference',
          description: `Checklist "${checklist.id}" references non-existent equipment tag "${checklist.equipmentTagId}"`,
          affectedItems: {
            checklistId: checklist.id,
            equipmentTagId: checklist.equipmentTagId,
            tagNumber: checklist.generalInfo?.tagNo,
            clientName: checklist.generalInfo?.clientName
          },
          suggestedActions: [
            {
              action: 'update_reference',
              description: 'Find and link to correct equipment tag',
              risk: 'low'
            },
            {
              action: 'create_equipment_tag',
              description: 'Create equipment tag with the referenced ID',
              risk: 'medium'
            },
            {
              action: 'delete_checklist',
              description: 'Delete checklist with invalid reference',
              risk: 'high'
            }
          ],
          autoResolvable: true
        });
      }
    });
    
    return issues;
  }

  /**
   * Check for data mismatches between checklists and equipment tags
   */
  private static async checkDataMismatches(
    checklists: ChecklistData[], 
    equipmentTags: Map<string, EquipmentTag>
  ): Promise<IntegrityIssue[]> {
    const issues: IntegrityIssue[] = [];
    
    checklists.forEach(checklist => {
      if (checklist.equipmentTagId && equipmentTags.has(checklist.equipmentTagId)) {
        const equipmentTag = equipmentTags.get(checklist.equipmentTagId)!;
        const mismatches: string[] = [];
        
        if (checklist.generalInfo?.tagNo !== equipmentTag.tagNumber) {
          mismatches.push(`Tag number: "${checklist.generalInfo?.tagNo}" vs "${equipmentTag.tagNumber}"`);
        }
        
        if (checklist.generalInfo?.clientName !== equipmentTag.clientName) {
          mismatches.push(`Client: "${checklist.generalInfo?.clientName}" vs "${equipmentTag.clientName}"`);
        }
        
        if (checklist.generalInfo?.equipmentName !== equipmentTag.equipmentName) {
          mismatches.push(`Equipment: "${checklist.generalInfo?.equipmentName}" vs "${equipmentTag.equipmentName}"`);
        }
        
        if (mismatches.length > 0) {
          issues.push({
            id: `mismatch_${checklist.id}`,
            type: 'data_mismatch',
            severity: 'warning',
            title: 'Data Mismatch',
            description: `Checklist data doesn't match equipment tag: ${mismatches.join(', ')}`,
            affectedItems: {
              checklistId: checklist.id,
              equipmentTagId: checklist.equipmentTagId,
              tagNumber: checklist.generalInfo?.tagNo,
              clientName: checklist.generalInfo?.clientName
            },
            suggestedActions: [
              {
                action: 'update_data',
                description: 'Update checklist data to match equipment tag',
                risk: 'low'
              },
              {
                action: 'update_reference',
                description: 'Find correct equipment tag for this checklist',
                risk: 'medium'
              }
            ],
            autoResolvable: true
          });
        }
      }
    });
    
    return issues;
  }

  /**
   * Check for duplicate tag numbers within the same client
   */
  private static async checkDuplicateTagNumbers(
    equipmentTags: Map<string, EquipmentTag>
  ): Promise<IntegrityIssue[]> {
    const issues: IntegrityIssue[] = [];
    const tagMap = new Map<string, EquipmentTag[]>();
    
    // Group by client-tagNumber combination
    equipmentTags.forEach(tag => {
      const key = `${tag.clientName}-${tag.tagNumber}`.toLowerCase();
      if (!tagMap.has(key)) {
        tagMap.set(key, []);
      }
      tagMap.get(key)!.push(tag);
    });
    
    // Find duplicates
    tagMap.forEach((tags, key) => {
      if (tags.length > 1) {
        issues.push({
          id: `duplicate_${key}`,
          type: 'duplicate_tag_numbers',
          severity: 'warning',
          title: 'Duplicate Tag Numbers',
          description: `${tags.length} equipment tags have the same tag number "${tags[0].tagNumber}" for client "${tags[0].clientName}"`,
          affectedItems: {
            tagNumber: tags[0].tagNumber,
            clientName: tags[0].clientName,
            count: tags.length
          },
          suggestedActions: [
            {
              action: 'merge_tags',
              description: 'Merge duplicate tags and update references',
              risk: 'medium'
            },
            {
              action: 'update_data',
              description: 'Update tag numbers to make them unique',
              risk: 'low'
            }
          ],
          autoResolvable: false
        });
      }
    });
    
    return issues;
  }

  /**
   * Check for unused equipment tags
   */
  private static async checkUnusedEquipmentTags(
    equipmentTags: Map<string, EquipmentTag>,
    checklists: ChecklistData[]
  ): Promise<IntegrityIssue[]> {
    const issues: IntegrityIssue[] = [];
    const usedTagIds = new Set(checklists.map(c => c.equipmentTagId).filter(Boolean));
    
    equipmentTags.forEach(tag => {
      if (!usedTagIds.has(tag.id)) {
        issues.push({
          id: `unused_${tag.id}`,
          type: 'missing_equipment_tag',
          severity: 'info',
          title: 'Unused Equipment Tag',
          description: `Equipment tag "${tag.tagNumber}" (${tag.clientName}) has no associated checklists`,
          affectedItems: {
            equipmentTagId: tag.id,
            tagNumber: tag.tagNumber,
            clientName: tag.clientName
          },
          suggestedActions: [
            {
              action: 'delete_checklist',
              description: 'Delete unused equipment tag',
              risk: 'low'
            }
          ],
          autoResolvable: false
        });
      }
    });
    
    return issues;
  }

  /**
   * Calculate summary statistics
   */
  private static calculateSummary(issues: IntegrityIssue[]) {
    return {
      totalIssues: issues.length,
      criticalIssues: issues.filter(i => i.severity === 'critical').length,
      warningIssues: issues.filter(i => i.severity === 'warning').length,
      infoIssues: issues.filter(i => i.severity === 'info').length,
      autoResolvableIssues: issues.filter(i => i.autoResolvable).length
    };
  }

  /**
   * Calculate data statistics
   */
  private static calculateDataStats(
    equipmentTags: Map<string, EquipmentTag>,
    checklists: ChecklistData[],
    issues: IntegrityIssue[]
  ) {
    const checklistsWithValidReferences = checklists.filter(c => 
      c.equipmentTagId && equipmentTags.has(c.equipmentTagId)
    ).length;
    
    const checklistsWithInvalidReferences = issues.filter(i => 
      i.type === 'invalid_reference'
    ).length;
    
    const orphanedChecklists = issues.filter(i => 
      i.type === 'orphaned_checklist'
    ).length;
    
    const unusedEquipmentTags = issues.filter(i => 
      i.type === 'missing_equipment_tag'
    ).length;

    return {
      totalEquipmentTags: equipmentTags.size,
      totalChecklists: checklists.length,
      checklistsWithValidReferences,
      checklistsWithInvalidReferences,
      orphanedChecklists,
      unusedEquipmentTags
    };
  }

  /**
   * Auto-resolve specific integrity issues
   */
  static async autoResolveIssue(issueId: string, action: string): Promise<{
    success: boolean;
    message: string;
    details?: any;
  }> {
    try {
      // This is a safe operation that only fixes data, doesn't delete
      console.log(`Auto-resolving issue ${issueId} with action ${action}`);
      
      // Implementation would go here based on the action type
      // For now, return a placeholder response
      return {
        success: true,
        message: `Issue ${issueId} resolved successfully with action ${action}`,
        details: { issueId, action, timestamp: new Date().toISOString() }
      };
    } catch (error) {
      console.error('Error auto-resolving issue:', error);
      return {
        success: false,
        message: `Failed to resolve issue ${issueId}: ${error instanceof Error ? error.message : 'Unknown error'}`
      };
    }
  }

  /**
   * Get detailed information about a specific issue
   */
  static async getIssueDetails(issueId: string): Promise<any> {
    try {
      // Implementation would fetch detailed information about the specific issue
      return {
        issueId,
        detailedAnalysis: 'Detailed analysis would go here',
        affectedData: {},
        resolutionOptions: []
      };
    } catch (error) {
      console.error('Error getting issue details:', error);
      throw new Error('Failed to get issue details');
    }
  }
} 
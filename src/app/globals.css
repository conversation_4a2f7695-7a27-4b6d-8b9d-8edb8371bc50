@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: 'Inter', ui-sans-serif, system-ui, sans-serif;
  --font-inter: 'Inter', ui-sans-serif, system-ui, sans-serif;
  --font-mono: var(--font-geist-mono);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.75rem;
  /* Balanced color scheme with red as primary */
  --background: rgb(10 10 10);
  --foreground: rgb(248 250 252);
  --card: rgb(20 20 20);
  --card-foreground: rgb(248 250 252);
  --popover: rgb(20 20 20);
  --popover-foreground: rgb(248 250 252);
  --primary: rgb(220 38 38); /* Keep red as primary brand color */
  --primary-foreground: rgb(248 250 252);
  --secondary: rgb(30 41 59); /* Slate blue for secondary elements */
  --secondary-foreground: rgb(248 250 252);
  --muted: rgb(30 30 35); /* Neutral muted background */
  --muted-foreground: rgb(161 161 170);
  --accent: rgb(59 130 246); /* Blue accent for interactive elements */
  --accent-foreground: rgb(248 250 252);
  --destructive: rgb(239 68 68); /* Keep red for destructive actions */
  --border: rgb(45 45 50); /* Neutral border color */
  --input: rgb(45 45 50); /* Neutral input background */
  --ring: rgb(59 130 246); /* Blue focus ring for better accessibility */
  --chart-1: rgb(220 38 38); /* Primary red for main data */
  --chart-2: rgb(59 130 246); /* Blue for secondary data */
  --chart-3: rgb(34 197 94); /* Green for success/positive data */
  --chart-4: rgb(251 191 36); /* Amber for warning data */
  --chart-5: rgb(168 85 247); /* Purple for additional data */
  --sidebar: rgb(15 15 18); /* Darker sidebar */
  --sidebar-foreground: rgb(248 250 252);
  --sidebar-primary: rgb(220 38 38); /* Keep red for sidebar primary */
  --sidebar-primary-foreground: rgb(248 250 252);
  --sidebar-accent: rgb(30 41 59); /* Blue-slate for sidebar accent */
  --sidebar-accent-foreground: rgb(248 250 252);
  --sidebar-border: rgb(45 45 50); /* Neutral sidebar border */
  --sidebar-ring: rgb(59 130 246); /* Blue sidebar focus ring */
}

.dark {
  --background: rgb(10 10 10);
  --foreground: rgb(248 250 252);
  --card: rgb(20 20 20);
  --card-foreground: rgb(248 250 252);
  --popover: rgb(20 20 20);
  --popover-foreground: rgb(248 250 252);
  --primary: rgb(220 38 38); /* Keep red as primary brand color */
  --primary-foreground: rgb(248 250 252);
  --secondary: rgb(30 41 59); /* Slate blue for secondary elements */
  --secondary-foreground: rgb(248 250 252);
  --muted: rgb(30 30 35); /* Neutral muted background */
  --muted-foreground: rgb(161 161 170);
  --accent: rgb(59 130 246); /* Blue accent for interactive elements */
  --accent-foreground: rgb(248 250 252);
  --destructive: rgb(239 68 68); /* Keep red for destructive actions */
  --border: rgb(45 45 50); /* Neutral border color */
  --input: rgb(45 45 50); /* Neutral input background */
  --ring: rgb(59 130 246); /* Blue focus ring for better accessibility */
  --chart-1: rgb(220 38 38); /* Primary red for main data */
  --chart-2: rgb(59 130 246); /* Blue for secondary data */
  --chart-3: rgb(34 197 94); /* Green for success/positive data */
  --chart-4: rgb(251 191 36); /* Amber for warning data */
  --chart-5: rgb(168 85 247); /* Purple for additional data */
  --sidebar: rgb(15 15 18); /* Darker sidebar */
  --sidebar-foreground: rgb(248 250 252);
  --sidebar-primary: rgb(220 38 38); /* Keep red for sidebar primary */
  --sidebar-primary-foreground: rgb(248 250 252);
  --sidebar-accent: rgb(30 41 59); /* Blue-slate for sidebar accent */
  --sidebar-accent-foreground: rgb(248 250 252);
  --sidebar-border: rgb(45 45 50); /* Neutral sidebar border */
  --sidebar-ring: rgb(59 130 246); /* Blue sidebar focus ring */
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
    background: radial-gradient(ellipse at top, rgb(20 20 20) 0%, rgb(10 10 10) 50%);
    min-height: 100vh;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer utilities {
  /* Improved container spacing */
  .container {
    @apply px-4 sm:px-6 lg:px-8;
    max-width: 1400px;
  }
  
  /* Better section spacing */
  .section-padding {
    @apply py-8 sm:py-12 lg:py-16;
  }
  
  /* Improved header spacing */
  .header-spacing {
    @apply px-4 sm:px-6 lg:px-8 py-3 sm:py-4;
  }
  
  /* Page top spacing to account for fixed header */
  .page-top-spacing {
    @apply pt-24 sm:pt-28 lg:pt-32;
  }

  /* Enhanced Glass Effect with Balanced Colors */
  .glass {
    background: rgba(20, 20, 20, 0.7);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(59, 130, 246, 0.15); /* Blue border for glass effect */
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  }

  .glass-light {
    background: rgba(20, 20, 20, 0.4);
    backdrop-filter: blur(16px);
    border: 1px solid rgba(59, 130, 246, 0.1); /* Blue border for light glass */
  }

  /* Custom Grid Pattern with Subtle Blue Accent */
  .bg-grid-pattern {
    background-image: radial-gradient(circle, rgba(59, 130, 246, 0.05) 1px, transparent 1px);
    background-size: 40px 40px;
  }

  /* Balanced Gradient Classes */
  .bg-red-gradient {
    background: linear-gradient(135deg, rgb(127, 29, 29) 0%, rgb(220, 38, 38) 50%, rgb(248, 113, 113) 100%);
  }

  .bg-blue-gradient {
    background: linear-gradient(135deg, rgb(30, 58, 138) 0%, rgb(59, 130, 246) 50%, rgb(147, 197, 253) 100%);
  }

  .bg-green-gradient {
    background: linear-gradient(135deg, rgb(20, 83, 45) 0%, rgb(34, 197, 94) 50%, rgb(134, 239, 172) 100%);
  }

  .bg-neutral-gradient {
    background: linear-gradient(135deg, rgb(30, 30, 35) 0%, rgb(45, 45, 50) 100%);
  }

  .text-red-gradient {
    background: linear-gradient(135deg, rgb(220, 38, 38) 0%, rgb(248, 113, 113) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-blue-gradient {
    background: linear-gradient(135deg, rgb(59, 130, 246) 0%, rgb(147, 197, 253) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .text-green-gradient {
    background: linear-gradient(135deg, rgb(34, 197, 94) 0%, rgb(134, 239, 172) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  /* Enhanced Button Styles with Color Variety */
  .btn-primary {
    @apply bg-gradient-to-r from-red-700 to-red-600 hover:from-red-800 hover:to-red-700 text-white shadow-lg shadow-red-500/25 transition-all duration-300;
  }

  .btn-secondary {
    @apply bg-gradient-to-r from-blue-600 to-blue-500 hover:from-blue-700 hover:to-blue-600 text-white shadow-lg shadow-blue-500/25 transition-all duration-300;
  }

  .btn-success {
    @apply bg-gradient-to-r from-green-600 to-green-500 hover:from-green-700 hover:to-green-600 text-white shadow-lg shadow-green-500/25 transition-all duration-300;
  }

  .btn-outline {
    @apply bg-transparent border border-blue-500/30 hover:bg-blue-900/20 text-slate-200 transition-all duration-300 backdrop-blur-sm;
  }

  /* Better spacing utilities */
  .space-section {
    @apply space-y-12 sm:space-y-16 lg:space-y-20;
  }

  .space-content {
    @apply space-y-6 sm:space-y-8;
  }

  /* Enhanced card styles with balanced colors */
  .card-modern {
    @apply bg-black/40 backdrop-blur-xl rounded-2xl p-6 sm:p-8 border border-blue-500/20 hover:border-blue-500/30 transition-all duration-300;
  }

  .card-success {
    @apply bg-black/40 backdrop-blur-xl rounded-2xl p-6 sm:p-8 border border-green-500/20 hover:border-green-500/30 transition-all duration-300;
  }

  .card-warning {
    @apply bg-black/40 backdrop-blur-xl rounded-2xl p-6 sm:p-8 border border-amber-500/20 hover:border-amber-500/30 transition-all duration-300;
  }

  .card-danger {
    @apply bg-black/40 backdrop-blur-xl rounded-2xl p-6 sm:p-8 border border-red-500/20 hover:border-red-500/30 transition-all duration-300;
  }

  /* Better typography */
  .heading-xl {
    @apply text-4xl sm:text-5xl lg:text-6xl font-bold tracking-tight;
  }

  .heading-lg {
    @apply text-3xl sm:text-4xl lg:text-5xl font-bold tracking-tight;
  }

  .heading-md {
    @apply text-2xl sm:text-3xl lg:text-4xl font-bold tracking-tight;
  }
}

/* Custom Red Gradient Animations */
@keyframes gradient-x {
  0%, 100% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
}

@keyframes gradient-y {
  0%, 100% {
    background-position: 50% 0%;
  }
  50% {
    background-position: 50% 100%;
  }
}

@keyframes gradient-xy {
  0%, 100% {
    background-position: 0% 0%;
  }
  25% {
    background-position: 100% 0%;
  }
  50% {
    background-position: 100% 100%;
  }
  75% {
    background-position: 0% 100%;
  }
}

.animate-gradient-x {
  animation: gradient-x 15s ease infinite;
}

.animate-gradient-y {
  animation: gradient-y 15s ease infinite;
}

.animate-gradient-xy {
  animation: gradient-xy 15s ease infinite;
}

/* Floating Animation */
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-15px);
  }
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

/* Red Pulse Glow */
@keyframes pulse-glow {
  0%, 100% {
    box-shadow: 0 0 20px rgba(220, 38, 38, 0.3);
  }
  50% {
    box-shadow: 0 0 40px rgba(185, 28, 28, 0.5);
  }
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

/* Responsive improvements */
@media (max-width: 640px) {
  .container {
    @apply px-3;
  }
  
  .section-padding {
    @apply py-6;
  }
  
  .header-spacing {
    @apply px-3 py-2;
  }
  
  /* Ensure mobile header spacing accounts for smaller header */
  main {
    @apply pt-16;
  }
}

@media (min-width: 641px) and (max-width: 1024px) {
  /* Tablet spacing */
  main {
    @apply pt-20;
  }
}

@media (min-width: 1025px) {
  /* Desktop spacing */
  main {
    @apply pt-24;
  }
}

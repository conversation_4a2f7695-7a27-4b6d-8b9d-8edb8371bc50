'use client'

import { motion } from 'framer-motion';
import { <PERSON>, Hammer, <PERSON><PERSON>, Co<PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from 'lucide-react';
import GradientText from '@/components/gradient-text';

export default function ProjectsPage() {
  return (
    <div className="min-h-screen flex items-center justify-center px-4">
      <div className="absolute inset-0 bg-gradient-to-br from-red-950/20 via-black to-red-950/20" />
      
      <motion.div 
        className="text-center space-y-8 max-w-4xl mx-auto relative z-10"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
      >
        {/* Main Title */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <h1 className="text-5xl sm:text-6xl md:text-7xl font-black mb-6">
            <GradientText gradient="from-red-200 to-red-400">
              Our Projects
            </GradientText>
          </h1>
        </motion.div>

        {/* Animated Icons */}
        <motion.div 
          className="flex justify-center space-x-6 mb-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <motion.div
            animate={{ y: [0, -15, 0] }}
            transition={{ duration: 2.5, repeat: Infinity, repeatDelay: 0.5 }}
          >
            <Building className="h-12 w-12 text-red-400" />
          </motion.div>
          <motion.div
            animate={{ rotate: [0, 15, -15, 0] }}
            transition={{ duration: 2, repeat: Infinity, repeatDelay: 2 }}
          >
            <Hammer className="h-12 w-12 text-red-300" />
          </motion.div>
          <motion.div
            animate={{ scale: [1, 1.2, 1] }}
            transition={{ duration: 1.5, repeat: Infinity, repeatDelay: 1 }}
          >
            <Zap className="h-12 w-12 text-red-500" />
          </motion.div>
          <motion.div
            animate={{ rotate: [0, 360] }}
            transition={{ duration: 4, repeat: Infinity, ease: "linear" }}
          >
            <Cog className="h-12 w-12 text-red-600" />
          </motion.div>
        </motion.div>

        {/* Humorous Message */}
        <motion.div
          className="space-y-6"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <div className="text-6xl mb-4">🏗️</div>
          
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            Our Portfolio Is Under Construction!
          </h2>
          
          <p className="text-xl sm:text-2xl text-slate-300 leading-relaxed max-w-3xl mx-auto">
            We've been so busy building amazing ACMV systems across Qatar for 15+ years, 
            we forgot to build our project showcase website! 
          </p>
          
          <div className="bg-red-900/20 border border-red-500/30 rounded-2xl p-6 max-w-2xl mx-auto">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <Sparkles className="h-6 w-6 text-red-400" />
              <span className="text-lg font-semibold text-white">Plot Twist!</span>
            </div>
            <p className="text-slate-300">
              While we're curating our most impressive projects (hospitals, hotels, malls, data centers), 
              our engineers are probably fixing someone's AC right now. Priorities! 🌡️
            </p>
          </div>
        </motion.div>

        {/* Fun Stats */}
        <motion.div
          className="grid grid-cols-1 sm:grid-cols-3 gap-6 max-w-3xl mx-auto"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <div className="bg-gradient-to-br from-red-900/30 to-red-800/30 border border-red-500/30 rounded-xl p-4">
            <div className="text-3xl font-bold text-red-300">200+</div>
            <div className="text-sm text-slate-400">Projects Completed</div>
            <div className="text-xs text-slate-500">(But who's counting? 😉)</div>
          </div>
          <div className="bg-gradient-to-br from-red-900/30 to-red-800/30 border border-red-500/30 rounded-xl p-4">
            <div className="text-3xl font-bold text-red-300">15+</div>
            <div className="text-sm text-slate-400">Years Experience</div>
            <div className="text-xs text-slate-500">(Still learning new tricks!)</div>
          </div>
          <div className="bg-gradient-to-br from-red-900/30 to-red-800/30 border border-red-500/30 rounded-xl p-4">
            <div className="text-3xl font-bold text-red-300">∞</div>
            <div className="text-sm text-slate-400">Happy Clients</div>
            <div className="text-xs text-slate-500">(Math is hard 🤓)</div>
          </div>
        </motion.div>

        {/* Progress Indicator */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 1 }}
        >
          <p className="text-slate-400 text-lg">
            Portfolio completion status: In progress (Like all good engineering projects! 🔧)
          </p>
          
          <div className="w-full max-w-md mx-auto bg-slate-800 rounded-full h-3 overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-red-500 to-red-600"
              initial={{ width: "0%" }}
              animate={{ width: "67%" }}
              transition={{ duration: 2.5, delay: 1.2, ease: "easeOut" }}
            />
          </div>
          <p className="text-sm text-slate-500">67% Complete - Quality takes time! 🏆</p>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          className="pt-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1.2 }}
        >
          <div className="flex items-center justify-center space-x-2 mb-4">
            <Trophy className="h-5 w-5 text-red-400" />
            <p className="text-slate-400">
              Want to see our work? Drive around Doha - you've probably been in one of our buildings!
            </p>
          </div>
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-6">
            <div className="flex items-center space-x-2 text-red-300">
              <span>📞</span>
              <span className="font-medium">+974 70300401</span>
            </div>
            <div className="flex items-center space-x-2 text-red-300">
              <span>🌐</span>
              <span className="font-medium">auburnengineering.com</span>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
} 
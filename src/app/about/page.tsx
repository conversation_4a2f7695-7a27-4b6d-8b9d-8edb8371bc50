'use client'

import { motion } from "framer-motion";
import { Award, Calendar, Users, MapPin, Target, Eye, Heart } from "lucide-react";
import GradientText from "@/components/gradient-text";
import { AnimatedFeatureCard } from "@/components/animated-card";

export default function AboutPage() {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  return (
    <div className="relative min-h-screen">
      <div className="absolute inset-0 bg-gradient-to-br from-red-950/20 via-black to-red-950/20" />
      
      {/* Hero Section */}
      <section className="relative z-10 py-20 px-4">
        <motion.div 
          className="max-w-4xl mx-auto text-center"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.h1 
            className="text-5xl sm:text-6xl md:text-7xl font-black mb-8"
            variants={itemVariants}
          >
            <GradientText gradient="from-red-200 to-red-400">
              About Auburn Engineering
            </GradientText>
          </motion.h1>
          
          <motion.p 
            className="text-xl sm:text-2xl text-slate-300 leading-relaxed"
            variants={itemVariants}
          >
            Building Qatar's future through innovative engineering solutions and unwavering commitment to excellence.
          </motion.p>
        </motion.div>
      </section>

      {/* Company Story */}
      <section className="relative z-10 py-20 px-4">
        <motion.div 
          className="max-w-7xl mx-auto"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <div className="glass rounded-3xl p-8 md:p-12 border border-red-500/20">
            <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
              <motion.div variants={itemVariants}>
                <h2 className="text-3xl sm:text-4xl font-bold mb-6">
                  <GradientText gradient="from-red-200 to-red-400">
                    Our Story
                  </GradientText>
                </h2>
                <div className="space-y-4 text-slate-300 text-lg leading-relaxed">
                  <p>
                    Founded in 2013, Auburn Engineering WLL has grown from a small engineering consultancy 
                    to a trusted leader in Qatar's ACMV and engineering sector. Our journey began with a 
                    simple vision: to provide world-class mechanical ventilation systems and engineering 
                    solutions that exceed client expectations.
                  </p>
                  <p>
                    Over the past 15 years, we have successfully completed numerous installation projects 
                    and provided comprehensive Annual Maintenance Contracts (AMCs) across diverse sectors 
                    including hospitality, hospitals, commercial buildings, shopping malls, schools, and data centers.
                  </p>
                  <p>
                    Today, Auburn Engineering stands as a certified leader with Qatar Civil Defense and 
                    Kahramaa certifications, employing over 200 skilled professionals dedicated to 
                    delivering innovative and reliable engineering solutions.
                  </p>
                </div>
              </motion.div>

              <motion.div 
                className="grid grid-cols-2 gap-6"
                variants={itemVariants}
              >
                {[
                  { icon: Calendar, number: "2013", label: "Founded" },
                  { icon: Users, number: "200+", label: "Team Members" },
                  { icon: Award, number: "1000+", label: "Projects" },
                  { icon: MapPin, number: "100%", label: "Qatar Based" }
                ].map((stat, index) => (
                  <div 
                    key={index}
                    className="glass rounded-2xl p-6 text-center border border-red-500/20"
                  >
                    <div className="flex justify-center mb-3">
                      <div className="p-3 rounded-xl bg-gradient-to-r from-red-600/20 to-red-700/20">
                        <stat.icon className="h-6 w-6 text-red-400" />
                      </div>
                    </div>
                    <div className="text-2xl font-bold text-white mb-1">{stat.number}</div>
                    <div className="text-slate-400 text-sm">{stat.label}</div>
                  </div>
                ))}
              </motion.div>
            </div>
          </div>
        </motion.div>
      </section>

      {/* Mission, Vision, Values */}
      <section className="relative z-10 py-20 px-4">
        <motion.div 
          className="max-w-7xl mx-auto"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <motion.div className="text-center mb-16" variants={itemVariants}>
            <h2 className="text-4xl sm:text-5xl font-black mb-6">
              <GradientText gradient="from-red-200 to-red-400">
                Our Foundation
              </GradientText>
            </h2>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <AnimatedFeatureCard
              icon={<Target className="h-8 w-8" />}
              title="Our Mission"
              description="To provide innovative, reliable, and efficient ACMV and engineering solutions that enhance the quality of life and business operations across Qatar."
              delay={0.1}
            />

            <AnimatedFeatureCard
              icon={<Eye className="h-8 w-8" />}
              title="Our Vision"
              description="To be the leading engineering consultancy in Qatar, recognized for excellence, innovation, and sustainable solutions that shape the future."
              delay={0.2}
            />

            <AnimatedFeatureCard
              icon={<Heart className="h-8 w-8" />}
              title="Our Values"
              description="Excellence, integrity, innovation, sustainability, and client satisfaction drive everything we do. We believe in building lasting partnerships."
              delay={0.3}
            />
          </div>
        </motion.div>
      </section>

      {/* Certifications */}
      <section className="relative z-10 py-20 px-4">
        <motion.div 
          className="max-w-7xl mx-auto"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <div className="glass rounded-3xl p-8 md:p-12 border border-red-500/20">
            <motion.div className="text-center mb-12" variants={itemVariants}>
              <h2 className="text-3xl sm:text-4xl font-bold mb-4">
                <GradientText gradient="from-red-200 to-red-400">
                  Certifications & Expertise
                </GradientText>
              </h2>
              <p className="text-xl text-slate-300">
                Our qualifications ensure the highest standards of service and compliance.
              </p>
            </motion.div>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              {[
                "Qatar Civil Defense Certified",
                "Kahramaa Approved",
                "QCD Inspector Qualified",
                "ISO 9001:2015 Compliant",
                "ACMV Specialists",
                "Generator Service Certified",
                "Electrical Systems Expert",
                "Project Management Professional"
              ].map((cert, index) => (
                <motion.div
                  key={index}
                  className="glass rounded-xl p-4 text-center border border-red-500/20 hover:border-red-500/40 transition-all duration-300"
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  whileHover={{ y: -2 }}
                >
                  <div className="flex justify-center mb-3">
                    <Award className="h-8 w-8 text-red-400" />
                  </div>
                  <h3 className="text-white font-medium text-sm">{cert}</h3>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      </section>

      {/* Location */}
      <section className="relative z-10 py-20 px-4">
        <motion.div 
          className="max-w-4xl mx-auto text-center"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <div className="glass rounded-3xl p-8 md:p-12 border border-red-500/20">
            <motion.h2 
              className="text-3xl sm:text-4xl font-bold mb-6"
              variants={itemVariants}
            >
              <GradientText gradient="from-red-200 to-red-400">
                Proudly Based in Qatar
              </GradientText>
            </motion.h2>
            
            <motion.div 
              className="space-y-4 text-slate-300 text-lg"
              variants={itemVariants}
            >
              <div className="flex items-center justify-center space-x-2 mb-4">
                <MapPin className="h-6 w-6 text-red-400" />
                <span className="font-semibold">Al Munthaza, B-Ring Road</span>
              </div>
              <p>Near Jaidah Flyover, 501, 5th Floor Retaj Building</p>
              <p>Doha, Qatar 22191</p>
              <div className="pt-4 border-t border-red-500/20 mt-6">
                <p className="text-slate-400">
                  Strategically located in the heart of Doha, we serve clients across Qatar 
                  with quick response times and comprehensive support.
                </p>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </section>
    </div>
  );
} 
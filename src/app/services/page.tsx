'use client'

import { motion } from 'framer-motion';
import { <PERSON><PERSON>, <PERSON>, Clock, Lightbulb } from 'lucide-react';
import GradientText from '@/components/gradient-text';

export default function ServicesPage() {
  return (
    <div className="min-h-screen flex items-center justify-center px-4">
      <div className="absolute inset-0 bg-gradient-to-br from-red-950/20 via-black to-red-950/20" />
      
      <motion.div 
        className="text-center space-y-8 max-w-4xl mx-auto relative z-10"
        initial={{ opacity: 0, y: 50 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
      >
        {/* Main Title */}
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.2 }}
        >
          <h1 className="text-5xl sm:text-6xl md:text-7xl font-black mb-6">
            <GradientText gradient="from-red-200 to-red-400">
              Our Services
            </GradientText>
          </h1>
        </motion.div>

        {/* Animated Icons */}
        <motion.div 
          className="flex justify-center space-x-8 mb-8"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.4 }}
        >
          <motion.div
            animate={{ rotate: [0, 10, -10, 0] }}
            transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
          >
            <Wrench className="h-12 w-12 text-red-400" />
          </motion.div>
          <motion.div
            animate={{ y: [0, -10, 0] }}
            transition={{ duration: 2, repeat: Infinity, repeatDelay: 1 }}
          >
            <Lightbulb className="h-12 w-12 text-red-300" />
          </motion.div>
          <motion.div
            animate={{ rotate: [0, 360] }}
            transition={{ duration: 3, repeat: Infinity, ease: "linear" }}
          >
            <Clock className="h-12 w-12 text-red-500" />
          </motion.div>
        </motion.div>

        {/* Humorous Message */}
        <motion.div
          className="space-y-6"
          initial={{ opacity: 0, y: 30 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 0.6 }}
        >
          <div className="text-6xl mb-4">🚧</div>
          
          <h2 className="text-3xl sm:text-4xl font-bold text-white mb-4">
            Our Engineers Are Busy Building Something Amazing!
          </h2>
          
          <p className="text-xl sm:text-2xl text-slate-300 leading-relaxed max-w-3xl mx-auto">
            While our ACMV systems keep Qatar cool, our web developers are heating up their keyboards 
            to bring you an epic services showcase. 
          </p>
          
          <div className="bg-red-900/20 border border-red-500/30 rounded-2xl p-6 max-w-2xl mx-auto">
            <div className="flex items-center justify-center space-x-3 mb-4">
              <Coffee className="h-6 w-6 text-red-400" />
              <span className="text-lg font-semibold text-white">Meanwhile...</span>
            </div>
            <p className="text-slate-300">
              Our team is perfecting every pixel, optimizing every animation, and ensuring 
              this page will be as reliable as our 15+ years of engineering excellence in Qatar!
            </p>
          </div>
        </motion.div>

        {/* Progress Indicator */}
        <motion.div
          className="space-y-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.8, delay: 0.8 }}
        >
          <p className="text-slate-400 text-lg">
            Expected completion: When it's ready (Engineering precision takes time! ⚡)
          </p>
          
          <div className="w-full max-w-md mx-auto bg-slate-800 rounded-full h-3 overflow-hidden">
            <motion.div
              className="h-full bg-gradient-to-r from-red-500 to-red-600"
              initial={{ width: "0%" }}
              animate={{ width: "73%" }}
              transition={{ duration: 2, delay: 1, ease: "easeOut" }}
            />
          </div>
          <p className="text-sm text-slate-500">73% Complete - Almost there! 🎯</p>
        </motion.div>

        {/* Call to Action */}
        <motion.div
          className="pt-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.8, delay: 1 }}
        >
          <p className="text-slate-400 mb-4">
            Need our services right now? We're still the same reliable Auburn Engineering!
          </p>
          <div className="flex flex-col sm:flex-row items-center justify-center space-y-3 sm:space-y-0 sm:space-x-6">
            <div className="flex items-center space-x-2 text-red-300">
              <span>📞</span>
              <span className="font-medium">+974 70300401</span>
            </div>
            <div className="flex items-center space-x-2 text-red-300">
              <span>🌐</span>
              <span className="font-medium">auburnengineering.com</span>
            </div>
          </div>
        </motion.div>
      </motion.div>
    </div>
  );
} 
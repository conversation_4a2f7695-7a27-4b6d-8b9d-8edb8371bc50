"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { calculateChecklistSummary } from "@/lib";
import { StorageService } from "@/lib/services/storage/storage-adapter";
import { ChecklistData } from "@/types/checklist";
import { exportToExcel, exportToPDF } from "@/lib/services/export/export";
import { AuthGuard, useAuth, ApprovalMessage } from "@/components/auth";


import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertD<PERSON>og<PERSON><PERSON>le,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Edit,
  Trash2,
  FileSpreadsheet,
  FileText,
  Plus,
  Loader2,
  Building,
  Tag,
  CheckCircle,
  Lock,
  Database,
} from "lucide-react";


function SavedInspectionsPageContent() {
  const router = useRouter();
  const { user } = useAuth();
  const [checklists, setChecklists] = useState<ChecklistData[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [exportingStates, setExportingStates] = useState<Record<string, { pdf: boolean; excel: boolean }>>({});
  const [completingStates, setCompletingStates] = useState<Record<string, boolean>>({});

  useEffect(() => {
    loadChecklists();
  }, [user]);

  // Listen for checklist updates
  useEffect(() => {
    const handleChecklistsUpdated = () => {
      loadChecklists();
    };

    window.addEventListener('checklistsUpdated', handleChecklistsUpdated);
    
    return () => {
      window.removeEventListener('checklistsUpdated', handleChecklistsUpdated);
    };
  }, []);

  const loadChecklists = async () => {
    setIsLoading(true);
    try {
      if (user) {
        // Use safe storage operation to handle initialization
        const { StorageService } = await import('@/lib/services/storage/storage-adapter');
        
        // Check if StorageService is initialized, if not wait a bit and retry
        if (!StorageService.isInitialized()) {
          // Wait for initialization with increased timeout for Firebase Storage
          let retries = 0;
          const maxRetries = 25; // 5 seconds total wait (increased from 2 seconds)
          while (!StorageService.isInitialized() && retries < maxRetries) {
            await new Promise(resolve => setTimeout(resolve, 200));
            retries++;
          }
          
          if (!StorageService.isInitialized()) {
            throw new Error('Storage service failed to initialize after waiting');
          }
        }
        
        // Only show checklists for the authenticated user
        const allChecklists = await StorageService.getAllChecklists();
        const userChecklists = allChecklists.filter(c => c.userId === user.uid);
        setChecklists(userChecklists);
      } else {
        setChecklists([]);
      }
    } catch (error) {
      console.error('Failed to load checklists:', error);
      setChecklists([]);
    }
    setIsLoading(false);
  };

  const handleDelete = async (id: string) => {
    const success = await StorageService.deleteChecklist(id);
    if (success) {
      // Optimistically update the local state instead of refetching all checklists
      setChecklists(prevChecklists => prevChecklists.filter(c => c.id !== id));
    } else {
      alert("Failed to delete checklist. Please try again.");
    }
  };

  const handleClearAll = async () => {
    try {
      const success = await StorageService.clearAllChecklists();
    if (success) {
      // Optimistically update the local state instead of refetching all checklists
      setChecklists([]);
    } else {
        alert("Failed to clear all checklists. Please try again.");
      }
    } catch (error) {
      console.error('Failed to clear checklists:', error);
      alert("Failed to clear all checklists. Please try again.");
    }
  };

  const handleExport = async (checklist: ChecklistData, type: 'excel' | 'pdf') => {
    try {
      setExportingStates(prev => ({
        ...prev,
        [checklist.id]: {
          ...prev[checklist.id],
          [type]: true
        }
      }));

      if (type === 'excel') {
        await exportToExcel(checklist);
      } else {
        await exportToPDF(checklist);
      }
    } catch (error) {
      console.error(`Export ${type} failed:`, error);
      alert(`Failed to export ${type.toUpperCase()} file. Please try again.`);
    } finally {
      setExportingStates(prev => ({
        ...prev,
        [checklist.id]: {
          ...prev[checklist.id],
          [type]: false
        }
      }));
    }
  };

  const handleMarkAsCompleted = async (checklist: ChecklistData) => {
    if (!user) return;

    // Set loading state for this specific checklist
    setCompletingStates(prev => ({ ...prev, [checklist.id]: true }));

    try {
      const updatedChecklist = {
        ...checklist,
        isCompleted: true,
        completedAt: new Date().toISOString()
      };

      const success = await StorageService.saveChecklist(updatedChecklist);
      if (success) {
        // Optimistically update the local state instead of refetching all checklists
        setChecklists(prevChecklists => 
          prevChecklists.map(c => 
            c.id === checklist.id ? updatedChecklist : c
          )
        );
        alert("Checklist marked as completed!");
      } else {
        alert("Failed to mark as completed. Please try again.");
      }
    } catch (error) {
      console.error("Failed to mark as completed:", error);
      alert("Failed to mark as completed. Please try again.");
    } finally {
      // Clear loading state
      setCompletingStates(prev => ({ ...prev, [checklist.id]: false }));
    }
  };

  const getStatusBadge = (checklist: ChecklistData) => {
    if (checklist.isCompleted) {
      return (
        <Badge variant="outline" className="text-purple-600 border-purple-600">
          <Lock className="h-3 w-3 mr-1" />
          Completed
        </Badge>
      );
    }
    
    return (
      <Badge variant="outline" className="text-green-600 border-green-600">
        <CheckCircle className="h-3 w-3 mr-1" />
        Active
      </Badge>
    );
  };



  const formatDate = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString();
    } catch {
      return dateString;
    }
  };

  // Mobile Card Component
  const InspectionCard = ({ checklist }: { checklist: ChecklistData }) => {
    const summary = calculateChecklistSummary(checklist);
    
    return (
      <Card className="w-full">
        <CardHeader className="pb-3">
          <div className="flex items-start justify-between">
            <div className="space-y-1">
              <CardTitle className="text-lg flex items-center gap-2">
                <Tag className="h-4 w-4" />
                {checklist.generalInfo.tagNo}
                {checklist.isCompleted && (
                  <div title="Completed - Admin access only">
                    <Lock className="h-4 w-4 text-purple-600" />
                  </div>
                )}
              </CardTitle>
              <div className="flex items-center gap-1 text-sm text-muted-foreground">
                <Building className="h-3 w-3" />
                {checklist.generalInfo.clientName}
              </div>
            </div>
            <div className="flex gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => router.push(`/checklist?edit=${checklist.id}`)}
                disabled={checklist.isCompleted}
                title={checklist.isCompleted ? "Cannot edit completed checklists" : "Edit checklist"}
              >
                <Edit className="h-4 w-4" />
              </Button>
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    disabled={checklist.isCompleted}
                    title={checklist.isCompleted ? "Cannot delete completed checklists" : "Delete checklist"}
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Delete Inspection</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete this inspection? This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={() => handleDelete(checklist.id)}>
                      Delete
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-3 text-sm">
            <div className="flex items-center gap-2">
              <Building className="h-4 w-4 text-muted-foreground" />
              <span className="text-muted-foreground">Building:</span>
              <span>{checklist.generalInfo.building}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground">Date:</span>
              <span>{formatDate(checklist.generalInfo.date)}</span>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-muted-foreground">Inspector:</span>
              <span>{checklist.generalInfo.inspectedBy}</span>
            </div>
          </div>
          
          <div className="space-y-2">
            <div className="text-sm text-muted-foreground">Status Summary:</div>
            <div className="flex gap-1 flex-wrap">
              <Badge variant="outline" className="text-green-500 border-green-500/30 dark:text-green-400 dark:border-green-400/30">
                {summary.totalOk} OK
              </Badge>
              {summary.totalFaulty > 0 && (
                <Badge variant="outline" className="text-red-500 border-red-500/30 dark:text-red-400 dark:border-red-400/30">
                  {summary.totalFaulty} Faulty
                </Badge>
              )}
              {summary.totalNA > 0 && (
                <Badge variant="outline" className="text-slate-500 border-slate-500/30 dark:text-slate-400 dark:border-slate-400/30">
                  {summary.totalNA} N/A
                </Badge>
              )}
              {summary.totalMissing > 0 && (
                <Badge variant="outline" className="text-amber-500 border-amber-500/30 dark:text-amber-400 dark:border-amber-400/30">
                  {summary.totalMissing} Missing
                </Badge>
              )}
            </div>
          </div>

          <div className="space-y-2">
            <div className="text-sm text-muted-foreground">Status:</div>
            <div className="flex items-center justify-between">
              {getStatusBadge(checklist)}
              {!checklist.isCompleted && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => handleMarkAsCompleted(checklist)}
                  disabled={completingStates[checklist.id]}
                  title="Mark as completed"
                >
                  {completingStates[checklist.id] ? (
                    <>
                      <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                      Completing...
                    </>
                  ) : (
                    <>
                      <CheckCircle className="h-3 w-3 mr-1" />
                      Complete
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>
          
          <div className="flex gap-2 pt-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport(checklist, 'excel')}
              disabled={exportingStates[checklist.id]?.excel}
              className="flex-1"
            >
              {exportingStates[checklist.id]?.excel ? (
                <Loader2 className="h-3 w-3 animate-spin mr-1" />
              ) : (
                <FileSpreadsheet className="h-3 w-3 mr-1" />
              )}
              Excel
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleExport(checklist, 'pdf')}
              disabled={exportingStates[checklist.id]?.pdf}
              className="flex-1"
            >
              {exportingStates[checklist.id]?.pdf ? (
                <Loader2 className="h-3 w-3 animate-spin mr-1" />
              ) : (
                <FileText className="h-3 w-3 mr-1" />
              )}
              PDF
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  };

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <Loader2 className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto space-y-6 p-4">
      {/* Approval Message for Users */}
      <ApprovalMessage />
      
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl sm:text-3xl font-bold">Saved Inspections</h1>
          <p className="text-muted-foreground">
            Manage your saved inspection checklists
          </p>
        </div>

        <div className="flex justify-end">
          <Link href="/checklist">
            <Button className="flex items-center gap-2">
              <Plus className="h-4 w-4" />
              New Inspection
            </Button>
          </Link>
        </div>
      </div>

      {checklists.length === 0 ? (
        <Card className="text-center p-8">
          <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
            <Database className="h-8 w-8 text-muted-foreground" />
          </div>
          <h3 className="text-lg font-semibold mb-2">No inspections found</h3>
          <p className="text-muted-foreground mb-4">
            You haven&apos;t saved any inspection checklists yet.
          </p>
          <Link href="/checklist">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Create Your First Inspection
            </Button>
          </Link>
        </Card>
      ) : (
        <>
          {/* Stats Cards */}
          <div className="grid grid-cols-2 md:grid-cols-6 gap-4">
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold">{checklists.length}</div>
                <p className="text-xs text-muted-foreground">Total Inspections</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-purple-600">
                  {checklists.filter(c => c.isCompleted).length}
                </div>
                <p className="text-xs text-muted-foreground">Completed</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="p-4">
                <div className="text-2xl font-bold text-green-600">
                  {checklists.filter(c => !c.isCompleted).length}
                </div>
                <p className="text-xs text-muted-foreground">Active</p>
              </CardContent>
            </Card>
          </div>



          {/* Mobile View */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 md:hidden">
            {checklists.map((checklist) => (
              <InspectionCard key={checklist.id} checklist={checklist} />
            ))}
          </div>

          {/* Desktop Table View */}
          <div className="hidden md:block">
            <Card>
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Tag No.</TableHead>
                    <TableHead>Client</TableHead>
                    <TableHead>Building</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead>Inspector</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Results</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {checklists.map((checklist) => {
                    const summary = calculateChecklistSummary(checklist);
                    
                    return (
                      <TableRow key={checklist.id}>
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            {checklist.generalInfo.tagNo}
                            {checklist.isCompleted && (
                              <div title="Completed - Admin access only">
                                <Lock className="h-4 w-4 text-purple-600" />
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>{checklist.generalInfo.clientName}</TableCell>
                        <TableCell>{checklist.generalInfo.building}</TableCell>
                        <TableCell>{formatDate(checklist.generalInfo.date)}</TableCell>
                        <TableCell>{checklist.generalInfo.inspectedBy}</TableCell>
                        <TableCell>{getStatusBadge(checklist)}</TableCell>
                        <TableCell>
                          <div className="flex gap-1">
                            <Badge variant="outline" className="text-green-600 border-green-600">
                              {summary.totalOk} OK
                            </Badge>
                            {summary.totalFaulty > 0 && (
                              <Badge variant="outline" className="text-red-600 border-red-600">
                                {summary.totalFaulty} F
                              </Badge>
                            )}
                          </div>
                        </TableCell>
                        <TableCell className="text-right">
                          <div className="flex items-center justify-end gap-1">
                            {!checklist.isCompleted && (
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => handleMarkAsCompleted(checklist)}
                                disabled={completingStates[checklist.id]}
                                title="Mark as completed"
                                className="text-purple-600 hover:text-purple-700"
                              >
                                {completingStates[checklist.id] ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <CheckCircle className="h-4 w-4" />
                                )}
                              </Button>
                            )}
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleExport(checklist, 'excel')}
                              disabled={exportingStates[checklist.id]?.excel}
                              title="Export to Excel"
                            >
                              {exportingStates[checklist.id]?.excel ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <FileSpreadsheet className="h-4 w-4" />
                              )}
                            </Button>
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleExport(checklist, 'pdf')}
                              disabled={exportingStates[checklist.id]?.pdf}
                              title="Export to PDF"
                            >
                              {exportingStates[checklist.id]?.pdf ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <FileText className="h-4 w-4" />
                              )}
                            </Button>
                            
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => router.push(`/checklist?edit=${checklist.id}`)}
                              disabled={checklist.isCompleted}
                              title={checklist.isCompleted ? "Cannot edit completed checklists" : "Edit checklist"}
                            >
                              <Edit className="h-4 w-4" />
                            </Button>
                            
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <Button 
                                  variant="ghost" 
                                  size="sm"
                                  disabled={checklist.isCompleted}
                                  title={checklist.isCompleted ? "Cannot delete completed checklists" : "Delete checklist"}
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Delete Inspection</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete this inspection? This action cannot be undone.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction onClick={() => handleDelete(checklist.id)}>
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </div>
                        </TableCell>
                      </TableRow>
                    );
                  })}
                </TableBody>
              </Table>
            </Card>
          </div>

          {/* Clear All Button */}
          {checklists.length > 0 && (
            <div className="flex justify-center">
              <AlertDialog>
                <AlertDialogTrigger asChild>
                  <Button variant="outline" className="text-destructive">
                    <Trash2 className="mr-2 h-4 w-4" />
                    Clear All Inspections
                  </Button>
                </AlertDialogTrigger>
                <AlertDialogContent>
                  <AlertDialogHeader>
                    <AlertDialogTitle>Clear All Inspections</AlertDialogTitle>
                    <AlertDialogDescription>
                      Are you sure you want to delete all saved inspections? This action cannot be undone.
                    </AlertDialogDescription>
                  </AlertDialogHeader>
                  <AlertDialogFooter>
                    <AlertDialogCancel>Cancel</AlertDialogCancel>
                    <AlertDialogAction onClick={handleClearAll}>
                      Clear All
                    </AlertDialogAction>
                  </AlertDialogFooter>
                </AlertDialogContent>
              </AlertDialog>
            </div>
          )}
        </>
      )}
      

    </div>
  );
}

export default function SavedInspectionsPage() {
  return (
    <AuthGuard>
      <SavedInspectionsPageContent />
    </AuthGuard>
  );
} 
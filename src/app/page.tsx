'use client'

import Link from "next/link";
import { motion } from "framer-motion";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { 
  Building2, 
  <PERSON><PERSON>, 
  <PERSON><PERSON>, 
  Users, 
  CheckCircle, 
  Award, 
  MapPin, 
  Phone, 
  Calendar,
  ArrowRight,
  Gauge,
  Shield,
  Settings,
  TrendingUp
} from "lucide-react";
import ThreeBackground from "@/components/three-background";
import GradientText from "@/components/gradient-text";
import { AnimatedFeatureCard } from "@/components/animated-card";

export default function HomePage() {
  
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: "easeOut"
      }
    }
  }

  return (
    <div className="relative">
      <ThreeBackground />
      
      {/* Hero Section */}
      <section className="relative z-10 min-h-screen flex items-start justify-center pt-24 sm:pt-32 lg:pt-40 pb-16">
        <motion.div 
          className="container mx-auto text-center space-content"
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <motion.div
            className="space-y-6"
            variants={itemVariants}
          >
            <h1 className="heading-xl">
              <span className="block text-red-gradient">
                Auburn
              </span>
              <span className="block text-slate-200">
                Engineering
              </span>
            </h1>
            
            <motion.div
              className="h-1 w-32 mx-auto bg-red-gradient rounded-full"
              initial={{ width: 0 }}
              animate={{ width: 128 }}
              transition={{ duration: 1, delay: 0.5 }}
            />
          </motion.div>

          <motion.div
            className="space-y-4"
            variants={itemVariants}
          >
            <p className="text-xl sm:text-2xl md:text-3xl text-slate-300 max-w-4xl mx-auto leading-relaxed">
              Leading ACMV & Engineering Solutions in Qatar
            </p>
            
            <p className="text-lg text-slate-400 max-w-3xl mx-auto">
              Trusted leader in Mechanical Ventilation Systems for over 15 years, 
              serving hospitality, hospitals, commercial buildings, and data centers across Qatar.
            </p>
          </motion.div>

          <motion.div 
            className="flex flex-col sm:flex-row gap-4 justify-center pt-4"
            variants={itemVariants}
          >
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button 
                asChild 
                size="lg" 
                className="btn-primary rounded-xl px-8 py-4 text-lg font-semibold"
              >
                <Link href="/services">
                  Our Services
                  <ArrowRight className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </motion.div>
            
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button 
                asChild 
                variant="outline" 
                size="lg" 
                className="btn-secondary rounded-xl px-8 py-4 text-lg font-semibold"
              >
                <Link href="/contact">
                  Get In Touch
                  <Phone className="ml-2 h-5 w-5" />
                </Link>
              </Button>
            </motion.div>
          </motion.div>
        </motion.div>
      </section>

      {/* Company Stats */}
      <section className="relative z-10 section-padding">
        <motion.div 
          className="container mx-auto"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <div className="card-modern">
            <div className="grid grid-cols-2 lg:grid-cols-4 gap-6 lg:gap-8">
              {[
                { number: "15+", label: "Years of Excellence", icon: Calendar, delay: 0.1 },
                { number: "200+", label: "Team Members", icon: Users, delay: 0.2 },
                { number: "1000+", label: "Projects Completed", icon: CheckCircle, delay: 0.3 },
                { number: "100%", label: "QCD Certified", icon: Award, delay: 0.4 }
              ].map((stat, index) => (
                <motion.div
                  key={index}
                  className="text-center group"
                  initial={{ opacity: 0, scale: 0.5 }}
                  whileInView={{ opacity: 1, scale: 1 }}
                  viewport={{ once: true }}
                  transition={{ duration: 0.5, delay: stat.delay }}
                  whileHover={{ scale: 1.05 }}
                >
                  <div className="flex justify-center mb-4">
                    <div className={`p-3 rounded-xl ${
                      index === 0 ? 'bg-gradient-to-r from-red-700/20 to-red-600/20 border border-red-500/30' :
                      index === 1 ? 'bg-gradient-to-r from-blue-700/20 to-blue-600/20 border border-blue-500/30' :
                      index === 2 ? 'bg-gradient-to-r from-green-700/20 to-green-600/20 border border-green-500/30' :
                      'bg-gradient-to-r from-amber-700/20 to-amber-600/20 border border-amber-500/30'
                    }`}>
                      <stat.icon className={`h-6 w-6 ${
                        index === 0 ? 'text-red-400' :
                        index === 1 ? 'text-blue-400' :
                        index === 2 ? 'text-green-400' :
                        'text-amber-400'
                      }`} />
                    </div>
                  </div>
                  <div className={`text-3xl sm:text-4xl font-bold mb-2 ${
                    index === 0 ? 'text-red-gradient' :
                    index === 1 ? 'text-blue-gradient' :
                    index === 2 ? 'text-green-gradient' :
                    'bg-gradient-to-r from-amber-500 to-amber-400 bg-clip-text text-transparent'
                  }`}>
                    {stat.number}
                  </div>
                  <div className="text-slate-400 text-sm sm:text-base font-medium">
                    {stat.label}
                  </div>
                </motion.div>
              ))}
            </div>
          </div>
        </motion.div>
      </section>

      {/* Services Overview */}
      <section className="relative z-10 section-padding">
        <motion.div 
          className="container mx-auto"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <motion.div className="text-center mb-12" variants={itemVariants}>
            <h2 className="heading-lg mb-6">
              <span className="text-red-gradient">Our Expertise</span>
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto">
              Comprehensive engineering solutions across multiple sectors with Qatar Civil Defense and Kahramaa certifications.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            <AnimatedFeatureCard
              icon={<Building2 className="h-8 w-8" />}
              title="ACMV Systems"
              description="Complete mechanical ventilation solutions including installation, maintenance, and QCD inspections for commercial and residential projects."
              delay={0.1}
            />

            <AnimatedFeatureCard
              icon={<Zap className="h-8 w-8" />}
              title="Generator Services"
              description="Installation, service, and maintenance of generators and fuel pumps with comprehensive testing and certification services."
              delay={0.2}
            />

            <AnimatedFeatureCard
              icon={<Settings className="h-8 w-8" />}
              title="Electrical Systems"
              description="LV/ELV panels, UPS systems, central battery systems, and capacitor bank testing with energy audit capabilities."
              delay={0.3}
            />

            <AnimatedFeatureCard
              icon={<Shield className="h-8 w-8" />}
              title="QCD Compliance"
              description="Certified QCD inspections for ACMV systems and generators by our qualified engineering team."
              delay={0.4}
            />

            <AnimatedFeatureCard
              icon={<TrendingUp className="h-8 w-8" />}
              title="Project Management"
              description="Comprehensive project management support and lean six sigma implementation for process optimization."
              delay={0.5}
            />

            <AnimatedFeatureCard
              icon={<Gauge className="h-8 w-8" />}
              title="Testing & Commissioning"
              description="Complete testing and commissioning services for all mechanical and electrical systems with detailed reporting."
              delay={0.6}
            />
          </div>
        </motion.div>
      </section>

      {/* Why Choose Us */}
      <section className="relative z-10 section-padding">
        <motion.div 
          className="container mx-auto"
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true }}
        >
          <motion.div className="text-center mb-12" variants={itemVariants}>
            <h2 className="heading-lg mb-6">
              <span className="text-red-gradient">Why Choose Auburn Engineering</span>
            </h2>
            <p className="text-xl text-slate-300 max-w-3xl mx-auto">
              Your trusted partner for comprehensive engineering solutions in Qatar
            </p>
          </motion.div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
            <motion.div 
              className="space-content"
              variants={itemVariants}
            >
              <div className="space-y-6">
                {[
                  {
                    icon: <Award className="h-6 w-6" />,
                    title: "QCD & Kahramaa Certified",
                    description: "Fully certified by Qatar Civil Defense and Kahramaa for all our services"
                  },
                  {
                    icon: <Users className="h-6 w-6" />,
                    title: "Expert Team",
                    description: "200+ skilled professionals with extensive experience in Qatar market"
                  },
                  {
                    icon: <CheckCircle className="h-6 w-6" />,
                    title: "Proven Track Record",
                    description: "Over 1000 successful projects across hospitality, healthcare, and commercial sectors"
                  },
                  {
                    icon: <Phone className="h-6 w-6" />,
                    title: "24/7 Support",
                    description: "Round-the-clock emergency support and maintenance services"
                  }
                ].map((feature, index) => (
                  <motion.div
                    key={index}
                    className="flex items-start space-x-4 p-4 rounded-xl glass-light border border-red-500/10 hover:border-red-500/20 transition-all duration-300"
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    viewport={{ once: true }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    whileHover={{ scale: 1.02 }}
                  >
                    <div className="flex-shrink-0 p-2 rounded-lg bg-gradient-to-r from-red-700/20 to-red-600/20 border border-red-500/30">
                      <div className="text-red-400">
                        {feature.icon}
                      </div>
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold text-white mb-2">{feature.title}</h3>
                      <p className="text-slate-400">{feature.description}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </motion.div>

            <motion.div 
              className="relative"
              variants={itemVariants}
            >
              <div className="card-modern text-center">
                <div className="mb-6">
                  <div className="w-20 h-20 mx-auto bg-red-gradient rounded-full flex items-center justify-center mb-4">
                    <Building2 className="h-10 w-10 text-white" />
                  </div>
                  <h3 className="heading-md text-red-gradient mb-4">Ready to Start Your Project?</h3>
                  <p className="text-slate-300 mb-6">
                    Get in touch with our expert team for a consultation and quote tailored to your specific requirements.
                  </p>
                </div>
                
                <div className="space-y-4">
                  <div className="flex items-center justify-center space-x-3 text-slate-300">
                    <Phone className="h-5 w-5 text-red-400" />
                    <span className="font-medium">+974 7030 0401</span>
                  </div>
                  <div className="flex items-center justify-center space-x-3 text-slate-300">
                    <MapPin className="h-5 w-5 text-red-400" />
                    <span>Doha, Qatar</span>
                  </div>
                  
                  <motion.div
                    whileHover={{ scale: 1.05 }}
                    whileTap={{ scale: 0.95 }}
                    className="pt-4"
                  >
                    <Button 
                      asChild 
                      className="btn-primary w-full rounded-xl"
                    >
                      <Link href="/contact">
                        Contact Us Today
                        <ArrowRight className="ml-2 h-5 w-5" />
                      </Link>
                    </Button>
                  </motion.div>
                </div>
              </div>
            </motion.div>
          </div>
        </motion.div>
      </section>
    </div>
  );
}

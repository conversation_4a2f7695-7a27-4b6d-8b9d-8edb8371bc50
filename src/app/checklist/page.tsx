"use client";

import { useState, useEffect, Suspense, useMemo, useRef } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { ChecklistFormData, checklistSchema } from "@/lib/utils/validation";
import { ChecklistData } from "@/types/checklist";
import { EquipmentTag } from "@/types/equipment-tag";
import { useStorageService } from "@/hooks/useStorageService";
import { 
  MECHANICAL_CHECKS, 
  ELECTRICAL_CHECKS, 
  SEQUENCE_CONTROLS_CHECKS 
} from "@/config/checklist-fields";

import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { GeneralInfoForm } from "@/components/checklist/general-info-form";
import { ChecksSection } from "@/components/checklist/checks-section";
import { ImageUpload } from "@/components/checklist/image-upload";
import { SummaryView } from "@/components/checklist/summary-view";
import { AIRemarksGenerator } from "@/components/ai/AIRemarksGenerator";
import { Save, Eye, EyeOff, Lock } from "lucide-react";
import { AuthGuard, useAuth, ApprovalMessage } from "@/components/auth";

function ChecklistPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const editId = searchParams.get('edit');
  const equipmentId = searchParams.get('equipment');
  const attemptParam = searchParams.get('attempt');
  const { user } = useAuth();
  const { saveChecklist: saveChecklistSafe, safeStorageOperation } = useStorageService();
  
  const [isSaving, setIsSaving] = useState(false);
  const [savedChecklist, setSavedChecklist] = useState<ChecklistData | null>(null);
  const [activeTab, setActiveTab] = useState("form");
  const [shouldSwitchToSummary, setShouldSwitchToSummary] = useState(false);
  const [selectedEquipmentTag, setSelectedEquipmentTag] = useState<EquipmentTag | null>(null);
  const [existingChecklist, setExistingChecklist] = useState<ChecklistData | null>(null);
  const [initialLoadComplete, setInitialLoadComplete] = useState(false);
  
  // Ref to track if we've already loaded this specific checklist
  const loadedChecklistId = useRef<string | null>(null);

  const form = useForm<ChecklistFormData>({
    resolver: zodResolver(checklistSchema),
    defaultValues: {
      generalInfo: {
        clientName: "",
        building: "",
        inspectedBy: "",
        approvedBy: "",
        date: new Date().toISOString().split('T')[0],
        ppmAttempt: 1,
        equipmentName: "",
        location: "",
        tagNo: "",
      },
      mechanicalChecks: {
        beltWearPulleyAlignment: "Missing",
        bladeImpellerDamage: "Missing",
        boltSetScrewTightness: "Missing",
        bladeTipClearance: "Missing",
        excessiveVibration: "Missing",
        fanGuardProtection: "Missing",
        fanPowerOff: "Missing",
        motorOverheating: "Missing",
        rotationDirection: "Missing",
        cleanBladesHousing: "Missing",
        dustDebrisRemoval: "Missing",
        erraticOperation: "Missing",
        inletVanesOperation: "Missing",
        bearingLubrication: "Missing",
        noObstructionsBackflow: "Missing",
        physicalDamageStability: "Missing",
        springMountVibrationIsolator: "Missing",
      },
      electricalChecks: {
        bmsControlsInterlocks: "Missing",
        burntMarksDiscolorMelted: "Missing",
        circuitBreakerFunctional: "Missing",
        contractorsBreakers: "Missing",
        fireAlarmConnected: "Missing",
        fuseTerminals: "Missing",
        mccPowerOffBreaker: "Missing",
        signsLiquidLeaks: "Missing",
        tripSettingsFunction: "Missing",
        controlRelaysOperations: "Missing",
        doorsCoversCloseProperly: "Missing",
        frayingExposedWires: "Missing",
        highLowSpeedVerification: "Missing",
        indicationsOnOffTrip: "Missing",
        looseWiresToBeTightened: "Missing",
        selectorHandStopAuto: "Missing",
        testEmergencyStopButton: "Missing",
      },
      sequenceControlsChecks: {
        dptDifferentialPressureTransmitter: "Missing",
        erraticOperationMalfunctioning: "Missing",
        indicationsOnOffTrip: "Missing",
        mccOffOverrideFunction: "Missing",
        msfdDamperFunctional: "Missing",
        offWithDuctDetectorActivation: "Missing",
        overrideFscsPanelStatus: "Missing",
        sameTagNameInMccFan: "Missing",
        selectorRunStopAuto: "Missing",
        vfdVariableFrequencyDrive: "Missing",
      },
      remarks: "",
    },
  });



  // Watch only the remarks field for the AI component
  const currentRemarks = form.watch("remarks");

  // Stable empty object to prevent reactive updates in AI component
  const stableChecklistData = useMemo(() => ({}), []);

  // Switch to summary tab when savedChecklist is set and flag is true
  useEffect(() => {
    if (savedChecklist && shouldSwitchToSummary) {
      // Add small delay for mobile browsers to ensure proper state sync
      setTimeout(() => {
        setActiveTab("summary");
        setShouldSwitchToSummary(false);
      }, 100);
    }
  }, [savedChecklist, shouldSwitchToSummary]);

  // DISABLED: This useEffect was causing form resets - commenting out entirely
  // // Ensure form data persists when switching tabs after save
  // useEffect(() => {
  //   if (savedChecklist && activeTab === "form" && initialLoadComplete) {
  //     // Re-populate form with saved data when switching back to form tab
  //     const formData = {
  //       generalInfo: savedChecklist.generalInfo,
  //       mechanicalChecks: savedChecklist.mechanicalChecks,
  //       electricalChecks: savedChecklist.electricalChecks,
  //       sequenceControlsChecks: savedChecklist.sequenceControlsChecks,
  //       remarks: savedChecklist.remarks,
  //       beforeImage: savedChecklist.beforeImage,
  //       afterImage: savedChecklist.afterImage,
  //       inspectorSignature: savedChecklist.inspectorSignature,
  //       imageMetadata: savedChecklist.imageMetadata,
  //     };
      
  //     // Only reset if form is actually empty (to avoid overwriting user changes)
  //     const currentFormData = form.getValues();
      
  //     // Check if form has been modified by comparing with saved data
  //     // This prevents resetting when user has made changes
  //     const hasUserChanges = (
  //       currentFormData.generalInfo.clientName !== savedChecklist.generalInfo.clientName ||
  //       currentFormData.generalInfo.building !== savedChecklist.generalInfo.building ||
  //       currentFormData.generalInfo.inspectedBy !== savedChecklist.generalInfo.inspectedBy ||
  //       currentFormData.generalInfo.approvedBy !== savedChecklist.generalInfo.approvedBy ||
  //       currentFormData.generalInfo.date !== savedChecklist.generalInfo.date ||
  //       currentFormData.generalInfo.ppmAttempt !== savedChecklist.generalInfo.ppmAttempt ||
  //       currentFormData.generalInfo.equipmentName !== savedChecklist.generalInfo.equipmentName ||
  //       currentFormData.generalInfo.location !== savedChecklist.generalInfo.location ||
  //       currentFormData.generalInfo.tagNo !== savedChecklist.generalInfo.tagNo ||
  //       currentFormData.remarks !== savedChecklist.remarks
  //     );
      
  //     // Only reset if no user changes detected and form appears to be in initial state
  //     const isFormEmpty = !currentFormData.generalInfo.clientName && 
  //                        !currentFormData.generalInfo.building && 
  //                        !currentFormData.generalInfo.inspectedBy;
      
  //     if (isFormEmpty && !hasUserChanges) {
  //       form.reset(formData);
        
  //       // Also restore equipment tag if it was cleared
  //       if (!selectedEquipmentTag && savedChecklist.generalInfo.tagNo) {
  //         const mockEquipmentTag: EquipmentTag = {
  //           id: savedChecklist.equipmentTagId || 'legacy-' + savedChecklist.generalInfo.tagNo,
  //           contractor: 'Auburn Engineering WLL',
  //           clientName: savedChecklist.generalInfo.clientName,
  //           equipmentName: savedChecklist.generalInfo.equipmentName,
  //           tagNumber: savedChecklist.generalInfo.tagNo,
  //           dateOfCreation: savedChecklist.createdAt?.split('T')[0] || new Date().toISOString().split('T')[0],
  //           building: savedChecklist.generalInfo.building,
  //           location: savedChecklist.generalInfo.location,
  //           qrCodeData: '',
  //           createdAt: savedChecklist.createdAt || new Date().toISOString(),
  //           updatedAt: savedChecklist.updatedAt || new Date().toISOString(),
  //           createdBy: savedChecklist.userId || user?.uid || '',
  //         };
  //         setSelectedEquipmentTag(mockEquipmentTag);
  //       }
  //     }
  //   }
  // }, [savedChecklist, activeTab, selectedEquipmentTag, user, initialLoadComplete]);

  // Load existing checklist if editing
  useEffect(() => {
    const loadExistingChecklist = async () => {
      if (editId) {
        const checklist = await safeStorageOperation(
          async () => {
            // Import StorageService dynamically for this specific call
            const { StorageService } = await import('@/lib/services/storage/storage-adapter');
            return StorageService.getChecklistById(editId);
          },
          null
        );
        if (checklist) {
          setExistingChecklist(checklist);
        
          // Check if checklist is completed and user is not admin
          if (checklist.isCompleted) {
          alert("This checklist has been marked as completed and can only be modified by administrators.");
          router.push('/saved');
          return;
        }

        // Helper function to convert string numbers to actual numbers
        const convertToNumber = (value: number | string | undefined): number | undefined => {
          if (typeof value === 'string' && value !== '') {
            const num = Number(value);
            const result = isNaN(num) ? undefined : num;
            return result;
          }
          const result = typeof value === 'number' ? value : undefined;
          return result;
        };

        // Ensure ppmAttempt is a number (in case of old data with string values)
        const generalInfo = {
          ...checklist.generalInfo,
          ppmAttempt: typeof checklist.generalInfo.ppmAttempt === 'string' 
            ? Number(checklist.generalInfo.ppmAttempt) 
            : checklist.generalInfo.ppmAttempt
        };

        // Convert number fields in mechanical checks
        const mechanicalChecks = {
          ...checklist.mechanicalChecks,
          airflowVelocity: convertToNumber(checklist.mechanicalChecks.airflowVelocity),
          speedRpm: convertToNumber(checklist.mechanicalChecks.speedRpm),
          unusualSoundDecibel: convertToNumber(checklist.mechanicalChecks.unusualSoundDecibel),
        };

        // Convert number fields in electrical checks
        const electricalChecks = {
          ...checklist.electricalChecks,
          currentAmps: convertToNumber(checklist.electricalChecks.currentAmps),
          motorPowerKw: convertToNumber(checklist.electricalChecks.motorPowerKw),
          potentialVoltage: convertToNumber(checklist.electricalChecks.potentialVoltage),
        };

        const formData = {
          generalInfo,
          mechanicalChecks,
          electricalChecks,
          sequenceControlsChecks: checklist.sequenceControlsChecks,
          remarks: checklist.remarks,
          beforeImage: checklist.beforeImage,
          afterImage: checklist.afterImage,
          inspectorSignature: checklist.inspectorSignature,
          imageMetadata: checklist.imageMetadata, // ✅ IMPORTANT: Preserve image metadata for PDF generation
        };

        form.reset(formData);

        // For editing mode, if we have equipment data, create a mock equipment tag
        // This is a fallback for existing checklists that don't have equipment tag data
        if (generalInfo.tagNo && generalInfo.clientName && generalInfo.equipmentName) {
          const mockEquipmentTag: EquipmentTag = {
            id: 'legacy-' + generalInfo.tagNo,
            contractor: 'Auburn Engineering WLL',
            clientName: generalInfo.clientName,
            equipmentName: generalInfo.equipmentName,
            tagNumber: generalInfo.tagNo,
            dateOfCreation: checklist.createdAt?.split('T')[0] || new Date().toISOString().split('T')[0],
            building: generalInfo.building,
            location: generalInfo.location,
            qrCodeData: '',
            createdAt: checklist.createdAt || new Date().toISOString(),
            updatedAt: checklist.updatedAt || new Date().toISOString(),
            createdBy: checklist.userId || user?.uid || '',
          };
          setSelectedEquipmentTag(mockEquipmentTag);
        }
        }
      }
      
      // Mark initial load as complete
      setInitialLoadComplete(true);
    };

    // Only run if we haven't loaded this checklist already
    if (editId && loadedChecklistId.current !== editId) {
      loadedChecklistId.current = editId;
      loadExistingChecklist();
    } else if (!editId && !initialLoadComplete) {
      // For new checklists, just mark as complete
      setInitialLoadComplete(true);
    }
  }, [editId]); // Only depend on editId - remove other problematic dependencies

  // Load equipment tag and existing checklist if provided via URL parameters
  useEffect(() => {
    const loadEquipmentAndChecklist = async () => {
      // Handle editing existing checklist
      if (editId && !selectedEquipmentTag) {
        try {
          const { StorageService } = await import('@/lib/services/storage/storage-adapter');
          const existingChecklist = await StorageService.getChecklistById(editId);

          if (existingChecklist) {
            setExistingChecklist(existingChecklist);

            // Check if checklist is completed and user is not admin
            if (existingChecklist.isCompleted) {
              alert("This checklist has been marked as completed and can only be modified by administrators.");
              router.push('/saved');
              return;
            }

            // Load the equipment tag associated with this checklist
            if (existingChecklist.equipmentTagId) {
              const { EquipmentTagService } = await import('@/lib/services/equipment/equipment-tag-service');
              const tag = await EquipmentTagService.getEquipmentTagById(existingChecklist.equipmentTagId);
              if (tag) {
                setSelectedEquipmentTag(tag);
              }
            }

            // Helper function to convert string numbers to actual numbers
            const convertToNumber = (value: number | string | undefined): number | undefined => {
              if (typeof value === 'string' && value !== '') {
                const num = Number(value);
                return isNaN(num) ? undefined : num;
              }
              return typeof value === 'number' ? value : undefined;
            };

            // Ensure ppmAttempt is a number
            const generalInfo = {
              ...existingChecklist.generalInfo,
              ppmAttempt: typeof existingChecklist.generalInfo.ppmAttempt === 'string'
                ? Number(existingChecklist.generalInfo.ppmAttempt)
                : existingChecklist.generalInfo.ppmAttempt
            };

            // Convert number fields in mechanical checks
            const mechanicalChecks = {
              ...existingChecklist.mechanicalChecks,
              airflowVelocity: convertToNumber(existingChecklist.mechanicalChecks.airflowVelocity),
              speedRpm: convertToNumber(existingChecklist.mechanicalChecks.speedRpm),
              unusualSoundDecibel: convertToNumber(existingChecklist.mechanicalChecks.unusualSoundDecibel),
            };

            // Convert number fields in electrical checks
            const electricalChecks = {
              ...existingChecklist.electricalChecks,
              currentAmps: convertToNumber(existingChecklist.electricalChecks.currentAmps),
              motorPowerKw: convertToNumber(existingChecklist.electricalChecks.motorPowerKw),
              potentialVoltage: convertToNumber(existingChecklist.electricalChecks.potentialVoltage),
            };

            const formData = {
              generalInfo,
              mechanicalChecks,
              electricalChecks,
              sequenceControlsChecks: existingChecklist.sequenceControlsChecks,
              remarks: existingChecklist.remarks,
              beforeImage: existingChecklist.beforeImage,
              afterImage: existingChecklist.afterImage,
              inspectorSignature: existingChecklist.inspectorSignature,
              imageMetadata: existingChecklist.imageMetadata,
            };

            form.reset(formData);
            console.log('Loaded existing checklist for editing:', editId);

            // Mark initial load as complete
            setInitialLoadComplete(true);
            return;
          }
        } catch (error) {
          console.error('Error loading existing checklist:', error);
          alert('Failed to load existing checklist. Please try again.');
          return;
        }
      }

      // Handle new checklist with equipment ID
      if (equipmentId && !selectedEquipmentTag) {
        try {
          // Import services dynamically
          const { EquipmentTagService } = await import('@/lib/services/equipment/equipment-tag-service');
          const { StorageService } = await import('@/lib/services/storage/storage-adapter');
          
          const tag = await EquipmentTagService.getEquipmentTagById(equipmentId);
          
          if (tag) {
            setSelectedEquipmentTag(tag);
            
            // If attempt parameter is provided, try to load existing checklist
            if (attemptParam) {
              const attemptNumber = parseInt(attemptParam, 10);
              if (!isNaN(attemptNumber)) {
                // Get all checklists and find one that matches equipment and attempt
                const allChecklists = await StorageService.getAllChecklists();
                const existingChecklist = allChecklists.find(checklist =>
                  checklist.equipmentTagId === equipmentId &&
                  checklist.generalInfo?.ppmAttempt === attemptNumber
                );
                
                if (existingChecklist) {
                  // Load existing checklist data
                  setExistingChecklist(existingChecklist);
                  
                  // Check if checklist is completed and user is not admin
                  if (existingChecklist.isCompleted) {
                    alert("This checklist has been marked as completed and can only be modified by administrators.");
                    router.push('/saved');
                    return;
                  }

                  // Helper function to convert string numbers to actual numbers
                  const convertToNumber = (value: number | string | undefined): number | undefined => {
                    if (typeof value === 'string' && value !== '') {
                      const num = Number(value);
                      return isNaN(num) ? undefined : num;
                    }
                    return typeof value === 'number' ? value : undefined;
                  };

                  // Ensure ppmAttempt is a number
                  const generalInfo = {
                    ...existingChecklist.generalInfo,
                    ppmAttempt: typeof existingChecklist.generalInfo.ppmAttempt === 'string' 
                      ? Number(existingChecklist.generalInfo.ppmAttempt) 
                      : existingChecklist.generalInfo.ppmAttempt
                  };

                  // Convert number fields in mechanical checks
                  const mechanicalChecks = {
                    ...existingChecklist.mechanicalChecks,
                    airflowVelocity: convertToNumber(existingChecklist.mechanicalChecks.airflowVelocity),
                    speedRpm: convertToNumber(existingChecklist.mechanicalChecks.speedRpm),
                    unusualSoundDecibel: convertToNumber(existingChecklist.mechanicalChecks.unusualSoundDecibel),
                  };

                  // Convert number fields in electrical checks
                  const electricalChecks = {
                    ...existingChecklist.electricalChecks,
                    currentAmps: convertToNumber(existingChecklist.electricalChecks.currentAmps),
                    motorPowerKw: convertToNumber(existingChecklist.electricalChecks.motorPowerKw),
                    potentialVoltage: convertToNumber(existingChecklist.electricalChecks.potentialVoltage),
                  };

                  const formData = {
                    generalInfo,
                    mechanicalChecks,
                    electricalChecks,
                    sequenceControlsChecks: existingChecklist.sequenceControlsChecks,
                    remarks: existingChecklist.remarks,
                    beforeImage: existingChecklist.beforeImage,
                    afterImage: existingChecklist.afterImage,
                    inspectorSignature: existingChecklist.inspectorSignature,
                    imageMetadata: existingChecklist.imageMetadata,
                  };

                  form.reset(formData);
                  console.log('Loaded existing checklist for PPM attempt:', attemptNumber);
                  
                  // Mark initial load as complete
                  setInitialLoadComplete(true);
                } else {
                  // No existing checklist found, auto-populate form with equipment tag data and attempt number
                  form.setValue("generalInfo.clientName", tag.clientName, { shouldValidate: true });
                  form.setValue("generalInfo.building", tag.building, { shouldValidate: true });
                  form.setValue("generalInfo.equipmentName", tag.equipmentName, { shouldValidate: true });
                  form.setValue("generalInfo.location", tag.location, { shouldValidate: true });
                  form.setValue("generalInfo.tagNo", tag.tagNumber, { shouldValidate: true });
                  form.setValue("generalInfo.ppmAttempt", attemptNumber, { shouldValidate: true });
                  console.log('No existing checklist found, starting new checklist for PPM attempt:', attemptNumber);
                  
                  // Mark initial load as complete
                  setInitialLoadComplete(true);
                }
              }
            } else {
              // No attempt parameter, just populate equipment tag data
              form.setValue("generalInfo.clientName", tag.clientName, { shouldValidate: true });
              form.setValue("generalInfo.building", tag.building, { shouldValidate: true });
              form.setValue("generalInfo.equipmentName", tag.equipmentName, { shouldValidate: true });
              form.setValue("generalInfo.location", tag.location, { shouldValidate: true });
              form.setValue("generalInfo.tagNo", tag.tagNumber, { shouldValidate: true });
              
              // Mark initial load as complete
              setInitialLoadComplete(true);
            }
          } else {
            console.warn('Equipment tag not found:', equipmentId);
            alert('Equipment tag not found. Please select a valid equipment tag.');
          }
        } catch (error) {
          console.error('Error loading equipment tag and checklist:', error);
          alert('Failed to load equipment data. Please select manually.');
        }
      }
    };

    // Load equipment and checklist data when:
    // 1. We have an equipment ID and no selected equipment tag and no edit ID (new checklist)
    // 2. We have an edit ID but no selected equipment tag (editing existing checklist)
    if ((equipmentId && !selectedEquipmentTag && !editId) || (editId && !selectedEquipmentTag)) {
      loadEquipmentAndChecklist();
    }
  }, [equipmentId, attemptParam, selectedEquipmentTag, editId, form, router]);

  const handleEquipmentTagSelect = (tag: EquipmentTag) => {
    setSelectedEquipmentTag(tag);
  };

  const handleEquipmentTagClear = () => {
    setSelectedEquipmentTag(null);
  };

  const onSubmit = async (data: ChecklistFormData) => {
    if (!user) {
      alert("You must be signed in to save checklists.");
      return;
    }

    // Validate that equipment tag is selected
    if (!selectedEquipmentTag) {
      alert("Please select a valid equipment tag before saving the checklist.");
      return;
    }

    // Validate equipment tag integrity before saving
    try {
      const { EquipmentTagService } = await import('@/lib/services/equipment/equipment-tag-service');
      const validation = await EquipmentTagService.validateEquipmentTagForChecklist(
        selectedEquipmentTag.id,
        selectedEquipmentTag.tagNumber,
        selectedEquipmentTag.clientName
      );

      if (!validation.isValid) {
        alert(
          `Equipment tag validation failed: ${validation.error}\n\n` +
          `Please select a valid equipment tag before saving the checklist.`
        );
        setSelectedEquipmentTag(null); // Clear invalid selection
        return;
      }

      // Update selected equipment tag with latest data if it was modified
      if (validation.equipmentTag && validation.equipmentTag.updatedAt !== selectedEquipmentTag.updatedAt) {
        setSelectedEquipmentTag(validation.equipmentTag);
        
        // Update form with latest equipment data
        form.setValue("generalInfo.clientName", validation.equipmentTag.clientName, { shouldValidate: true });
        form.setValue("generalInfo.building", validation.equipmentTag.building, { shouldValidate: true });
        form.setValue("generalInfo.equipmentName", validation.equipmentTag.equipmentName, { shouldValidate: true });
        form.setValue("generalInfo.location", validation.equipmentTag.location, { shouldValidate: true });
        form.setValue("generalInfo.tagNo", validation.equipmentTag.tagNumber, { shouldValidate: true });
      }
    } catch (validationError) {
      console.error('Equipment tag validation error:', validationError);
      alert("Failed to validate equipment tag. Please try again or select a different equipment tag.");
      return;
    }

    setIsSaving(true);
    try {
      // Get the checklist ID - use existing or generate new one
      let checklistId = editId;
      
      // If we have an existing checklist from continuing an attempt, use its ID
      if (!checklistId && existingChecklist) {
        checklistId = existingChecklist.id;
      }
      
      if (!checklistId) {
        // For new checklists, check if we have a session ID from image uploads
        const sessionId = sessionStorage.getItem('current_checklist_id');
        if (!sessionId) {
          // Generate ID using crypto.randomUUID() if available, fallback to timestamp
          checklistId = crypto.randomUUID ? crypto.randomUUID() : `checklist_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
        } else {
          checklistId = sessionId;
        }
      }

      const checklistData: ChecklistData = {
        id: checklistId,
        createdAt: editId && existingChecklist ? 
          existingChecklist.createdAt || new Date().toISOString() : 
          new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        userId: user.uid, // Associate with authenticated user
        syncMetadata: editId && existingChecklist ? 
          existingChecklist.syncMetadata || {
            status: 'local-only',
            lastLocalUpdateAt: new Date().toISOString(),
            localVersion: 1
          } : {
            status: 'local-only',
            lastLocalUpdateAt: new Date().toISOString(),
            localVersion: 1
          },
        equipmentTagId: selectedEquipmentTag.id, // Store equipment tag reference
        isCompleted: editId && existingChecklist ? 
          existingChecklist.isCompleted || false :
          false, // New checklists start as not completed
        ...data,
      };

      const success = await saveChecklistSafe(checklistData);
      if (success) {
        setSavedChecklist(checklistData);
        setShouldSwitchToSummary(true);
        
        // Update existing checklist state to prevent form reset issues
        setExistingChecklist(checklistData);
        
        if (!editId) {
          // Update URL to show we're now editing this checklist
          router.replace(`/checklist?edit=${checklistData.id}`);
          // Clean up session storage since we now have a permanent ID
          sessionStorage.removeItem('current_checklist_id');
        }
      } else {
        alert("Failed to save checklist. This might be due to:\n\n• Network connectivity issues\n• Firestore service unavailable\n• Authentication problems\n• Image upload failures\n\nPlease check your connection and try saving again.");
      }
    } catch (error) {
      console.error("Error saving checklist:", error);
      alert("Failed to save checklist. Please try again.");
    } finally {
      setIsSaving(false);
    }
  };

  // Check if inspection sections should be blocked
  const isInspectionBlocked = !selectedEquipmentTag;

  return (
    <div className="max-w-6xl mx-auto space-y-4 sm:space-y-6 p-3 sm:p-4">
      {/* Approval Message for Users */}
      <ApprovalMessage />

      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="min-w-0 flex-1">
          <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold">
            {editId ? "Edit Inspection Checklist" : "New Inspection Checklist"}
          </h1>
          <p className="text-sm sm:text-base text-muted-foreground">
            Complete the inspection checklist for ACMV systems
            {selectedEquipmentTag && (
              <span className="block mt-1 text-green-600 font-medium text-sm">
                Equipment: {selectedEquipmentTag.tagNumber} - {selectedEquipmentTag.equipmentName}
              </span>
            )}
          </p>
        </div>

        <div className="flex items-center gap-2 flex-shrink-0">
          <Button
            onClick={() => setActiveTab(activeTab === "form" ? "summary" : "form")}
            variant="outline"
            size="sm"
            className="flex items-center gap-2 w-full sm:w-auto"
          >
            {activeTab === "form" ? (
              <>
                <Eye className="h-4 w-4" />
                <span className="hidden sm:inline">Preview</span>
                <span className="sm:hidden">Summary</span>
              </>
            ) : (
              <>
                <EyeOff className="h-4 w-4" />
                <span className="hidden sm:inline">Edit</span>
                <span className="sm:hidden">Form</span>
              </>
            )}
          </Button>
        </div>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4 sm:space-y-6">
        <TabsList className="grid w-full grid-cols-2 h-auto">
          <TabsTrigger value="form" className="flex items-center gap-2 p-2 sm:p-3">
            <Save className="h-4 w-4" />
            <span className="text-sm sm:text-base">Form</span>
          </TabsTrigger>
          <TabsTrigger value="summary" className="flex items-center gap-2 p-2 sm:p-3">
            <Eye className="h-4 w-4" />
            <span className="text-sm sm:text-base">Summary</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="form" className="space-y-4 sm:space-y-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4 sm:space-y-6">
              {/* General Information with Equipment Tag Selection */}
              <GeneralInfoForm 
                form={form} 
                selectedEquipmentTag={selectedEquipmentTag}
                onEquipmentTagSelect={handleEquipmentTagSelect}
                onEquipmentTagClear={handleEquipmentTagClear}
              />

              {/* Blocked Access Warning */}
              {isInspectionBlocked && (
                <Alert variant="destructive">
                  <Lock className="h-4 w-4" />
                  <AlertDescription>
                    <strong>Inspection sections locked:</strong> Please select a valid equipment tag above to access the inspection checklist sections.
                  </AlertDescription>
                </Alert>
              )}

              {/* Mechanical Checks */}
              <div className={isInspectionBlocked ? "opacity-50 pointer-events-none" : ""}>
                <ChecksSection
                  title="Mechanical Checks"
                  description="Physical inspection of mechanical components"
                  fields={MECHANICAL_CHECKS}
                  section="mechanical"
                  form={form}
                  defaultOpen={!isInspectionBlocked}
                />
              </div>

              {/* Electrical Checks */}
              <div className={isInspectionBlocked ? "opacity-50 pointer-events-none" : ""}>
                <ChecksSection
                  title="Electrical Checks"
                  description="Electrical system and component inspection"
                  fields={ELECTRICAL_CHECKS}
                  section="electrical"
                  form={form}
                />
              </div>

              {/* Sequence Controls Checks */}
              <div className={isInspectionBlocked ? "opacity-50 pointer-events-none" : ""}>
                <ChecksSection
                  title="Sequence Controls Checks"
                  description="Control system and sequence verification"
                  fields={SEQUENCE_CONTROLS_CHECKS}
                  section="sequence"
                  form={form}
                />
              </div>

              {/* Remarks */}
              <div className={isInspectionBlocked ? "opacity-50 pointer-events-none" : ""}>
                <AIRemarksGenerator
                  checklistData={stableChecklistData}
                  currentRemarks={currentRemarks}
                  onRemarksChange={(remarks) => form.setValue("remarks", remarks)}
                  disabled={isInspectionBlocked}
                  placeholder="Enter any additional remarks, observations, or recommendations..."
                />
              </div>

              {/* Image Uploads and Signature */}
              <div className={isInspectionBlocked ? "opacity-50 pointer-events-none" : ""}>
                <ImageUpload 
                  form={form} 
                  checklistId={editId || undefined}
                />
              </div>

              {/* Save Button */}
              <div className="flex justify-end">
                <Button 
                  type="submit" 
                  disabled={isSaving || isInspectionBlocked}
                  size="lg"
                  className="w-full sm:w-auto"
                >
                  {isSaving ? (
                    <>
                      <Save className="mr-2 h-4 w-4 animate-spin" />
                      Saving...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      {editId ? "Update Checklist" : "Save Checklist"}
                    </>
                  )}
                </Button>
              </div>
            </form>
          </Form>
        </TabsContent>

        <TabsContent value="summary" className="space-y-4 sm:space-y-6">
          {savedChecklist && (
            <SummaryView checklist={savedChecklist} />
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}

export default function ChecklistPage() {
  return (
    <AuthGuard>
      <Suspense fallback={
        <div className="flex items-center justify-center min-h-screen">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      }>
        <ChecklistPageContent />
      </Suspense>
    </AuthGuard>
  );
} 
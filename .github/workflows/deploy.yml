# name: Deploy to Firebase

# on:
#   push:
#     branches:
#       - dev
#   workflow_dispatch: # Allow manual triggering

# jobs:
#   deploy:
#     name: Build and Deploy
#     runs-on: ubuntu-latest
    
#     steps:
#       - name: Checkout code
#         uses: actions/checkout@v4
        
#       - name: Setup Node.js
#         uses: actions/setup-node@v4
#         with:
#           node-version: '20'
          
#       - name: Cache dependencies
#         uses: actions/cache@v4
#         with:
#           path: |
#             ~/.npm
#             node_modules
#             functions/node_modules
#           key: ${{ runner.os }}-deps-${{ hashFiles('**/package-lock.json') }}
#           restore-keys: |
#             ${{ runner.os }}-deps-
            
#       - name: Install dependencies
#         run: npm ci
        
#       - name: Install functions dependencies
#         run: |
#           cd functions
#           npm ci
          
#       - name: Build Next.js application
#         run: npm run build
#         env:
#           NEXT_PUBLIC_FIREBASE_API_KEY: ${{ secrets.NEXT_PUBLIC_FIREBASE_API_KEY }}
#           NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN: ${{ secrets.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN }}
#           NEXT_PUBLIC_FIREBASE_PROJECT_ID: ${{ secrets.NEXT_PUBLIC_FIREBASE_PROJECT_ID }}
#           NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET: ${{ secrets.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET }}
#           NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID: ${{ secrets.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID }}
#           NEXT_PUBLIC_FIREBASE_APP_ID: ${{ secrets.NEXT_PUBLIC_FIREBASE_APP_ID }}
            
#       - name: Authenticate to Google Cloud
#         uses: google-github-actions/auth@v2
#         with:
#           credentials_json: '${{ secrets.FIREBASE_SERVICE_ACCOUNT_AUBURN_ENGINEERING }}'
          
#       - name: Setup Firebase CLI
#         run: npm install -g firebase-tools
        
#       - name: Deploy to Firebase
#         run: firebase deploy --project auburn-engineering --only hosting,functions,firestore:rules,storage 
# Camera Functionality Troubleshooting Guide

## Overview
This guide helps troubleshoot camera capture issues in the checklist inspection system for both mobile and web platforms.

## Requirements for Camera Access

### 1. Browser Compatibility
- **Supported Browsers:**
  - Chrome 53+ (recommended)
  - Safari 11+ (iOS Safari 11+)
  - Firefox 36+
  - Edge 79+

### 2. Security Requirements
- **HTTPS Required:** Camera access requires a secure context (HTTPS)
- **Localhost Exception:** HTTP works only on localhost for development
- **Permissions:** User must grant camera permissions

### 3. Device Requirements
- Device must have a camera
- Camera must not be in use by another application

## Common Issues and Solutions

### Issue 1: "Camera permission denied"
**Symptoms:** Error message about permission denied
**Solutions:**
1. Click "Allow" when browser prompts for camera access
2. Check browser settings:
   - Chrome: Settings > Privacy and Security > Site Settings > Camera
   - Safari: Safari > Preferences > Websites > Camera
   - Firefox: about:preferences#privacy > Permissions > Camera
3. Refresh the page after granting permissions

### Issue 2: "No camera found"
**Symptoms:** Error message about no camera devices
**Solutions:**
1. Ensure device has a camera
2. Close other applications using the camera
3. Try switching between front/back cameras
4. Restart the browser

### Issue 3: "Camera requires HTTPS"
**Symptoms:** Error about secure context or HTTPS
**Solutions:**
1. Access the site via HTTPS
2. For development: Use `npm run dev:https`
3. Ensure SSL certificate is valid

### Issue 4: Black screen or camera not loading
**Symptoms:** Camera preview shows black screen
**Solutions:**
1. Wait a few seconds for camera to initialize
2. Try the "Force Refresh" button
3. Switch camera facing mode (front/back)
4. Refresh the page

### Issue 5: Mobile-specific issues
**Symptoms:** Camera works on desktop but not mobile
**Solutions:**
1. Ensure mobile browser is up to date
2. Try opening in Chrome/Safari mobile
3. Check if PWA is installed correctly
4. Clear browser cache and data

## Testing Camera Functionality

### Development Testing
1. Run with HTTPS: `npm run dev:https`
2. Visit test page: `/test/camera`
3. Check system information
4. Test camera capture flow

### Production Testing
1. Ensure site is served over HTTPS
2. Test on multiple devices and browsers
3. Verify PWA manifest permissions
4. Check security headers

## Browser-Specific Notes

### Chrome (Desktop & Mobile)
- Best compatibility
- Supports all camera features
- Good error reporting

### Safari (Desktop & Mobile)
- Requires iOS 11+ for mobile
- May need user interaction to start camera
- Strict security requirements

### Firefox
- Good compatibility
- May require additional permissions
- Check about:config for media settings

### Edge
- Modern Edge (Chromium-based) works well
- Legacy Edge not supported

## Mobile Platform Notes

### iOS Safari
- Requires user gesture to start camera
- May show permission dialog multiple times
- Works best in standalone PWA mode

### Android Chrome
- Generally good compatibility
- May need specific video constraints
- Check for hardware acceleration issues

## Debugging Steps

### 1. Check System Information
Use the camera test page to verify:
- Browser compatibility
- HTTPS status
- MediaDevices API availability
- Secure context status

### 2. Browser Console
Check for errors in browser console:
```javascript
// Test camera availability
navigator.mediaDevices.getUserMedia({video: true})
  .then(stream => {
    console.log('Camera available');
    stream.getTracks().forEach(track => track.stop());
  })
  .catch(err => console.error('Camera error:', err));
```

### 3. Permission Status
Check permission status:
```javascript
navigator.permissions.query({name: 'camera'})
  .then(result => console.log('Camera permission:', result.state));
```

## Configuration Files

### Manifest.json
Ensure camera permissions are declared:
```json
{
  "permissions": ["camera", "microphone"]
}
```

### Security Headers
Next.js config includes required headers:
```typescript
{
  key: 'Permissions-Policy',
  value: 'camera=*, microphone=*, geolocation=*'
}
```

## Support Contact
If issues persist after following this guide:
1. Check browser compatibility
2. Verify HTTPS configuration
3. Test on different devices
4. Contact development team with:
   - Browser version
   - Device type
   - Error messages
   - Console logs

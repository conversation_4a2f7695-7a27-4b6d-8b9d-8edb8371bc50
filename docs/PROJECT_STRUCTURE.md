# Auburn Engineering - Project Structure

## Overview
This document outlines the restructured codebase organization for better scalability and maintainability.

## Directory Structure

### `/docs`
Project documentation and architecture guides
- `STORAGE_ARCHITECTURE.md` - Firebase storage architecture details
- `PROJECT_STRUCTURE.md` - This file, explaining project organization

### `/public`
Static assets organized by type
- `/icons` - All app icons, favicons, and PWA assets
- `/images` - Logos and static images
- `manifest.json` - PWA manifest

### `/src`
Main application source code

#### `/src/app`
Next.js App Router structure (production routes only)
- Clean production routes without test/debug pages
- Follows Next.js 13+ app directory conventions

#### `/src/components`
React components organized by feature
- `/ui` - Reusable UI components (shadcn/ui)
- `/auth` - Authentication related components
- `/dashboard` - Dashboard specific components  
- `/checklist` - Checklist management components
- `/equipment` - Equipment tagging components
- `/charts` - Data visualization components
- `/storage` - Storage management components
- `/sync` - Data synchronization components
- `/qr` - QR code related components
- `/ai` - AI integration components
- `/common` - Shared layout and utility components

#### `/src/lib`
Business logic and utilities

##### `/src/lib/services`
Domain-specific business services
- `/auth` - Authentication and user management
- `/storage` - File and data storage services
- `/checklist` - Checklist management logic
- `/equipment` - Equipment tagging services
- `/export` - Data export functionality
- `/ai` - AI service integration

##### `/src/lib/utils`
Pure utility functions
- `/compression` - Image compression utilities
- `/data` - Data processing and cleanup
- General utilities (validation, logging, analytics)

#### `/src/types`
TypeScript type definitions
- Well-structured domain types
- Shared interfaces and enums

#### `/src/hooks`
Custom React hooks

#### `/src/config`
Application configuration

#### `/src/constants`
Application constants and enums

### `/functions`
Firebase Cloud Functions (separate Node.js project)
- Serverless backend functionality
- Independent deployment and versioning

### `/tools`
Development utilities (excluded from production)
- `/dev` - Development and testing pages
- `/scripts` - Build and deployment scripts
- `/testing` - Test utilities and fixtures

## Benefits

### Scalability
- Clear domain separation makes adding features easier
- Service layer architecture enables better code reuse
- Modular structure supports team development

### Maintainability  
- Smaller, focused files are easier to understand and modify
- Clear separation of concerns reduces coupling
- Consistent organization reduces cognitive load

### Developer Experience
- Intuitive directory structure reduces onboarding time
- Development tools separated from production code
- Clear documentation and examples

### Performance
- Production builds exclude development utilities
- Better tree-shaking with modular exports
- Cleaner bundle analysis

## Migration Notes

### Asset Paths
Static assets have been consolidated in `/public` with proper subdirectories. Update any hardcoded paths:
- Icons: `/icons/filename.png` 
- Images: `/images/filename.jpg`

### Import Paths
Service imports have changed to reflect new structure:
```typescript
// Old
import { AuthService } from '@/lib/auth'

// New  
import { AuthService } from '@/lib/services/auth/auth'
```

### Development Pages
Test and debug pages moved to `/tools/dev/`. Can be temporarily added back to app router during development if needed.

## Next Steps

1. Update all remaining import paths throughout codebase
2. Add proper service interfaces and abstractions
3. Implement route grouping in app directory
4. Set up automated build scripts in `/tools/scripts`
5. Create comprehensive testing utilities in `/tools/testing` 
# Storage Architecture

## Overview
This application uses a dual-storage approach with Firebase services:

### Firestore (Document Database)
- **Purpose**: Stores checklist metadata, form data, and user information
- **Content**: All text data, checkbox states, user IDs, timestamps, etc.
- **Benefits**: Real-time sync, offline support, queryable data
- **Limit**: 1MB per document (no longer an issue with images moved out)

### Firebase Storage (File Storage)
- **Purpose**: Stores compressed images (before/after photos, signatures)
- **Content**: Image files with metadata for tracking and cleanup
- **Benefits**: CDN delivery, large file support, cost-effective for binary data
- **Limit**: 5GB free tier (generous for images)

## Data Flow

### Image Upload Process
1. User selects image file
2. Client-side compression (300-500KB target)
3. Upload to Firebase Storage with metadata
4. Store download URL in Firestore document
5. Track image path for cleanup purposes

### Storage Management
- **Firestore**: Handles text data, real-time sync, offline cache
- **Firebase Storage**: Handles image files, automatic CDN distribution
- **Cleanup**: Coordinated cleanup of both Firestore records and Storage files

## Benefits of New Architecture
- ✅ **No storage quota issues**: Images don't bloat Firestore documents
- ✅ **Better performance**: Images load from CDN, faster document queries
- ✅ **Cost effective**: Optimal pricing for both text and binary data
- ✅ **Scalable**: Can handle thousands of checklists with images
- ✅ **Automatic compression**: Images optimized before upload

## Migration Notes
- Old base64 image storage has been completely removed
- localStorage utilities cleaned up (not needed with Firebase)
- Offline persistence handled by Firestore's built-in capabilities
- Image metadata tracked for coordinated cleanup operations 
# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.
functions/node_modules
scripts/node_modules
# dependencies
/node_modules
/.pnp
.pnp.*
.yarn/*
!.yarn/patches
!.yarn/plugins
!.yarn/releases
!.yarn/versions

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# env files (can opt-in for committing if needed)
# .env*

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts


.firebase/auburn-engineering/hosting/
.firebase/
scripts/backups/
scripts/node_modules/
.env.local

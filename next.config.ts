import type { NextConfig } from "next";

const nextConfig: NextConfig = {
  // Enable static export for complete JAMstack deployment
  output: 'export',
  trailingSlash: true,

  // Exclude functions directory from output file tracing
  outputFileTracingExcludes: {
    '*': ['./functions/**/*'],
  },

  // Security headers for camera access
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'Permissions-Policy',
            value: 'camera=*, geolocation=*'
          },
          {
            key: 'Feature-Policy',
            value: 'camera *; geolocation *'
          }
        ],
      },
    ];
  },
  
  eslint: {
    // Warning: This allows production builds to successfully complete even if
    // your project has ESLint errors.
    ignoreDuringBuilds: true,
  },
  // Webpack configuration for Puppeteer and Node.js modules
  webpack: (config, { isServer }) => {
    // Exclude functions directory from webpack compilation
    config.externals = config.externals || [];
    if (Array.isArray(config.externals)) {
      config.externals.push('./functions');
    }
    
    if (!isServer) {
      // Exclude Node.js modules and server-only packages from client-side bundle
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        path: false,
        os: false,
        buffer: false,
        util: false,
        url: false,
        querystring: false,
        child_process: false,
        puppeteer: false,
        'puppeteer-core': false,
        '@puppeteer/browsers': false,
        'chrome-aws-lambda': false,
        'playwright': false,
        'playwright-chromium': false,
      };

      // Also exclude these packages as externals
      config.externals = [
        ...(config.externals || []),
        'child_process',
        'puppeteer',
        'puppeteer-core',
        '@puppeteer/browsers',
        'chrome-aws-lambda',
        'playwright',
        'playwright-chromium',
      ];

      // Add module rules to ignore problematic modules
      config.module.rules.push({
        test: /node_modules\/@puppeteer\/browsers/,
        use: 'null-loader',
      });

      // Ignore specific modules that cause issues
      const webpack = require('webpack');
      config.plugins = config.plugins || [];
      config.plugins.push(
        new webpack.IgnorePlugin({
          resourceRegExp: /^(@puppeteer\/browsers|puppeteer|child_process)$/,
        })
      );
    }
    return config;
  },
  // Server external packages for better server-side handling
  serverExternalPackages: [
    'puppeteer', 
    'puppeteer-core', 
    '@puppeteer/browsers',
    'chrome-aws-lambda',
    'playwright',
    'playwright-chromium'
  ],
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'firebasestorage.googleapis.com',
        port: '',
        pathname: '/v0/b/**',
      },
      {
        protocol: 'https',
        hostname: '*.firebasestorage.app',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'auburn-engineering.firebasestorage.app',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'auburnengineering.com',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'auburn-engineering.web.app',
        port: '',
        pathname: '/**',
      },
    ],
    // Always disable optimization for static export and Firebase Storage compatibility
    unoptimized: true,
    // Use custom loader for Firebase Storage images
    loader: 'custom',
    loaderFile: './src/lib/utils/image-loader.ts',
  },
};

export default nextConfig;

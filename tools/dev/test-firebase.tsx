'use client';

import React, { useEffect, useState } from 'react';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { CheckCircle, XCircle, Loader2 } from 'lucide-react';
import { auth, analytics } from '@/config/firebase-persistence';
import { useAuth } from '@/components/auth';

export default function TestFirebasePage() {
  const [firebaseStatus, setFirebaseStatus] = useState<{
    auth: 'loading' | 'success' | 'error';
    analytics: 'loading' | 'success' | 'error';
    config: 'loading' | 'success' | 'error';
  }>({
    auth: 'loading',
    analytics: 'loading',
    config: 'loading'
  });

  const { user } = useAuth();

  useEffect(() => {
    // Test Firebase configuration
    const testFirebase = async () => {
      try {
        // Test config
        const config = {
          apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
          authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
          projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
          storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
          messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
          appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID,
          measurementId: process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID
        };

        const hasAllConfig = Object.values(config).every(value => value && value !== '');
        
        setFirebaseStatus(prev => ({
          ...prev,
          config: hasAllConfig ? 'success' : 'error'
        }));

        // Test Auth
        if (auth) {
          setFirebaseStatus(prev => ({
            ...prev,
            auth: 'success'
          }));
        } else {
          setFirebaseStatus(prev => ({
            ...prev,
            auth: 'error'
          }));
        }

        // Test Analytics
        if (analytics) {
          setFirebaseStatus(prev => ({
            ...prev,
            analytics: 'success'
          }));
        } else {
          setFirebaseStatus(prev => ({
            ...prev,
            analytics: 'error'
          }));
        }
      } catch (error) {
        console.error('Firebase test error:', error);
        setFirebaseStatus({
          auth: 'error',
          analytics: 'error',
          config: 'error'
        });
      }
    };

    testFirebase();
  }, []);

  const StatusIcon = ({ status }: { status: 'loading' | 'success' | 'error' }) => {
    switch (status) {
      case 'loading':
        return <Loader2 className="h-5 w-5 animate-spin text-blue-500" />;
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-500" />;
    }
  };

  const getStatusText = (status: 'loading' | 'success' | 'error') => {
    switch (status) {
      case 'loading':
        return 'Testing...';
      case 'success':
        return 'Working';
      case 'error':
        return 'Error';
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      <div className="space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Firebase Configuration Test</h1>
          <p className="text-muted-foreground">
            This page tests your Firebase setup to ensure everything is working correctly.
          </p>
        </div>

        {/* Configuration Status */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Configuration Status</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span>Environment Variables</span>
              <div className="flex items-center gap-2">
                <StatusIcon status={firebaseStatus.config} />
                <span className="text-sm">{getStatusText(firebaseStatus.config)}</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span>Authentication Service</span>
              <div className="flex items-center gap-2">
                <StatusIcon status={firebaseStatus.auth} />
                <span className="text-sm">{getStatusText(firebaseStatus.auth)}</span>
              </div>
            </div>
            <div className="flex items-center justify-between">
              <span>Analytics Service</span>
              <div className="flex items-center gap-2">
                <StatusIcon status={firebaseStatus.analytics} />
                <span className="text-sm">{getStatusText(firebaseStatus.analytics)}</span>
              </div>
            </div>
          </div>
        </Card>

        {/* Environment Variables Details */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Environment Variables</h2>
          <div className="space-y-2 text-sm font-mono">
            <div className="flex justify-between">
              <span>API Key:</span>
              <span className="text-green-500">
                {process.env.NEXT_PUBLIC_FIREBASE_API_KEY ? '✓ Set' : '✗ Missing'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Auth Domain:</span>
              <span className="text-green-500">
                {process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN ? '✓ Set' : '✗ Missing'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Project ID:</span>
              <span className="text-green-500">
                {process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID || '✗ Missing'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Storage Bucket:</span>
              <span className="text-green-500">
                {process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET ? '✓ Set' : '✗ Missing'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Messaging Sender ID:</span>
              <span className="text-green-500">
                {process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID ? '✓ Set' : '✗ Missing'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>App ID:</span>
              <span className="text-green-500">
                {process.env.NEXT_PUBLIC_FIREBASE_APP_ID ? '✓ Set' : '✗ Missing'}
              </span>
            </div>
            <div className="flex justify-between">
              <span>Measurement ID:</span>
              <span className="text-green-500">
                {process.env.NEXT_PUBLIC_FIREBASE_MEASUREMENT_ID ? '✓ Set' : '✗ Missing'}
              </span>
            </div>
          </div>
        </Card>

        {/* Authentication Status */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Authentication Status</h2>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <span>Current User:</span>
              <span className={user ? 'text-green-500' : 'text-gray-500'}>
                {user ? `Signed in as ${user.email}` : 'Not signed in'}
              </span>
            </div>
            {user && (
              <div className="text-sm space-y-2 bg-gray-50 dark:bg-gray-800 p-4 rounded">
                <div><strong>UID:</strong> {user.uid}</div>
                <div><strong>Email:</strong> {user.email}</div>
                <div><strong>Display Name:</strong> {user.displayName || 'Not set'}</div>
                <div><strong>Email Verified:</strong> {user.emailVerified ? 'Yes' : 'No'}</div>
              </div>
            )}
          </div>
        </Card>

        {/* Quick Actions */}
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Quick Actions</h2>
          <div className="space-y-4">
            <Button 
              onClick={() => window.location.href = '/dashboard'}
              className="w-full"
            >
              Test Protected Route (Dashboard)
            </Button>
            <Button 
              onClick={() => window.location.href = '/'}
              variant="outline"
              className="w-full"
            >
              Back to Home
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
} 
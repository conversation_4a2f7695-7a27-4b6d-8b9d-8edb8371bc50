'use client';

import React, { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { AlertTriangle, CheckCircle, XCircle, Loader2, Database, Trash2, RefreshCw, Cloud } from 'lucide-react';
import { useAuth } from '@/components/auth';
import { FirestoreStorageService } from '@/lib/services/storage/firestore-storage';
import { findCorruptDocuments, findUserDocuments, fixMissingTimestamps, clearUserData, CleanupResult } from '@/lib/utils/data/data-cleanup';

interface DebugResult {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
}

export default function DebugSyncPage() {
  const { user } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [results, setResults] = useState<DebugResult[]>([]);

  const addResult = (result: DebugResult) => {
    setResults(prev => [result, ...prev]);
  };

  const clearResults = () => {
    setResults([]);
  };

  const handleFindCorruptDocuments = async (deleteCorrupt: boolean = false) => {
    setIsLoading(true);
    try {
      const result = await findCorruptDocuments(deleteCorrupt);
      addResult({
        success: true,
        message: `Scan complete: ${result.totalDocuments} total, ${result.deletedDocuments} deleted, ${result.errors.length} errors`,
        data: result
      });
    } catch (error) {
      addResult({
        success: false,
        message: 'Failed to scan documents',
        error: error instanceof Error ? error.message : String(error)
      });
    }
    setIsLoading(false);
  };

  const handleFindUserDocuments = async () => {
    if (!user) {
      addResult({
        success: false,
        message: 'No user logged in',
        error: 'Authentication required'
      });
      return;
    }

    setIsLoading(true);
    try {
      const result = await findUserDocuments(user.uid);
      addResult({
        success: true,
        message: `Found ${result.documents.length} documents for user with ${result.issues.length} issues`,
        data: result
      });
    } catch (error) {
      addResult({
        success: false,
        message: 'Failed to find user documents',
        error: error instanceof Error ? error.message : String(error)
      });
    }
    setIsLoading(false);
  };

  const handleFixMissingTimestamps = async () => {
    setIsLoading(true);
    try {
      const result = await fixMissingTimestamps();
      addResult({
        success: true,
        message: `Fixed timestamps: ${result.totalDocuments} total, ${result.updatedDocuments} updated, ${result.errors.length} errors`,
        data: result
      });
    } catch (error) {
      addResult({
        success: false,
        message: 'Failed to fix timestamps',
        error: error instanceof Error ? error.message : String(error)
      });
    }
    setIsLoading(false);
  };

  const handleClearUserData = async () => {
    if (!user) {
      addResult({
        success: false,
        message: 'No user logged in',
        error: 'Authentication required'
      });
      return;
    }

    if (!confirm(`WARNING: This will permanently delete ALL checklists for user ${user.email}. Are you sure?`)) {
      return;
    }

    setIsLoading(true);
    try {
      const result = await clearUserData(user.uid);
      addResult({
        success: true,
        message: `Cleared user data: ${result.deletedDocuments} documents deleted`,
        data: result
      });
    } catch (error) {
      addResult({
        success: false,
        message: 'Failed to clear user data',
        error: error instanceof Error ? error.message : String(error)
      });
    }
    setIsLoading(false);
  };

  const handleTestFirestoreConnection = async () => {
    setIsLoading(true);
    try {
      const networkStatus = FirestoreStorageService.getNetworkStatus();
      addResult({
        success: true,
        message: `Firestore connection test: ${networkStatus.isOnline ? 'Online' : 'Offline'}`,
        data: networkStatus
      });
    } catch (error) {
      addResult({
        success: false,
        message: 'Firestore connection test failed',
        error: error instanceof Error ? error.message : String(error)
      });
    }
    setIsLoading(false);
  };

  const handleRefreshFirestoreCache = async () => {
    if (!user) {
      addResult({
        success: false,
        message: 'No user logged in',
        error: 'Authentication required'
      });
      return;
    }

    setIsLoading(true);
    try {
      await FirestoreStorageService.refreshCache();
      addResult({
        success: true,
        message: 'Firestore cache refreshed successfully',
        data: { timestamp: new Date().toISOString() }
      });
    } catch (error) {
      addResult({
        success: false,
        message: 'Failed to refresh Firestore cache',
        error: error instanceof Error ? error.message : String(error)
      });
    }
    setIsLoading(false);
  };

  const handleGetStorageStatus = async () => {
    if (!user) {
      addResult({
        success: false,
        message: 'No user logged in',
        error: 'Authentication required'
      });
      return;
    }

    setIsLoading(true);
    try {
      const status = await FirestoreStorageService.getStorageStatus(user.uid);
      const settings = await FirestoreStorageService.getSettings(user.uid);
      addResult({
        success: true,
        message: 'Retrieved storage status and settings',
        data: { status, settings }
      });
    } catch (error) {
      addResult({
        success: false,
        message: 'Failed to get storage status',
        error: error instanceof Error ? error.message : String(error)
      });
    }
    setIsLoading(false);
  };

  return (
    <div className="container mx-auto px-4 py-8 max-w-6xl">
      <div className="space-y-8">
        <div className="text-center">
          <h1 className="text-3xl font-bold mb-4">Firestore Debug Tools</h1>
          <p className="text-muted-foreground">
            Debug and test Firestore operations. Use with caution!
          </p>
          {user && (
            <p className="text-sm text-muted-foreground mt-2">
              Logged in as: {user.email} ({user.uid})
            </p>
          )}
          <div className="flex items-center justify-center gap-2 mt-4">
            <Cloud className="h-4 w-4 text-green-600" />
            <span className="text-sm text-green-600">Firestore Persistence Active</span>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          {/* Firestore Operations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Cloud className="h-5 w-5" />
                Firestore Operations
              </CardTitle>
              <CardDescription>
                Test and debug Firestore functionality
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button
                onClick={handleTestFirestoreConnection}
                disabled={isLoading}
                className="w-full"
                variant="outline"
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Database className="h-4 w-4 mr-2" />}
                Test Firestore Connection
              </Button>
              
              <Button
                onClick={handleRefreshFirestoreCache}
                disabled={isLoading || !user}
                className="w-full"
                variant="outline"
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <RefreshCw className="h-4 w-4 mr-2" />}
                Refresh Firestore Cache
              </Button>
              
              <Button
                onClick={handleGetStorageStatus}
                disabled={isLoading || !user}
                className="w-full"
                variant="outline"
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <CheckCircle className="h-4 w-4 mr-2" />}
                Get Storage Status
              </Button>
            </CardContent>
          </Card>

          {/* Data Cleanup Operations */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
                <Database className="h-5 w-5" />
              Data Cleanup
            </CardTitle>
            <CardDescription>
                Find and fix data integrity issues
            </CardDescription>
          </CardHeader>
            <CardContent className="space-y-3">
              <Button
                onClick={() => handleFindCorruptDocuments(false)}
                disabled={isLoading}
                className="w-full"
                variant="outline"
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Database className="h-4 w-4 mr-2" />}
                Find Corrupt Documents
              </Button>

              <Button
                onClick={() => handleFindCorruptDocuments(true)}
                disabled={isLoading}
                className="w-full"
                variant="destructive"
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Trash2 className="h-4 w-4 mr-2" />}
                Find & Delete Corrupt
              </Button>
              
              <Button
                onClick={handleFindUserDocuments}
                disabled={isLoading || !user}
                className="w-full"
                variant="outline"
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Database className="h-4 w-4 mr-2" />}
                Find User Documents
              </Button>

              <Button
                onClick={handleFixMissingTimestamps}
                disabled={isLoading}
                className="w-full"
                variant="outline"
              >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <RefreshCw className="h-4 w-4 mr-2" />}
                Fix Missing Timestamps
              </Button>
              
            <Button
              onClick={handleClearUserData}
              disabled={isLoading || !user}
                className="w-full"
              variant="destructive"
            >
                {isLoading ? <Loader2 className="h-4 w-4 animate-spin mr-2" /> : <Trash2 className="h-4 w-4 mr-2" />}
              Clear All User Data
            </Button>
          </CardContent>
        </Card>
        </div>

        {/* Results Section */}
        <Card>
          <CardHeader className="flex flex-row items-center justify-between">
            <div>
              <CardTitle>Debug Results</CardTitle>
              <CardDescription>
                Recent debug operation results
              </CardDescription>
            </div>
            <Button
              onClick={clearResults}
              variant="outline"
              size="sm"
              disabled={results.length === 0}
            >
              Clear Results
            </Button>
          </CardHeader>
          <CardContent>
            {results.length === 0 ? (
              <p className="text-muted-foreground text-center py-8">
                No debug results yet. Run a debug operation above.
              </p>
            ) : (
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {results.map((result, index) => (
                  <div
                    key={index}
                    className={`p-4 rounded border ${
                      result.success 
                        ? 'border-green-200 bg-green-50 dark:border-green-800 dark:bg-green-950'
                        : 'border-red-200 bg-red-50 dark:border-red-800 dark:bg-red-950'
                    }`}
                  >
                    <div className="flex items-start gap-2">
                      {result.success ? (
                        <CheckCircle className="h-5 w-5 text-green-600 mt-0.5 flex-shrink-0" />
                      ) : (
                        <XCircle className="h-5 w-5 text-red-600 mt-0.5 flex-shrink-0" />
                      )}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium">{result.message}</p>
                        {result.error && (
                          <p className="text-xs text-red-600 mt-1">{result.error}</p>
                        )}
                        {result.data && (
                          <details className="mt-2">
                            <summary className="text-xs cursor-pointer text-muted-foreground">
                              View Details
                            </summary>
                            <Textarea
                              value={JSON.stringify(result.data, null, 2)}
                              readOnly
                              className="mt-2 text-xs font-mono h-32"
                            />
                          </details>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
} 
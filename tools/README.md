# Development Tools

This directory contains development utilities, testing components, and build scripts that are separate from the production codebase.

## Directory Structure

### `/dev`
Development and testing pages that were moved from the main app directory:
- `test-firebase.tsx` - Firebase integration testing interface
- `debug-sync.tsx` - Data synchronization debugging tools
- `charts-test.tsx` - Charts component testing page

### `/scripts`
Build and deployment automation scripts

### `/testing`
Test utilities, fixtures, and testing helpers

## Usage

These tools are excluded from production builds but can be used during development:

```bash
# To run development tools, you can create dedicated routes or standalone scripts
npm run dev
```

## Development Pages

The development pages can be temporarily added back to the app router during development if needed, or run as standalone components for testing purposes. 
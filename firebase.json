{"hosting": {"site": "auburn-engineering", "public": "out", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "/api/proxy-image**", "function": "proxyImage"}, {"source": "/api/proxy-pdf**", "function": "proxyPdf"}, {"source": "/api/admin/queue-export**", "function": "queueExportHttp"}], "headers": [{"source": "**/*.@(jpg|jpeg|gif|png|svg|webp|ico)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "**/*.@(css|js)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "**/*.html", "headers": [{"key": "Cache-Control", "value": "public, max-age=3600"}]}], "cleanUrls": true, "trailingSlash": false}, "firestore": {"rules": "firestore.rules"}, "storage": {"rules": "storage.rules"}, "functions": [{"source": "functions", "codebase": "default", "ignore": ["node_modules", ".git", "firebase-debug.log", "firebase-debug.*.log", "*.local"], "predeploy": ["npm --prefix \"$RESOURCE_DIR\" run build"]}]}
#!/usr/bin/env node

/**
 * Firestore Backup and Restore Script
 * 
 * This script provides comprehensive backup and restore functionality for the Auburn Engineering
 * Firestore database, including all collections and their associated Firebase Storage files.
 * 
 * Collections handled:
 * - checklists: Main checklist data with image references
 * - equipment-tags: Equipment tag data with QR codes
 * - users: User profile data
 * - settings: User settings
 * - app_config: System configuration
 * 
 * Usage:
 *   node scripts/firestore-backup-restore.js backup [--output-dir ./backups]
 *   node scripts/firestore-backup-restore.js restore [--backup-file ./backups/backup-YYYY-MM-DD.json]
 *   node scripts/firestore-backup-restore.js list-backups [--backup-dir ./backups]
 * 
 * Environment Variables Required:
 *   FIREBASE_PROJECT_ID - Firebase project ID
 *   GOOGLE_APPLICATION_CREDENTIALS - Path to service account key file
 */

const admin = require('firebase-admin');
const fs = require('fs').promises;
const path = require('path');
const { createWriteStream, createReadStream } = require('fs');
const { pipeline } = require('stream/promises');

// Initialize Firebase Admin SDK with service account
if (!admin.apps.length) {
  try {
    // Use the service account file directly
    const serviceAccount = require('./serviceAccounts.json');
    const projectId = serviceAccount.project_id;
    
    admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
      projectId: projectId,
      storageBucket: `${projectId}.firebasestorage.app`
    });
    console.log(`✅ Firebase Admin SDK initialized for project: ${projectId}`);
  } catch (error) {
    console.error('❌ Failed to initialize Firebase Admin SDK:', error.message);
    console.error('   Make sure serviceAccounts.json exists in the scripts directory');
    process.exit(1);
  }
}

const db = admin.firestore();
const storage = admin.storage();

// Collection definitions based on codebase analysis
const COLLECTIONS = {
  checklists: {
    name: 'checklists',
    hasImages: true,
    imageFields: ['beforeImage', 'afterImage', 'inspectorSignature', 'clientSignature'],
    timestampFields: ['createdAt', 'updatedAt', 'completedAt'],
    description: 'Checklist data with associated images'
  },
  equipmentTags: {
    name: 'equipment-tags',
    hasImages: false,
    timestampFields: ['createdAt', 'updatedAt'],
    description: 'Equipment tags with QR codes'
  },
  users: {
    name: 'users',
    hasImages: false,
    timestampFields: ['createdAt', 'updatedAt', 'metadata.lastLoginAt'],
    description: 'User profile data'
  },
  settings: {
    name: 'settings',
    hasImages: false,
    timestampFields: ['createdAt', 'updatedAt'],
    description: 'User settings and preferences'
  },
  appConfig: {
    name: 'app_config',
    hasImages: false,
    timestampFields: ['createdAt', 'updatedAt'],
    description: 'System configuration and maintenance settings'
  }
};

/**
 * Utility function to safely convert Firestore timestamps
 */
function convertTimestamp(timestamp) {
  if (!timestamp) return null;
  
  if (timestamp._seconds !== undefined) {
    return new Date(timestamp._seconds * 1000).toISOString();
  }
  
  if (timestamp.seconds !== undefined) {
    return new Date(timestamp.seconds * 1000).toISOString();
  }
  
  if (timestamp.toDate && typeof timestamp.toDate === 'function') {
    return timestamp.toDate().toISOString();
  }
  
  if (typeof timestamp === 'string') {
    return timestamp;
  }
  
  return timestamp;
}

/**
 * Process document data to handle timestamps and prepare for backup
 */
function processDocumentForBackup(data, collectionConfig) {
  const processed = { ...data };
  
  // Convert timestamps
  if (collectionConfig.timestampFields) {
    collectionConfig.timestampFields.forEach(field => {
      if (field.includes('.')) {
        // Handle nested fields like 'metadata.lastLoginAt'
        const parts = field.split('.');
        let current = processed;
        for (let i = 0; i < parts.length - 1; i++) {
          if (current[parts[i]]) {
            current = current[parts[i]];
          } else {
            return;
          }
        }
        const lastPart = parts[parts.length - 1];
        if (current[lastPart]) {
          current[lastPart] = convertTimestamp(current[lastPart]);
        }
      } else {
        if (processed[field]) {
          processed[field] = convertTimestamp(processed[field]);
        }
      }
    });
  }
  
  return processed;
}

/**
 * Download a file from Firebase Storage
 */
async function downloadStorageFile(storagePath, localPath) {
  try {
    const file = storage.bucket().file(storagePath);
    const [exists] = await file.exists();
    
    if (!exists) {
      console.warn(`⚠️  Storage file not found: ${storagePath}`);
      return false;
    }
    
    await pipeline(
      file.createReadStream(),
      createWriteStream(localPath)
    );
    
    return true;
  } catch (error) {
    console.error(`❌ Failed to download ${storagePath}:`, error.message);
    return false;
  }
}

/**
 * Upload a file to Firebase Storage
 */
async function uploadStorageFile(localPath, storagePath) {
  try {
    const file = storage.bucket().file(storagePath);
    
    await pipeline(
      createReadStream(localPath),
      file.createWriteStream({
        metadata: {
          contentType: getContentType(localPath)
        }
      })
    );
    
    return true;
  } catch (error) {
    console.error(`❌ Failed to upload ${localPath} to ${storagePath}:`, error.message);
    return false;
  }
}

/**
 * Get content type based on file extension
 */
function getContentType(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  const contentTypes = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.pdf': 'application/pdf',
    '.json': 'application/json'
  };
  return contentTypes[ext] || 'application/octet-stream';
}

/**
 * Extract storage path from Firebase Storage URL
 */
function extractStoragePath(url) {
  if (!url || typeof url !== 'string') return null;
  
  // Handle Firebase Storage URLs
  const patterns = [
    /\/o\/(.+?)\?/,  // Standard Firebase Storage URL
    /\/b\/[^\/]+\/o\/(.+?)(\?|$)/,  // Alternative format
  ];
  
  for (const pattern of patterns) {
    const match = url.match(pattern);
    if (match) {
      return decodeURIComponent(match[1]);
    }
  }
  
  return null;
}

/**
 * Show progress bar
 */
function showProgress(current, total, label = '') {
  const percentage = Math.round((current / total) * 100);
  const barLength = 30;
  const filledLength = Math.round((barLength * current) / total);
  const bar = '█'.repeat(filledLength) + '░'.repeat(barLength - filledLength);
  process.stdout.write(`\r   ${label} [${bar}] ${percentage}% (${current}/${total})`);
  if (current === total) {
    console.log(''); // New line when complete
  }
}

/**
 * Backup a single collection
 */
async function backupCollection(collectionName, collectionConfig, backupDir) {
  console.log(`📦 Backing up collection: ${collectionName}`);
  
  try {
    // Get collection snapshot
    process.stdout.write(`   📊 Fetching documents...`);
    const snapshot = await db.collection(collectionName).get();
    console.log(` Found ${snapshot.docs.length} documents`);
    
    const documents = [];
    const storageFiles = new Set();
    
    // Process documents with progress
    for (let i = 0; i < snapshot.docs.length; i++) {
      const doc = snapshot.docs[i];
      showProgress(i + 1, snapshot.docs.length, 'Processing docs');
      
      const data = processDocumentForBackup(doc.data(), collectionConfig);
      const docData = {
        id: doc.id,
        data: data
      };
      
      // Collect storage file paths if collection has images
      if (collectionConfig.hasImages && collectionConfig.imageFields) {
        collectionConfig.imageFields.forEach(field => {
          const url = data[field];
          if (url) {
            const storagePath = extractStoragePath(url);
            if (storagePath) {
              storageFiles.add(storagePath);
            }
          }
        });
      }
      
      documents.push(docData);
    }
    
    // Save collection data
    process.stdout.write(`   💾 Saving collection data...`);
    const collectionFile = path.join(backupDir, `${collectionName}.json`);
    await fs.writeFile(collectionFile, JSON.stringify(documents, null, 2));
    const fileSize = (await fs.stat(collectionFile)).size;
    console.log(` ${(fileSize / 1024 / 1024).toFixed(2)}MB saved`);
    
    console.log(`✅ Backed up ${documents.length} documents from ${collectionName}`);
    
    // Download storage files if any
    if (storageFiles.size > 0) {
      console.log(`   📁 Downloading ${storageFiles.size} storage files...`);
      const storageDir = path.join(backupDir, 'storage', collectionName);
      await fs.mkdir(storageDir, { recursive: true });
      
      let downloadedCount = 0;
      let skippedCount = 0;
      const storageArray = Array.from(storageFiles);
      
      for (let i = 0; i < storageArray.length; i++) {
        const storagePath = storageArray[i];
        showProgress(i + 1, storageArray.length, 'Downloading files');
        
        const fileName = path.basename(storagePath);
        const localPath = path.join(storageDir, fileName);
        
        if (await downloadStorageFile(storagePath, localPath)) {
          downloadedCount++;
        } else {
          skippedCount++;
        }
      }
      
      console.log(`✅ Downloaded ${downloadedCount}/${storageFiles.size} storage files (${skippedCount} skipped)`);
    }
    
    return {
      collection: collectionName,
      documents: documents.length,
      storageFiles: storageFiles.size
    };
    
  } catch (error) {
    console.error(`❌ Failed to backup collection ${collectionName}:`, error.message);
    throw error;
  }
}

/**
 * Restore a single collection
 */
async function restoreCollection(collectionName, collectionConfig, backupDir, options = {}) {
  console.log(`📥 Restoring collection: ${collectionName}`);
  
  try {
    const collectionFile = path.join(backupDir, `${collectionName}.json`);
    
    try {
      await fs.access(collectionFile);
    } catch {
      console.log(`⚠️  No backup file found for collection: ${collectionName}`);
      return { collection: collectionName, documents: 0, storageFiles: 0 };
    }
    
    const documentsData = JSON.parse(await fs.readFile(collectionFile, 'utf8'));
    
    // Clear existing collection if requested
    if (options.clearExisting) {
      console.log(`🗑️  Clearing existing documents in ${collectionName}...`);
      const existingSnapshot = await db.collection(collectionName).get();
      const batch = db.batch();
      
      existingSnapshot.docs.forEach(doc => {
        batch.delete(doc.ref);
      });
      
      if (existingSnapshot.docs.length > 0) {
        await batch.commit();
        console.log(`✅ Cleared ${existingSnapshot.docs.length} existing documents`);
      }
    }
    
    // Restore documents in batches
    const batchSize = 500;
    let restoredCount = 0;
    
    for (let i = 0; i < documentsData.length; i += batchSize) {
      const batch = db.batch();
      const batchDocs = documentsData.slice(i, i + batchSize);
      
      batchDocs.forEach(({ id, data }) => {
        const docRef = db.collection(collectionName).doc(id);
        
        // Convert timestamp strings back to Firestore timestamps for restore
        const processedData = { ...data };
        if (collectionConfig.timestampFields) {
          collectionConfig.timestampFields.forEach(field => {
            if (field.includes('.')) {
              const parts = field.split('.');
              let current = processedData;
              for (let j = 0; j < parts.length - 1; j++) {
                if (current[parts[j]]) {
                  current = current[parts[j]];
                } else {
                  return;
                }
              }
              const lastPart = parts[parts.length - 1];
              if (current[lastPart] && typeof current[lastPart] === 'string') {
                current[lastPart] = admin.firestore.Timestamp.fromDate(new Date(current[lastPart]));
              }
            } else {
              if (processedData[field] && typeof processedData[field] === 'string') {
                processedData[field] = admin.firestore.Timestamp.fromDate(new Date(processedData[field]));
              }
            }
          });
        }
        
        batch.set(docRef, processedData);
      });
      
      await batch.commit();
      restoredCount += batchDocs.length;
      console.log(`✅ Restored ${restoredCount}/${documentsData.length} documents`);
    }
    
    // Restore storage files if any
    let uploadedCount = 0;
    if (collectionConfig.hasImages) {
      const storageDir = path.join(backupDir, 'storage', collectionName);
      
      try {
        const storageFiles = await fs.readdir(storageDir);
        
        for (const fileName of storageFiles) {
          const localPath = path.join(storageDir, fileName);
          const storagePath = `${collectionName}/${fileName}`;
          
          if (await uploadStorageFile(localPath, storagePath)) {
            uploadedCount++;
          }
        }
        
        console.log(`✅ Uploaded ${uploadedCount}/${storageFiles.length} storage files for ${collectionName}`);
      } catch (error) {
        console.log(`⚠️  No storage files found for collection: ${collectionName}`);
      }
    }
    
    return {
      collection: collectionName,
      documents: restoredCount,
      storageFiles: uploadedCount
    };
    
  } catch (error) {
    console.error(`❌ Failed to restore collection ${collectionName}:`, error.message);
    throw error;
  }
}

/**
 * Create a complete backup
 */
async function createBackup(outputDir = './backups') {
  const timestamp = new Date().toISOString().split('T')[0];
  const backupDir = path.join(outputDir, `backup-${timestamp}-${Date.now()}`);
  
  console.log(`🚀 Starting backup to: ${backupDir}`);
  
  try {
    await fs.mkdir(backupDir, { recursive: true });
    
    const results = [];
    const serviceAccount = require('./serviceAccounts.json');
    const metadata = {
      timestamp: new Date().toISOString(),
      projectId: serviceAccount.project_id,
      collections: {},
      version: '1.0.0'
    };
    
    // Backup each collection
    for (const [key, config] of Object.entries(COLLECTIONS)) {
      try {
        const result = await backupCollection(config.name, config, backupDir);
        results.push(result);
        metadata.collections[config.name] = {
          documents: result.documents,
          storageFiles: result.storageFiles,
          description: config.description
        };
      } catch (error) {
        console.error(`❌ Failed to backup ${config.name}:`, error.message);
        metadata.collections[config.name] = {
          error: error.message,
          description: config.description
        };
      }
    }
    
    // Save metadata
    await fs.writeFile(
      path.join(backupDir, 'metadata.json'),
      JSON.stringify(metadata, null, 2)
    );
    
    // Create summary
    const totalDocs = results.reduce((sum, r) => sum + r.documents, 0);
    const totalFiles = results.reduce((sum, r) => sum + r.storageFiles, 0);
    
    console.log('\n📊 Backup Summary:');
    console.log(`   📁 Backup directory: ${backupDir}`);
    console.log(`   📄 Total documents: ${totalDocs}`);
    console.log(`   🖼️  Total storage files: ${totalFiles}`);
    console.log(`   ⏰ Completed at: ${new Date().toISOString()}`);
    
    results.forEach(result => {
      console.log(`   • ${result.collection}: ${result.documents} docs, ${result.storageFiles} files`);
    });
    
    return backupDir;
    
  } catch (error) {
    console.error('❌ Backup failed:', error.message);
    throw error;
  }
}

/**
 * Restore from backup
 */
async function restoreBackup(backupPath, options = {}) {
  console.log(`🔄 Starting restore from: ${backupPath}`);
  
  try {
    // Verify backup directory exists
    await fs.access(backupPath);
    
    // Read metadata
    const metadataPath = path.join(backupPath, 'metadata.json');
    let metadata = {};
    
    try {
      metadata = JSON.parse(await fs.readFile(metadataPath, 'utf8'));
      console.log(`📋 Backup metadata:`);
      console.log(`   🕐 Created: ${metadata.timestamp}`);
      console.log(`   🏗️  Project: ${metadata.projectId}`);
      console.log(`   📦 Version: ${metadata.version || 'unknown'}`);
    } catch {
      console.log('⚠️  No metadata file found, proceeding with restore...');
    }
    
    // Confirm restore if not in force mode
    if (!options.force) {
      console.log('\n⚠️  WARNING: This will overwrite existing data in Firestore!');
      console.log('   Use --force flag to skip this confirmation.');
      
      // In a real implementation, you'd want to add readline for user confirmation
      // For now, we'll require the --force flag
      throw new Error('Restore cancelled. Use --force flag to proceed.');
    }
    
    const results = [];
    
    // Restore each collection
    for (const [key, config] of Object.entries(COLLECTIONS)) {
      try {
        const result = await restoreCollection(config.name, config, backupPath, {
          clearExisting: options.clearExisting
        });
        results.push(result);
      } catch (error) {
        console.error(`❌ Failed to restore ${config.name}:`, error.message);
        results.push({
          collection: config.name,
          documents: 0,
          storageFiles: 0,
          error: error.message
        });
      }
    }
    
    // Create summary
    const totalDocs = results.reduce((sum, r) => sum + (r.documents || 0), 0);
    const totalFiles = results.reduce((sum, r) => sum + (r.storageFiles || 0), 0);
    
    console.log('\n📊 Restore Summary:');
    console.log(`   📄 Total documents restored: ${totalDocs}`);
    console.log(`   🖼️  Total storage files restored: ${totalFiles}`);
    console.log(`   ⏰ Completed at: ${new Date().toISOString()}`);
    
    results.forEach(result => {
      if (result.error) {
        console.log(`   ❌ ${result.collection}: ERROR - ${result.error}`);
      } else {
        console.log(`   ✅ ${result.collection}: ${result.documents} docs, ${result.storageFiles} files`);
      }
    });
    
  } catch (error) {
    console.error('❌ Restore failed:', error.message);
    throw error;
  }
}

/**
 * List available backups
 */
async function listBackups(backupDir = './backups') {
  try {
    const entries = await fs.readdir(backupDir, { withFileTypes: true });
    const backups = entries
      .filter(entry => entry.isDirectory() && entry.name.startsWith('backup-'))
      .sort((a, b) => b.name.localeCompare(a.name));
    
    if (backups.length === 0) {
      console.log('📭 No backups found in:', backupDir);
      return;
    }
    
    console.log(`📋 Available backups in ${backupDir}:`);
    
    for (const backup of backups) {
      const backupPath = path.join(backupDir, backup.name);
      const metadataPath = path.join(backupPath, 'metadata.json');
      
      try {
        const metadata = JSON.parse(await fs.readFile(metadataPath, 'utf8'));
        const totalDocs = Object.values(metadata.collections || {})
          .reduce((sum, col) => sum + (col.documents || 0), 0);
        
        console.log(`   📦 ${backup.name}`);
        console.log(`      🕐 Created: ${metadata.timestamp}`);
        console.log(`      📄 Documents: ${totalDocs}`);
        console.log(`      🏗️  Project: ${metadata.projectId}`);
      } catch {
        console.log(`   📦 ${backup.name} (no metadata)`);
      }
    }
    
  } catch (error) {
    console.error('❌ Failed to list backups:', error.message);
  }
}

/**
 * Main CLI handler
 */
async function main() {
  const args = process.argv.slice(2);
  const command = args[0];
  
  // Parse command line arguments
  const options = {};
  for (let i = 1; i < args.length; i += 2) {
    const key = args[i]?.replace(/^--/, '');
    const value = args[i + 1];
    if (key) {
      options[key] = value || true;
    }
  }
  
  try {
    switch (command) {
      case 'backup':
        const outputDir = options['output-dir'] || './backups';
        await createBackup(outputDir);
        break;
        
      case 'restore':
        const backupFile = options['backup-file'];
        if (!backupFile) {
          console.error('❌ --backup-file is required for restore command');
          process.exit(1);
        }
        await restoreBackup(backupFile, {
          force: options.force,
          clearExisting: options['clear-existing']
        });
        break;
        
      case 'list-backups':
        const backupDir = options['backup-dir'] || './backups';
        await listBackups(backupDir);
        break;
        
      default:
        console.log('🔧 Auburn Engineering Firestore Backup & Restore Tool');
        console.log('');
        console.log('Usage:');
        console.log('  node firestore-backup-restore.js backup [--output-dir ./backups]');
        console.log('  node firestore-backup-restore.js restore --backup-file ./backups/backup-YYYY-MM-DD [--force] [--clear-existing]');
        console.log('  node firestore-backup-restore.js list-backups [--backup-dir ./backups]');
        console.log('');
        console.log('Requirements:');
        console.log('  • serviceAccounts.json file must be in the same directory');
        console.log('  • Node.js with firebase-admin package installed');
        console.log('');
        console.log('Collections backed up:');
        Object.values(COLLECTIONS).forEach(config => {
          console.log(`  • ${config.name}: ${config.description}`);
        });
        console.log('');
        console.log('Examples:');
        console.log('  node firestore-backup-restore.js backup');
        console.log('  node firestore-backup-restore.js list-backups');
        console.log('  node firestore-backup-restore.js restore --backup-file ./backups/backup-2024-01-15-123456 --force');
        break;
    }
  } catch (error) {
    console.error('❌ Command failed:', error.message);
    process.exit(1);
  }
}

// Run the CLI
if (require.main === module) {
  main().catch(error => {
    console.error('❌ Unexpected error:', error);
    process.exit(1);
  });
}

module.exports = {
  createBackup,
  restoreBackup,
  listBackups,
  COLLECTIONS
}; 
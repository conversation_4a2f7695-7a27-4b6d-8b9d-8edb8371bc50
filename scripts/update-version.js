#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

const versionFilePath = path.join(__dirname, '../src/config/version.ts');

function updateVersion(type) {
  try {
    // Read current version file
    const versionContent = fs.readFileSync(versionFilePath, 'utf8');
    
    // Extract current version numbers using regex
    const majorMatch = versionContent.match(/major:\s*(\d+)/);
    const minorMatch = versionContent.match(/minor:\s*(\d+)/);
    const patchMatch = versionContent.match(/patch:\s*(\d+)/);
    
    if (!majorMatch || !minorMatch || !patchMatch) {
      throw new Error('Could not parse current version numbers');
    }
    
    let major = parseInt(majorMatch[1]);
    let minor = parseInt(minorMatch[1]);
    let patch = parseInt(patchMatch[1]);
    
    // Update version based on type
    switch (type) {
      case 'major':
        major++;
        minor = 0;
        patch = 0;
        break;
      case 'minor':
        minor++;
        patch = 0;
        break;
      case 'patch':
        patch++;
        break;
      default:
        throw new Error('Invalid version type. Use: major, minor, or patch');
    }
    
    // Get current timestamp and build info
    const timestamp = new Date().toISOString();
    const buildNumber = process.env.BUILD_NUMBER || 'dev';
    const commitHash = process.env.COMMIT_HASH || 'local';
    
    // Create new version content
    const newVersionContent = `export interface AppVersion {
  major: number;
  minor: number;
  patch: number;
  build?: string;
  timestamp: string;
  commitHash?: string;
}

export interface VersionInfo {
  current: AppVersion;
  displayName: string;
  releaseDate: string;
  releaseNotes?: string[];
}

// Current application version
export const APP_VERSION: AppVersion = {
  major: ${major},
  minor: ${minor},
  patch: ${patch},
  build: process.env.NEXT_PUBLIC_BUILD_NUMBER || '${buildNumber}',
  timestamp: '${timestamp}',
  commitHash: process.env.NEXT_PUBLIC_COMMIT_HASH || '${commitHash}'
};

// Version utilities
export class VersionUtils {
  static formatVersion(version: AppVersion): string {
    return \`v\${version.major}.\${version.minor}.\${version.patch}\`;
  }

  static formatFullVersion(version: AppVersion): string {
    const base = this.formatVersion(version);
    if (version.build && version.build !== 'dev') {
      return \`\${base}-\${version.build}\`;
    }
    return base;
  }

  static compareVersions(v1: AppVersion, v2: AppVersion): number {
    if (v1.major !== v2.major) return v1.major - v2.major;
    if (v1.minor !== v2.minor) return v1.minor - v2.minor;
    if (v1.patch !== v2.patch) return v1.patch - v2.patch;
    return 0;
  }

  static isNewerVersion(current: AppVersion, latest: AppVersion): boolean {
    return this.compareVersions(latest, current) > 0;
  }

  static formatBuildInfo(version: AppVersion): string {
    const date = new Date(version.timestamp).toLocaleDateString();
    const time = new Date(version.timestamp).toLocaleTimeString();
    return \`Built on \${date} at \${time}\`;
  }

  static getShortCommitHash(version: AppVersion): string {
    if (!version.commitHash || version.commitHash === 'local') return 'local';
    return version.commitHash.substring(0, 7);
  }
}

// Current version info
export const CURRENT_VERSION_INFO: VersionInfo = {
  current: APP_VERSION,
  displayName: VersionUtils.formatFullVersion(APP_VERSION),
  releaseDate: '${timestamp}',
  releaseNotes: [
    'Version ${major}.${minor}.${patch} release',
    'Updated system components',
    'Performance improvements'
  ]
};`;
    
    // Write updated version file
    fs.writeFileSync(versionFilePath, newVersionContent);
    
    console.log(`✅ Version updated to v${major}.${minor}.${patch}`);
    console.log(`📅 Build timestamp: ${timestamp}`);
    console.log(`🔨 Build number: ${buildNumber}`);
    console.log(`📝 Commit hash: ${commitHash}`);
    
    // Also update package.json version
    const packageJsonPath = path.join(__dirname, '../package.json');
    const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
    packageJson.version = `${major}.${minor}.${patch}`;
    fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
    
    console.log(`📦 Package.json version updated to ${major}.${minor}.${patch}`);
    
  } catch (error) {
    console.error('❌ Error updating version:', error.message);
    process.exit(1);
  }
}

// Get version type from command line arguments
const versionType = process.argv[2];

if (!versionType) {
  console.error('❌ Please specify version type: major, minor, or patch');
  console.log('Usage: npm run version:patch | npm run version:minor | npm run version:major');
  process.exit(1);
}

updateVersion(versionType); 